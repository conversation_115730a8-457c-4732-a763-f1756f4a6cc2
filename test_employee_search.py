#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة البحث في نظام إدارة الموظفين
Test Employee Search Feature
"""

import sys
import datetime
import os

def test_employee_search():
    """اختبار ميزة البحث في نظام إدارة الموظفين"""
    print("🔍 اختبار ميزة البحث في نظام إدارة الموظفين")
    print("=" * 70)
    
    test_results = {
        'total_tests': 0,
        'passed_tests': 0,
        'failed_tests': 0
    }
    
    # اختبار 1: فحص وجود دوال البحث
    print("\n🧪 اختبار 1: فحص وجود دوال البحث")
    print("-" * 50)
    
    try:
        with open("employee_management.py", "r", encoding="utf-8") as f:
            code = f.read()
        
        search_functions = [
            "def perform_search",
            "def apply_filters", 
            "def reset_filters",
            "def on_search_change",
            "def on_filter_change"
        ]
        
        for func in search_functions:
            if func in code:
                print(f"✅ {func}: موجودة")
                test_results['passed_tests'] += 1
            else:
                print(f"❌ {func}: مفقودة")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
    
    except Exception as e:
        print(f"❌ خطأ في فحص الدوال: {e}")
        test_results['failed_tests'] += 5
        test_results['total_tests'] += 5
    
    # اختبار 2: فحص متغيرات البحث
    print("\n🧪 اختبار 2: فحص متغيرات البحث")
    print("-" * 50)
    
    try:
        search_variables = [
            "self.search_var",
            "self.filter_workplace_var",
            "self.filter_qualification_var", 
            "self.filter_nationality_var",
            "self.filtered_data"
        ]
        
        for var in search_variables:
            if var in code:
                print(f"✅ {var}: موجود")
                test_results['passed_tests'] += 1
            else:
                print(f"❌ {var}: مفقود")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
    
    except Exception as e:
        print(f"❌ خطأ في فحص المتغيرات: {e}")
        test_results['failed_tests'] += 5
        test_results['total_tests'] += 5
    
    # اختبار 3: فحص حقول البحث
    print("\n🧪 اختبار 3: فحص حقول البحث")
    print("-" * 50)
    
    try:
        search_fields_basic = [
            '"الرقم الوظيفي"',
            '"الاسم العربي"',
            '"الاسم الإنجليزي"',
            '"المسمى الوظيفي"',
            '"الرقم الوطني"'
        ]
        
        search_fields_additional = [
            '"المؤهل"',
            '"مكان العمل الحالي"',
            '"التخصص"',
            '"الجنسية"'
        ]
        
        print("📋 الحقول الأساسية للبحث:")
        for field in search_fields_basic:
            if field in code:
                print(f"   ✅ {field}: متاح للبحث")
                test_results['passed_tests'] += 1
            else:
                print(f"   ❌ {field}: غير متاح للبحث")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
        
        print("\n📋 الحقول الإضافية للبحث:")
        for field in search_fields_additional:
            if field in code:
                print(f"   ✅ {field}: متاح للبحث")
                test_results['passed_tests'] += 1
            else:
                print(f"   ❌ {field}: غير متاح للبحث")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
    
    except Exception as e:
        print(f"❌ خطأ في فحص حقول البحث: {e}")
        test_results['failed_tests'] += 9
        test_results['total_tests'] += 9
    
    # اختبار 4: فحص واجهة البحث
    print("\n🧪 اختبار 4: فحص واجهة البحث")
    print("-" * 50)
    
    try:
        ui_elements = [
            "search_entry",
            "🔍 بحث",
            "🔄 إعادة تعيين",
            "create_search_filter_area",
            "textvariable=self.search_var"
        ]
        
        for element in ui_elements:
            if element in code:
                print(f"✅ {element}: موجود في الواجهة")
                test_results['passed_tests'] += 1
            else:
                print(f"❌ {element}: مفقود من الواجهة")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
    
    except Exception as e:
        print(f"❌ خطأ في فحص واجهة البحث: {e}")
        test_results['failed_tests'] += 5
        test_results['total_tests'] += 5
    
    # اختبار 5: فحص ربط الأحداث
    print("\n🧪 اختبار 5: فحص ربط الأحداث")
    print("-" * 50)
    
    try:
        event_bindings = [
            "self.search_var.trace",
            "self.on_search_change",
            "self.on_filter_change",
            "command=self.perform_search",
            "command=self.reset_filters"
        ]
        
        for binding in event_bindings:
            if binding in code:
                print(f"✅ {binding}: مربوط بشكل صحيح")
                test_results['passed_tests'] += 1
            else:
                print(f"❌ {binding}: غير مربوط")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
    
    except Exception as e:
        print(f"❌ خطأ في فحص ربط الأحداث: {e}")
        test_results['failed_tests'] += 5
        test_results['total_tests'] += 5
    
    # اختبار 6: فحص منطق البحث المحسن
    print("\n🧪 اختبار 6: فحص منطق البحث المحسن")
    print("-" * 50)
    
    try:
        enhanced_logic = [
            "search_term.lower()",
            "value.lower()",
            "if not search_term:",
            "additional_fields",
            "لم يتم العثور على نتائج",
            "تم العثور على"
        ]
        
        for logic in enhanced_logic:
            if logic in code:
                print(f"✅ {logic}: منطق محسن موجود")
                test_results['passed_tests'] += 1
            else:
                print(f"❌ {logic}: منطق محسن مفقود")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
    
    except Exception as e:
        print(f"❌ خطأ في فحص المنطق المحسن: {e}")
        test_results['failed_tests'] += 6
        test_results['total_tests'] += 6
    
    # اختبار 7: فحص التصفية المتقدمة
    print("\n🧪 اختبار 7: فحص التصفية المتقدمة")
    print("-" * 50)
    
    try:
        filter_logic = [
            "workplace_filter",
            "qualification_filter", 
            "nationality_filter",
            'workplace_filter != "الكل"',
            "has_filters",
            "any(["
        ]
        
        for logic in filter_logic:
            if logic in code:
                print(f"✅ {logic}: منطق تصفية موجود")
                test_results['passed_tests'] += 1
            else:
                print(f"❌ {logic}: منطق تصفية مفقود")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
    
    except Exception as e:
        print(f"❌ خطأ في فحص التصفية: {e}")
        test_results['failed_tests'] += 6
        test_results['total_tests'] += 6
    
    # اختبار 8: فحص تحديث الجدول
    print("\n🧪 اختبار 8: فحص تحديث الجدول")
    print("-" * 50)
    
    try:
        table_update = [
            "self.update_employees_table()",
            "data_to_show = self.filtered_data if self.filtered_data else self.employees_data",
            "self.results_label.config",
            "عدد النتائج:",
            "len(data_to_show)"
        ]
        
        for update in table_update:
            if update in code:
                print(f"✅ {update}: تحديث الجدول موجود")
                test_results['passed_tests'] += 1
            else:
                print(f"❌ {update}: تحديث الجدول مفقود")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
    
    except Exception as e:
        print(f"❌ خطأ في فحص تحديث الجدول: {e}")
        test_results['failed_tests'] += 5
        test_results['total_tests'] += 5
    
    return test_results

def main():
    """تشغيل جميع الاختبارات"""
    print("🔍 اختبار ميزة البحث في نظام إدارة الموظفين")
    print(f"📅 التاريخ: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 إصدار Python: {sys.version}")
    print()
    
    # تشغيل الاختبارات
    results = test_employee_search()
    
    # عرض النتائج النهائية
    print("\n" + "=" * 70)
    print("📊 ملخص نتائج اختبار البحث:")
    print(f"   📈 إجمالي الاختبارات: {results['total_tests']}")
    print(f"   ✅ نجح: {results['passed_tests']}")
    print(f"   ❌ فشل: {results['failed_tests']}")
    
    success_rate = (results['passed_tests'] / results['total_tests']) * 100 if results['total_tests'] > 0 else 0
    print(f"   📊 معدل النجاح: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("\n🎉 ممتاز! ميزة البحث تعمل بشكل مثالي")
    elif success_rate >= 75:
        print("\n✅ جيد! ميزة البحث تعمل مع بعض التحسينات المطلوبة")
    elif success_rate >= 50:
        print("\n⚠️ متوسط! ميزة البحث تحتاج إلى تحسينات إضافية")
    else:
        print("\n❌ ضعيف! ميزة البحث تحتاج إلى مراجعة شاملة")
    
    print("=" * 70)
    
    # عرض تعليمات الاستخدام
    print("\n📋 كيفية استخدام البحث:")
    print("1. اكتب نص البحث في حقل البحث")
    print("2. سيتم البحث تلقائياً في الحقول التالية:")
    print("   • الرقم الوظيفي")
    print("   • الاسم العربي")
    print("   • الاسم الإنجليزي")
    print("   • المسمى الوظيفي")
    print("   • الرقم الوطني")
    print("   • المؤهل")
    print("   • مكان العمل الحالي")
    print("   • التخصص")
    print("   • الجنسية")
    print("3. استخدم المرشحات الإضافية للتصفية حسب:")
    print("   • مكان العمل")
    print("   • المؤهل")
    print("   • الجنسية")
    print("4. اضغط 'إعادة تعيين' لمسح جميع المرشحات")
    
    print("\n🔧 الإصلاحات المطبقة:")
    print("✅ إصلاح مشكلة عدم عرض النتائج")
    print("✅ تحسين منطق التصفية")
    print("✅ إضافة حقول بحث إضافية")
    print("✅ تحسين رسائل الحالة")
    print("✅ إصلاح دالة إعادة التعيين")
    
    print("\n🏁 انتهى اختبار ميزة البحث")

if __name__ == "__main__":
    main()
