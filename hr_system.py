#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
منظومة إدارة الموارد البشرية - ملف تشغيل موحد
جميع الوحدات في ملف واحد لضمان عدم وجود مشاكل في الاستيراد
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import json
import sys

def get_resource_path(relative_path):
    """الحصول على المسار الصحيح للملفات"""
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

def is_running_as_executable():
    """فحص ما إذا كان التطبيق يعمل كملف تنفيذي"""
    return getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')
import subprocess
import importlib

# إخفاء نافذة الكونسول في Windows (فقط إذا لم يكن هناك أخطاء)
def hide_console():
    """إخفاء نافذة الكونسول في Windows"""
    if sys.platform == "win32":
        try:
            import ctypes
            ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(), 0)
        except:
            pass

def show_console():
    """إظهار نافذة الكونسول في Windows"""
    if sys.platform == "win32":
        try:
            import ctypes
            ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(), 1)
        except:
            pass

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_and_install_requirements():
    """فحص وتثبيت المتطلبات اللازمة"""
    required_packages = [
        ("openpyxl", "openpyxl"),
        ("tkcalendar", "tkcalendar"),
        ("pandas", "pandas"),
        ("docx", "python-docx"),
        ("matplotlib", "matplotlib")
    ]

    missing_packages = []
    for import_name, package_name in required_packages:
        try:
            importlib.import_module(import_name)
        except ImportError:
            missing_packages.append(package_name)

    if missing_packages:
        response = messagebox.askyesno(
            "مكتبات مفقودة",
            f"المكتبات التالية مطلوبة:\n{', '.join(missing_packages)}\n\nهل تريد تثبيتها الآن؟"
        )
        if response:
            try:
                for package in missing_packages:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                messagebox.showinfo("نجاح", "تم تثبيت المكتبات بنجاح!")
                return True
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تثبيت المكتبات: {str(e)}")
                return False
        else:
            return False
    return True

def create_initial_files():
    """إنشاء الملفات الأولية إذا لم تكن موجودة"""
    try:
        # إنشاء ملف المستخدمين
        users_file = get_resource_path("users.json") if is_running_as_executable() else "users.json"
        if not os.path.exists(users_file):
            users_data = {
                "admin": {"password": "admin123", "role": "admin"},
                "hr_manager": {"password": "hr123", "role": "hr_manager"},
                "employee": {"password": "emp123", "role": "employee"}
            }
            with open(users_file, "w", encoding="utf-8") as f:
                json.dump(users_data, f, ensure_ascii=False, indent=4)

        # إنشاء ملفات Excel إذا لم تكن موجودة
        try:
            import openpyxl
            from openpyxl import Workbook

            if not os.path.exists("employees_data.xlsx"):
                wb = Workbook()
                ws = wb.active
                ws.title = "Employees"
                headers = [
                    "الاسم العربي", "الاسم الإنجليزي", "الرقم الوظيفي", "الرقم المالي",
                    "الرقم الوطني", "المؤهل", "مكان العمل الحالي", "رقم الحساب",
                    "اسم المصرف", "تاريخ أول مباشرة", "الدرجة الحالية", "العلاوة",
                    "تاريخ الدرجة الحالية", "التخصص", "المسمى الوظيفي",
                    "تاريخ التعيين", "رقم الهاتف", "الجنسية", "تاريخ الميلاد"
                ]
                ws.append(headers)
                wb.save("employees_data.xlsx")

            if not os.path.exists("leaves_data.xlsx"):
                wb = Workbook()
                ws = wb.active
                ws.title = "Leaves"
                headers = [
                    "الرقم الوظيفي", "اسم الموظف", "نوع الإجازة",
                    "تاريخ البدء", "تاريخ الانتهاء", "عدد الأيام",
                    "الحالة", "ملاحظات"
                ]
                ws.append(headers)
                wb.save("leaves_data.xlsx")

        except ImportError:
            pass  # سيتم إنشاء الملفات لاحقاً

        # إنشاء المجلدات المطلوبة
        directories = ["data", "exports", "documents", "logs", "templates"]
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)

    except Exception as e:
        print(f"تحذير: لم يتم إنشاء بعض الملفات: {str(e)}")

class HRMainSystem:
    """النظام الرئيسي لإدارة الموارد البشرية"""

    def __init__(self):
        self.current_user = None
        self.open_windows = {}

        # فحص وتثبيت المتطلبات
        if not check_and_install_requirements():
            sys.exit(1)

        # إنشاء الملفات الأولية
        create_initial_files()
        
    def create_users_file(self):
        """إنشاء ملف المستخدمين"""
        users_file = get_resource_path("users.json") if is_running_as_executable() else "users.json"
        if not os.path.exists(users_file):
            users_data = {
                "admin": {
                    "password": "admin123",
                    "role": "admin"
                }
            }
            with open(users_file, "w", encoding="utf-8") as f:
                json.dump(users_data, f, ensure_ascii=False, indent=4)
    
    def show_login(self):
        """عرض نافذة تسجيل الدخول محسنة"""
        self.create_users_file()

        login_root = tk.Tk()
        login_root.title("منظومة إدارة الموارد البشرية - تسجيل الدخول")
        login_root.configure(bg="#f0f8ff")

        # تحديد حجم مناسب للنافذة
        window_width = 550
        window_height = 650

        # الحصول على أبعاد الشاشة للتوسيط
        screen_width = login_root.winfo_screenwidth()
        screen_height = login_root.winfo_screenheight()

        # توسيط النافذة
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        login_root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        login_root.resizable(False, False)  # حجم ثابت

        # جعل النافذة في المقدمة
        login_root.lift()
        login_root.attributes('-topmost', True)
        login_root.after_idle(lambda: login_root.attributes('-topmost', False))

        # إضافة أيقونة للنافذة (اختيارية)
        try:
            login_root.iconbitmap(default="icon.ico")
        except:
            pass

        # إطار تسجيل الدخول مع تصميم محسن
        main_frame = tk.Frame(login_root, bg="#f0f8ff", padx=30, pady=25)  # حشو مناسب للحجم الأصغر
        main_frame.pack(fill=tk.BOTH, expand=True)

        # إطار داخلي مع حدود وظل
        login_frame = tk.Frame(main_frame, bg="#ffffff", relief=tk.RAISED, bd=3, padx=25, pady=30)  # حشو مناسب
        login_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان رئيسي بخط مناسب
        title_label = tk.Label(login_frame, text="🏢 منظومة إدارة الموارد البشرية",
                              font=("Arial", 18, "bold"), fg="#2c3e50", bg="#ffffff")  # خط مناسب للحجم الأصغر
        title_label.pack(pady=(0, 15))  # مسافة مناسبة

        # عنوان فرعي
        subtitle_label = tk.Label(login_frame, text="تسجيل الدخول للنظام",
                                 font=("Arial", 14), fg="#7f8c8d", bg="#ffffff")  # خط مناسب
        subtitle_label.pack(pady=(0, 25))  # مسافة مناسبة

        # إطار الحقول
        fields_frame = tk.Frame(login_frame, bg="#ffffff")
        fields_frame.pack(fill=tk.X, pady=20)  # مسافة مناسبة

        # حقل اسم المستخدم مع خط مناسب
        username_label = tk.Label(fields_frame, text="👤 اسم المستخدم:",
                                 font=("Arial", 14, "bold"), fg="#34495e", bg="#ffffff")  # خط مناسب للحجم الأصغر
        username_label.pack(pady=(0, 8))  # مسافة مناسبة

        username_entry = tk.Entry(fields_frame, font=("Arial", 13), width=25,  # خط وعرض مناسب
                                 relief=tk.GROOVE, bd=2, justify='center')
        username_entry.pack(pady=(0, 20), ipady=8)  # مسافة وحشو مناسب

        # حقل كلمة المرور مع خط مناسب
        password_label = tk.Label(fields_frame, text="🔐 كلمة المرور:",
                                 font=("Arial", 14, "bold"), fg="#34495e", bg="#ffffff")  # خط مناسب
        password_label.pack(pady=(0, 8))  # مسافة مناسبة

        password_entry = tk.Entry(fields_frame, font=("Arial", 13), width=25, show="*",  # خط وعرض مناسب
                                 relief=tk.GROOVE, bd=2, justify='center')
        password_entry.pack(pady=(0, 25), ipady=8)  # مسافة وحشو مناسب
        
        def login():
            username = username_entry.get().strip()
            password = password_entry.get().strip()

            # التحقق من وجود البيانات
            if not username or not password:
                messagebox.showwarning("تحذير", "الرجاء إدخال اسم المستخدم وكلمة المرور")
                return

            # التحقق من ملف المستخدمين
            users_file = get_resource_path("users.json") if is_running_as_executable() else "users.json"
            try:
                if os.path.exists(users_file):
                    with open(users_file, "r", encoding="utf-8") as f:
                        users_data = json.load(f)

                    if username in users_data and users_data[username]["password"] == password:
                        self.current_user = {
                            "username": username,
                            "role": users_data[username].get("role", "employee"),
                            "name": users_data[username].get("name", username)
                        }
                        # إخفاء نافذة تسجيل الدخول
                        login_root.withdraw()
                        # إظهار النظام الرئيسي
                        self.show_main_system()
                        # تدمير نافذة تسجيل الدخول
                        login_root.destroy()
                        return

                # إذا فشل التحقق من الملف، استخدم البيانات الافتراضية
                if username == "admin" and password == "admin123":
                    self.current_user = {"username": username, "role": "admin", "name": "مدير النظام"}
                    login_root.withdraw()
                    self.show_main_system()
                    login_root.destroy()
                else:
                    messagebox.showerror("خطأ في تسجيل الدخول",
                                       "اسم المستخدم أو كلمة المرور غير صحيحة\n"
                                       "الرجاء المحاولة مرة أخرى")
                    # مسح كلمة المرور عند الخطأ
                    password_entry.delete(0, tk.END)
                    password_entry.focus_set()

            except Exception as e:
                # في حالة خطأ في قراءة الملف، استخدم البيانات الافتراضية
                if username == "admin" and password == "admin123":
                    self.current_user = {"username": username, "role": "admin", "name": "مدير النظام"}
                    login_root.withdraw()
                    self.show_main_system()
                    login_root.destroy()
                else:
                    messagebox.showerror("خطأ في النظام", f"حدث خطأ في النظام:\n{str(e)}")

        def minimize_window():
            """تصغير النافذة"""
            login_root.iconify()

        def close_application():
            """إغلاق التطبيق"""
            if messagebox.askyesno("تأكيد الخروج", "هل أنت متأكد من إغلاق التطبيق؟"):
                login_root.quit()
                login_root.destroy()

        def clear_fields():
            """مسح الحقول"""
            username_entry.delete(0, tk.END)
            password_entry.delete(0, tk.END)
            username_entry.focus_set()

        # إطار الأزرار
        buttons_frame = tk.Frame(login_frame, bg="#ffffff")
        buttons_frame.pack(fill=tk.X, pady=20)  # مسافة مناسبة

        # زر تسجيل الدخول الرئيسي
        login_btn = tk.Button(buttons_frame, text="🔑 تسجيل الدخول",
                             font=("Arial", 14, "bold"), command=login,  # خط مناسب للحجم الأصغر
                             bg="#27ae60", fg="white", relief=tk.RAISED, bd=3,
                             padx=20, pady=10, width=18)  # حشو وعرض مناسب
        login_btn.pack(pady=(0, 15))  # مسافة مناسبة

        # إطار الأزرار الفرعية
        sub_buttons_frame = tk.Frame(buttons_frame, bg="#ffffff")
        sub_buttons_frame.pack(pady=10)  # مسافة مناسبة

        # زر مسح الحقول
        clear_btn = tk.Button(sub_buttons_frame, text="🗑️ مسح",
                             font=("Arial", 11), command=clear_fields,  # خط مناسب للحجم الأصغر
                             bg="#f39c12", fg="white", relief=tk.RAISED, bd=2,
                             padx=12, pady=6, width=10)  # حشو وعرض مناسب
        clear_btn.pack(side=tk.LEFT, padx=5)  # مسافة مناسبة

        # زر تصغير
        minimize_btn = tk.Button(sub_buttons_frame, text="➖ تصغير",
                               font=("Arial", 11), command=minimize_window,  # خط مناسب
                               bg="#3498db", fg="white", relief=tk.RAISED, bd=2,
                               padx=12, pady=6, width=10)  # حشو وعرض مناسب
        minimize_btn.pack(side=tk.LEFT, padx=5)  # مسافة مناسبة

        # زر إغلاق
        close_btn = tk.Button(sub_buttons_frame, text="❌ إغلاق",
                             font=("Arial", 11), command=close_application,  # خط مناسب
                             bg="#e74c3c", fg="white", relief=tk.RAISED, bd=2,
                             padx=12, pady=6, width=10)  # حشو وعرض مناسب
        close_btn.pack(side=tk.LEFT, padx=5)  # مسافة مناسبة

        # معلومات إضافية
        info_frame = tk.Frame(login_frame, bg="#ffffff")
        info_frame.pack(fill=tk.X, pady=(15, 0))  # مسافة مناسبة

        info_label = tk.Label(info_frame, text="💡 اضغط Enter للدخول السريع | ESC للخروج",
                             font=("Arial", 10), fg="#95a5a6", bg="#ffffff")  # خط مناسب للحجم الأصغر
        info_label.pack(pady=8)  # مسافة مناسبة

        # ربط المفاتيح
        login_root.bind('<Return>', lambda event: login())
        login_root.bind('<Escape>', lambda event: close_application())
        login_root.bind('<F1>', lambda event: clear_fields())

        # ربط Tab للتنقل بين الحقول
        username_entry.bind('<Tab>', lambda event: password_entry.focus_set())
        password_entry.bind('<Tab>', lambda event: username_entry.focus_set())

        # تركيز تلقائي على حقل اسم المستخدم
        username_entry.focus_set()

        # إظهار النافذة
        login_root.deiconify()

        # تشغيل النافذة
        login_root.mainloop()
    
    def show_main_system(self):
        """عرض النظام الرئيسي بتصميم عمودي محسن"""
        main_root = tk.Tk()
        main_root.title("🏢 منظومة إدارة الموارد البشرية")

        # تصميم نافذة بسيط وفعال
        screen_width = main_root.winfo_screenwidth()
        screen_height = main_root.winfo_screenheight()

        # حجم نافذة مناسب لجميع الشاشات
        window_width = min(1400, int(screen_width * 0.9))
        window_height = min(900, int(screen_height * 0.9))

        # توسيط النافذة
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        main_root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        main_root.configure(bg="#f5f5f5")
        main_root.resizable(True, True)
        main_root.minsize(1000, 700)

        # إطار رئيسي بسيط وعملي
        main_frame = tk.Frame(main_root, bg="#f5f5f5", padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان النظام - تصميم بسيط وواضح
        header_frame = tk.Frame(main_frame, bg="#2c3e50", relief=tk.RAISED, bd=2)
        header_frame.pack(fill=tk.X, pady=(0, 20))

        header_content = tk.Frame(header_frame, bg="#2c3e50", padx=25, pady=15)
        header_content.pack(fill=tk.X)

        # العنوان الرئيسي
        title_label = tk.Label(header_content, text="🏢 منظومة إدارة الموارد البشرية",
                              font=("Arial", 22, "bold"), fg="white", bg="#2c3e50")
        title_label.pack(side=tk.LEFT)

        # معلومات المستخدم
        user_info = tk.Label(header_content, text=f"👤 مرحباً {self.current_user['username']}",
                            font=("Arial", 14), fg="#ecf0f1", bg="#2c3e50")
        user_info.pack(side=tk.RIGHT)
        
        # إطار التنبيهات - تصميم بسيط وعملي
        alerts_frame = tk.LabelFrame(main_frame, text="⚠️ تنبيهات مهمة",
                                   font=("Arial", 12, "bold"), fg="#e74c3c",
                                   bg="#ffffff", padx=15, pady=10)
        alerts_frame.pack(fill=tk.X, pady=(0, 15))

        # إطار محتوى التنبيهات
        alerts_content = tk.Frame(alerts_frame, bg="#ffffff")
        alerts_content.pack(fill=tk.X)

        # إطار التنبيهات الفعلية
        self.alerts_display_frame = tk.Frame(alerts_content, bg="#ffffff")
        self.alerts_display_frame.pack(fill=tk.X, side=tk.LEFT, expand=True)

        # أزرار التحديث
        buttons_frame = tk.Frame(alerts_content, bg="#ffffff")
        buttons_frame.pack(side=tk.RIGHT, padx=(10, 0))

        refresh_alerts_btn = tk.Button(buttons_frame, text="🔄 تحديث التنبيهات",
                                     command=self.refresh_alerts,
                                     font=("Arial", 10, "bold"),
                                     bg="#27ae60", fg="white",
                                     relief=tk.RAISED, bd=2,
                                     padx=12, pady=6)
        refresh_alerts_btn.pack(pady=(0, 5))

        refresh_stats_btn = tk.Button(buttons_frame, text="📊 تحديث الإحصائيات",
                                    command=self.refresh_stats,
                                    font=("Arial", 10, "bold"),
                                    bg="#3498db", fg="white",
                                    relief=tk.RAISED, bd=2,
                                    padx=12, pady=6)
        refresh_stats_btn.pack()

        # إضافة جميع أنواع التنبيهات المهمة
        self.create_important_alerts(self.alerts_display_frame)

        # إطار الإحصائيات - تصميم بسيط ومضغوط
        stats_frame = tk.LabelFrame(main_frame, text="📊 إحصائيات سريعة",
                                  font=("Arial", 12, "bold"), fg="#2980b9",
                                  bg="#ffffff", padx=15, pady=10)
        stats_frame.pack(fill=tk.X, pady=(0, 15))

        # حساب الإحصائيات الفعلية
        stats_data = self.calculate_dashboard_stats()

        # إحصائيات مضغوطة وبسيطة
        stats = [
            ("👥 الموظفين", str(stats_data["employees_count"]), "#3498db"),
            ("🏖️ إجازات الشهر", str(stats_data["leaves_this_month"]), "#9b59b6"),
            ("💰 متوسط الرصيد", str(stats_data["avg_remaining_balance"]), "#16a085"),
            ("⬆️ ترقيات مستحقة", str(stats_data["pending_promotions"]), "#2ecc71"),
            ("📊 إجمالي الإجازات", str(stats_data["total_leaves"]), "#f39c12"),
            ("⚠️ رصيد منخفض", str(stats_data["low_balance_employees"]), "#e74c3c")
        ]

        # حفظ مراجع للتحديث اللاحق
        self.stats_labels = []

        for i, (title, value, bg_color) in enumerate(stats):
            stat_frame = tk.Frame(stats_frame, bg=bg_color, relief=tk.RAISED, bd=2)
            stat_frame.grid(row=i//6, column=i%6, padx=5, pady=5, sticky="nsew", ipadx=10, ipady=8)

            title_label = tk.Label(stat_frame, text=title, font=("Arial", 9, "bold"),
                                 bg=bg_color, fg="white")
            title_label.pack(pady=(5, 2))

            value_label = tk.Label(stat_frame, text=value, font=("Arial", 14, "bold"),
                                 bg=bg_color, fg="white")
            value_label.pack(pady=(0, 5))

            # حفظ مرجع للتحديث
            self.stats_labels.append((title_label, value_label, title, bg_color, "white"))

        # جعل الخلايا قابلة للتوسع - 6 أعمدة في صف واحد
        for col in range(6):
            stats_frame.grid_columnconfigure(col, weight=1)
        
        # إطار الوحدات - تصميم عمودي محسن
        modules_frame = tk.LabelFrame(main_frame, text="🔧 وحدات النظام",
                                    font=("Arial", 14, "bold"), fg="#34495e",
                                    bg="#ffffff", padx=20, pady=15)
        modules_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # أزرار الوحدات بتخطيط عمودي بسيط
        modules = [
            ("👥 إدارة الموظفين", self.open_employees, "إدارة بيانات الموظفين", "#3498db"),
            ("🏖️ إدارة الإجازات", self.open_leaves, "إدارة إجازات الموظفين", "#2ecc71"),
            ("💰 رصيد الإجازات السنوي", self.open_leave_balance, "إدارة رصيد الإجازات السنوي للموظفين", "#16a085"),
            ("⬆️ نظام الترقيات", self.open_promotions, "حساب وإدارة الترقيات", "#9b59b6"),
            ("📊 التقارير والإحصائيات", self.open_reports, "عرض التقارير والإحصائيات", "#e67e22"),
            ("🎨 مدير قوالب المراسلات", self.open_template_manager, "إدارة قوالب المراسلات العامة", "#e74c3c"),
            ("🔐 إدارة المستخدمين", self.open_user_management, "إدارة المستخدمين والصلاحيات", "#8e44ad")
        ]

        print(f"🔧 إنشاء {len(modules)} وحدة بتخطيط عمودي...")

        for i, (text, command, tooltip, color) in enumerate(modules):
            # تخطيط عمودي: صفين - الأول 4 وحدات والثاني 3 وحدات
            if i < 4:
                row = 0
                col = i
            else:
                row = 1
                col = i - 4

            print(f"   📋 إنشاء وحدة {i+1}: {text} في الموضع ({row}, {col})")

            # إطار الوحدة بتصميم بسيط وعملي
            btn_frame = tk.Frame(modules_frame, bg="#ffffff", relief=tk.RAISED, bd=2)
            btn_frame.grid(row=row, column=col, padx=12, pady=12, sticky="nsew", ipadx=10, ipady=10)

            # زر كبير وواضح
            btn = tk.Button(btn_frame, text=text, font=("Arial", 12, "bold"),
                           width=16, height=3, command=command,
                           bg=color, fg="white",
                           relief=tk.RAISED, bd=3,
                           activebackground="#2c3e50", activeforeground="white",
                           cursor="hand2")
            btn.pack(fill=tk.BOTH, expand=True, padx=8, pady=8)

            # وصف مختصر
            desc_label = tk.Label(btn_frame, text=tooltip, font=("Arial", 9),
                                 fg="#7f8c8d", bg="#ffffff", wraplength=160)
            desc_label.pack(pady=(5, 0))

        print(f"✅ تم إنشاء جميع الوحدات بنجاح")

        # تكوين الشبكة للتخطيط العمودي - 4 أعمدة في الصف الأول
        for col in range(4):
            modules_frame.grid_columnconfigure(col, weight=1)

        # صفين: الأول 4 وحدات والثاني 3 وحدات
        modules_frame.grid_rowconfigure(0, weight=1)
        modules_frame.grid_rowconfigure(1, weight=1)

        print(f"🔧 تكوين الشبكة: {len(modules)} وحدة في صفين (4+3)")

        # تحديث الواجهة لضمان ظهور جميع العناصر
        modules_frame.update_idletasks()
        main_frame.update_idletasks()
        
        # إطار الأزرار السفلية - تصميم بسيط وعملي
        bottom_frame = tk.Frame(main_frame, bg="#ecf0f1", relief=tk.RAISED, bd=2)
        bottom_frame.pack(fill=tk.X, pady=(15, 0))

        # إطار داخلي للأزرار
        bottom_inner = tk.Frame(bottom_frame, bg="#ecf0f1", padx=20, pady=15)
        bottom_inner.pack(fill=tk.X)

        # زر تسجيل الخروج
        logout_btn = tk.Button(bottom_inner, text="🚪 تسجيل الخروج",
                              font=("Arial", 12, "bold"), command=self.logout,
                              bg="#e74c3c", fg="white",
                              relief=tk.RAISED, bd=3,
                              padx=20, pady=10,
                              activebackground="#c0392b", activeforeground="white")
        logout_btn.pack(side=tk.RIGHT, padx=10)

        # زر المساعدة
        help_btn = tk.Button(bottom_inner, text="❓ المساعدة",
                            font=("Arial", 12, "bold"), command=self.show_help,
                            bg="#3498db", fg="white",
                            relief=tk.RAISED, bd=3,
                            padx=20, pady=10,
                            activebackground="#2980b9", activeforeground="white")
        help_btn.pack(side=tk.LEFT, padx=10)

        # زر مراقب النظام
        monitor_btn = tk.Button(bottom_inner, text="🔍 مراقب النظام",
                               font=("Arial", 12, "bold"), command=self.show_system_monitor,
                               bg="#9b59b6", fg="white",
                               relief=tk.RAISED, bd=3,
                               padx=20, pady=10,
                               activebackground="#8e44ad", activeforeground="white")
        monitor_btn.pack(side=tk.LEFT, padx=10)

        # معلومات النظام
        info_label = tk.Label(bottom_inner, text="منظومة إدارة الموارد البشرية - الإصدار 2.2",
                             font=("Arial", 10), fg="#7f8c8d", bg="#ecf0f1")
        info_label.pack()

        # مؤشر حالة النوافذ
        self.windows_status_label = tk.Label(bottom_inner, text="📋 النوافذ المفتوحة: لا يوجد",
                                           font=("Arial", 9), fg="#95a5a6", bg="#ecf0f1")
        self.windows_status_label.pack(pady=(5, 0))

        # تحديث مؤشر حالة النوافذ
        self.update_windows_status_display()

        # حفظ مرجع النافذة الرئيسية
        self.main_root = main_root

        # تشغيل النافذة الرئيسية
        main_root.mainloop()

    def calculate_dashboard_stats(self):
        """حساب إحصائيات لوحة التحكم"""
        try:
            stats = {
                "employees_count": 0,
                "leaves_this_month": 0,
                "avg_remaining_balance": 0,
                "pending_promotions": 0,
                "total_leaves": 0,
                "low_balance_employees": 0
            }

            # حساب عدد الموظفين
            stats["employees_count"] = self.count_employees()

            # حساب إجازات هذا الشهر
            stats["leaves_this_month"] = self.count_leaves_this_month()

            # حساب متوسط الرصيد المتبقي
            stats["avg_remaining_balance"] = self.calculate_avg_remaining_balance()

            # حساب الترقيات المستحقة
            stats["pending_promotions"] = self.count_pending_promotions()

            # حساب إجمالي الإجازات
            stats["total_leaves"] = self.count_total_leaves()

            # حساب الموظفين برصيد منخفض
            stats["low_balance_employees"] = self.count_low_balance_employees()

            return stats

        except Exception as e:
            print(f"خطأ في حساب الإحصائيات: {e}")
            return {
                "employees_count": 0,
                "leaves_this_month": 0,
                "avg_remaining_balance": 0,
                "pending_promotions": 0,
                "total_leaves": 0,
                "low_balance_employees": 0
            }

    def count_employees(self):
        """حساب عدد الموظفين"""
        try:
            from openpyxl import load_workbook

            employees_file = "employees_data.xlsx"
            if not os.path.exists(employees_file):
                return 0

            wb = load_workbook(employees_file)
            # محاولة العثور على الورقة بأسماء مختلفة
            if "الموظفين" in wb.sheetnames:
                ws = wb["الموظفين"]
            elif "Employees" in wb.sheetnames:
                ws = wb["Employees"]
            else:
                ws = wb.active

            # عد الصفوف التي تحتوي على بيانات (باستثناء العنوان)
            count = 0
            for row in ws.iter_rows(min_row=2, values_only=True):
                if any(row):  # إذا كان هناك أي بيانات في الصف
                    count += 1

            return count

        except Exception as e:
            print(f"خطأ في حساب عدد الموظفين: {e}")
            return 0

    def count_leaves_this_month(self):
        """حساب إجازات هذا الشهر"""
        try:
            from datetime import datetime

            leaves_data = self.load_leaves_data()
            if not leaves_data:
                return 0

            current_month = datetime.now().month
            current_year = datetime.now().year

            count = 0
            for leave in leaves_data:
                try:
                    start_date_str = leave.get("تاريخ البدء", "")
                    if not start_date_str:
                        continue

                    # محاولة تحويل التاريخ بصيغ مختلفة
                    start_date = None
                    for date_format in ["%Y-%m-%d", "%d/%m/%Y", "%d-%m-%Y"]:
                        try:
                            start_date = datetime.strptime(str(start_date_str), date_format)
                            break
                        except:
                            continue

                    if start_date and start_date.month == current_month and start_date.year == current_year:
                        count += 1

                except Exception as e:
                    continue

            return count

        except Exception as e:
            print(f"خطأ في حساب إجازات هذا الشهر: {e}")
            return 0

    def count_pending_promotions(self):
        """حساب الترقيات المستحقة - متوافق مع نظام الترقيات"""
        try:
            from datetime import datetime

            employees_data = self.load_employees_data()
            if not employees_data:
                return 0

            count = 0
            current_date = datetime.now()

            for employee in employees_data:
                try:
                    # استخدام نفس معايير نظام الترقيات
                    job_title = employee.get("المسمى الوظيفي", "")
                    current_grade = employee.get("الدرجة الحالية", "")
                    grade_date_str = employee.get("تاريخ الدرجة الحالية", "")

                    # تجاهل المعلمين (حسب نظام الترقيات)
                    if "معلم" in job_title:
                        continue

                    if not current_grade or not grade_date_str:
                        continue

                    # محاولة تحويل التاريخ
                    grade_date = None
                    for date_format in ["%Y-%m-%d", "%d/%m/%Y", "%d-%m-%Y"]:
                        try:
                            grade_date = datetime.strptime(str(grade_date_str), date_format)
                            break
                        except:
                            continue

                    if grade_date:
                        # حساب السنوات في الدرجة الحالية
                        years_in_grade = (current_date - grade_date).days / 365.25

                        # فحص الحد الأقصى للدرجة
                        try:
                            current_grade_num = int(current_grade)
                            if current_grade_num >= 15:  # الحد الأقصى
                                continue
                        except:
                            pass

                        # معيار الاستحقاق: 4 سنوات أو أكثر (نفس نظام الترقيات)
                        if years_in_grade >= 4:
                            count += 1

                except Exception as e:
                    continue

            return count

        except Exception as e:
            print(f"خطأ في حساب الترقيات المستحقة: {e}")
            return 0

    def count_total_leaves(self):
        """حساب إجمالي الإجازات"""
        try:
            leaves_data = self.load_leaves_data()
            return len(leaves_data) if leaves_data else 0

        except Exception as e:
            print(f"خطأ في حساب إجمالي الإجازات: {e}")
            return 0

    def calculate_avg_remaining_balance(self):
        """حساب متوسط الرصيد المتبقي للموظفين"""
        try:
            # محاولة استيراد نظام الرصيد
            try:
                import leave_balance_system
                import tkinter as tk

                # إنشاء نسخة مؤقتة من نظام الرصيد
                temp_root = tk.Tk()
                temp_root.withdraw()  # إخفاء النافذة
                balance_system = leave_balance_system.LeaveBalanceSystem(temp_root)

                total_remaining = 0
                employee_count = 0

                for emp in balance_system.employees_data:
                    emp_id = emp.get("الرقم الوظيفي", "")
                    if emp_id:
                        service_years = balance_system.calculate_service_years(emp.get("تاريخ أول مباشرة", ""))
                        auto_balance = balance_system.calculate_automatic_balance(service_years)
                        manual_balance = balance_system.get_manual_balance(emp_id)
                        total_balance = auto_balance + manual_balance
                        used_leaves = balance_system.calculate_used_leaves(emp_id)
                        remaining_balance = total_balance - used_leaves

                        total_remaining += remaining_balance
                        employee_count += 1

                temp_root.destroy()

                if employee_count > 0:
                    return round(total_remaining / employee_count)
                else:
                    return 0

            except ImportError:
                return 0

        except Exception as e:
            print(f"خطأ في حساب متوسط الرصيد المتبقي: {e}")
            return 0

    def count_low_balance_employees(self):
        """حساب عدد الموظفين برصيد منخفض (أقل من 10 أيام)"""
        try:
            # محاولة استيراد نظام الرصيد
            try:
                import leave_balance_system
                import tkinter as tk

                # إنشاء نسخة مؤقتة من نظام الرصيد
                temp_root = tk.Tk()
                temp_root.withdraw()  # إخفاء النافذة
                balance_system = leave_balance_system.LeaveBalanceSystem(temp_root)

                low_balance_count = 0

                for emp in balance_system.employees_data:
                    emp_id = emp.get("الرقم الوظيفي", "")
                    if emp_id:
                        service_years = balance_system.calculate_service_years(emp.get("تاريخ أول مباشرة", ""))
                        auto_balance = balance_system.calculate_automatic_balance(service_years)
                        manual_balance = balance_system.get_manual_balance(emp_id)
                        total_balance = auto_balance + manual_balance
                        used_leaves = balance_system.calculate_used_leaves(emp_id)
                        remaining_balance = total_balance - used_leaves

                        if remaining_balance < 10:  # أقل من 10 أيام
                            low_balance_count += 1

                temp_root.destroy()
                return low_balance_count

            except ImportError:
                return 0

        except Exception as e:
            print(f"خطأ في حساب الموظفين برصيد منخفض: {e}")
            return 0

    def load_employees_data(self):
        """تحميل بيانات الموظفين من ملف Excel"""
        try:
            from openpyxl import load_workbook

            employees_file = "employees_data.xlsx"
            if not os.path.exists(employees_file):
                return []

            wb = load_workbook(employees_file)
            # محاولة العثور على الورقة بأسماء مختلفة
            if "الموظفين" in wb.sheetnames:
                ws = wb["الموظفين"]
            elif "Employees" in wb.sheetnames:
                ws = wb["Employees"]
            else:
                ws = wb.active

            data = []
            headers = [cell.value for cell in ws[1]]

            for row in ws.iter_rows(min_row=2, values_only=True):
                if any(row):
                    data.append(dict(zip(headers, row)))

            return data

        except Exception as e:
            print(f"خطأ في تحميل بيانات الموظفين: {e}")
            return []

    def create_important_alerts(self, parent_frame):
        """إنشاء جميع التنبيهات المهمة"""
        try:
            # مسح التنبيهات الحالية
            for widget in parent_frame.winfo_children():
                widget.destroy()

            alerts_found = False

            # 1. تنبيهات الإجازات المنتهية قريباً
            leave_alerts = self.get_leave_alerts()
            if leave_alerts:
                alerts_found = True
                self.display_leave_alerts(parent_frame, leave_alerts)

            # 2. تنبيهات الترقيات المستحقة - تم إلغاؤها
            # promotion_alerts = self.get_promotion_alerts()
            # if promotion_alerts:
            #     alerts_found = True
            #     self.display_promotion_alerts(parent_frame, promotion_alerts)

            # 3. تنبيهات النظام
            system_alerts = self.get_system_alerts()
            if system_alerts:
                alerts_found = True
                self.display_system_alerts(parent_frame, system_alerts)

            # 4. تنبيهات البيانات
            data_alerts = self.get_data_alerts()
            if data_alerts:
                alerts_found = True
                self.display_data_alerts(parent_frame, data_alerts)

            # إذا لم توجد تنبيهات
            if not alerts_found:
                no_alerts_label = tk.Label(parent_frame, text="✅ لا توجد تنبيهات مهمة حالياً",
                                         font=("Arial", 12, "bold"), fg="#27ae60", bg="#ffffff")
                no_alerts_label.pack(pady=10)

                # إضافة معلومات إضافية
                info_text = "🔍 فحص: الإجازات المنتهية • ملفات النظام • صحة البيانات"
                info_label = tk.Label(parent_frame, text=info_text,
                                    font=("Arial", 10), fg="#7f8c8d", bg="#ffffff", justify="center")
                info_label.pack(pady=(0, 10))

        except Exception as e:
            error_label = tk.Label(parent_frame, text=f"❌ خطأ في تحميل التنبيهات المهمة: {str(e)}",
                                 font=("Arial", 12, "bold"), fg="#e74c3c", bg="#fff5f5")
            error_label.pack(pady=15)
            print(f"خطأ في التنبيهات المهمة: {e}")

    def get_leave_alerts(self):
        """الحصول على تنبيهات الإجازات"""
        try:
            leaves_data = self.load_leaves_data()
            if not leaves_data:
                return []

            from datetime import datetime, timedelta
            today = datetime.now().date()
            alert_period = today + timedelta(days=5)

            expiring_leaves = []

            for leave in leaves_data:
                try:
                    # التحقق من أن الإجازة موافق عليها أو نشطة
                    status = leave.get("الحالة", "").strip()
                    if status not in ["موافق", "نشط", "مفعل"]:
                        continue

                    # تحويل تاريخ الانتهاء
                    end_date_str = leave.get("تاريخ الانتهاء", "")
                    if not end_date_str:
                        continue

                    # محاولة تحويل التاريخ بصيغ مختلفة
                    end_date = None
                    for date_format in ["%Y-%m-%d", "%d/%m/%Y", "%d-%m-%Y"]:
                        try:
                            end_date = datetime.strptime(str(end_date_str), date_format).date()
                            break
                        except:
                            continue

                    if not end_date:
                        continue

                    # فحص إذا كانت الإجازة ستنتهي خلال 5 أيام
                    if today <= end_date <= alert_period:
                        days_remaining = (end_date - today).days
                        expiring_leaves.append({
                            "employee": leave.get("اسم الموظف", "غير محدد"),
                            "type": leave.get("نوع الإجازة", "غير محدد"),
                            "end_date": end_date,
                            "days_remaining": days_remaining
                        })

                except Exception as e:
                    print(f"خطأ في معالجة إجازة: {e}")
                    continue

            return expiring_leaves

        except Exception as e:
            print(f"خطأ في تحميل تنبيهات الإجازات: {e}")
            return []

    def get_promotion_alerts(self):
        """الحصول على تنبيهات الترقيات المستحقة - متوافق مع نظام الترقيات"""
        try:
            employees_data = self.load_employees_data()
            if not employees_data:
                return []

            from datetime import datetime
            today = datetime.now()

            due_promotions = []

            for emp in employees_data:
                try:
                    # استخدام نفس معايير نظام الترقيات
                    job_title = emp.get("المسمى الوظيفي", "")
                    current_grade = emp.get("الدرجة الحالية", "")
                    grade_date_str = emp.get("تاريخ الدرجة الحالية", "")

                    # تجاهل المعلمين (حسب نظام الترقيات)
                    if "معلم" in job_title:
                        continue

                    if not current_grade or not grade_date_str:
                        continue

                    # تحويل تاريخ الدرجة الحالية إلى كائن تاريخ
                    grade_date = None
                    for date_format in ["%Y-%m-%d", "%d/%m/%Y", "%d-%m-%Y"]:
                        try:
                            grade_date = datetime.strptime(str(grade_date_str), date_format)
                            break
                        except:
                            continue

                    if not grade_date:
                        continue

                    # حساب المدة في الدرجة الحالية (بالسنوات)
                    duration = today - grade_date
                    years_in_grade = duration.days / 365.25

                    # فحص الحد الأقصى للدرجة
                    try:
                        current_grade_num = int(current_grade)
                        if current_grade_num >= 15:  # الحد الأقصى
                            continue
                    except:
                        pass

                    # إذا كان مستحق للترقية (4 سنوات أو أكثر)
                    if years_in_grade >= 4:
                        due_promotions.append({
                            "employee": emp.get("الاسم العربي", "غير محدد"),
                            "emp_id": emp.get("الرقم الوظيفي", "غير محدد"),
                            "current_grade": current_grade,
                            "years_in_grade": round(years_in_grade, 1)
                        })

                except Exception as e:
                    print(f"خطأ في معالجة ترقية: {e}")
                    continue

            return due_promotions

        except Exception as e:
            print(f"خطأ في تحميل تنبيهات الترقيات: {e}")
            return []

    def get_system_alerts(self):
        """الحصول على تنبيهات النظام"""
        try:
            system_alerts = []

            # فحص ملفات النظام المهمة
            important_files = [
                ("employees_data.xlsx", "ملف بيانات الموظفين"),
                ("leaves_data.xlsx", "ملف بيانات الإجازات"),
                ("users.json", "ملف المستخدمين")
            ]

            for filename, description in important_files:
                if not os.path.exists(filename):
                    system_alerts.append({
                        "type": "missing_file",
                        "message": f"ملف مفقود: {description}",
                        "severity": "high"
                    })

            # فحص مجلدات النظام
            important_dirs = [
                ("correspondence_templates", "مجلد قوالب المراسلات"),
                ("documents", "مجلد المستندات")
            ]

            for dirname, description in important_dirs:
                if not os.path.exists(dirname):
                    system_alerts.append({
                        "type": "missing_directory",
                        "message": f"مجلد مفقود: {description}",
                        "severity": "medium"
                    })

            # فحص المكتبات المطلوبة
            try:
                import openpyxl
            except ImportError:
                system_alerts.append({
                    "type": "missing_library",
                    "message": "مكتبة openpyxl غير مثبتة - مطلوبة لقراءة ملفات Excel",
                    "severity": "high"
                })

            try:
                from docx import Document
            except ImportError:
                system_alerts.append({
                    "type": "missing_library",
                    "message": "مكتبة python-docx غير مثبتة - مطلوبة لإنشاء المراسلات",
                    "severity": "medium"
                })

            return system_alerts

        except Exception as e:
            print(f"خطأ في فحص تنبيهات النظام: {e}")
            return []

    def get_data_alerts(self):
        """الحصول على تنبيهات البيانات"""
        try:
            data_alerts = []

            # فحص بيانات الموظفين
            employees_data = self.load_employees_data()
            if employees_data:
                incomplete_employees = 0
                for emp in employees_data:
                    required_fields = ["الاسم العربي", "الرقم الوظيفي", "المسمى الوظيفي"]
                    if not all(emp.get(field) for field in required_fields):
                        incomplete_employees += 1

                if incomplete_employees > 0:
                    data_alerts.append({
                        "type": "incomplete_data",
                        "message": f"{incomplete_employees} موظف لديهم بيانات ناقصة",
                        "severity": "medium"
                    })

            # فحص بيانات الإجازات
            leaves_data = self.load_leaves_data()
            if leaves_data:
                pending_leaves = len([leave for leave in leaves_data
                                    if leave.get("الحالة", "").strip() == "قيد الانتظار"])

                if pending_leaves > 0:
                    data_alerts.append({
                        "type": "pending_leaves",
                        "message": f"{pending_leaves} إجازة في انتظار الموافقة",
                        "severity": "medium"
                    })

            return data_alerts

        except Exception as e:
            print(f"خطأ في فحص تنبيهات البيانات: {e}")
            return []

    def display_leave_alerts(self, parent_frame, leave_alerts):
        """عرض تنبيهات الإجازات"""
        # عنوان القسم
        section_title = tk.Label(parent_frame, text="🏖️ تنبيهات الإجازات:",
                               font=("Arial", 15, "bold"), fg="#e74c3c", bg="#fff5f5")
        section_title.pack(anchor="w", pady=(0, 5))

        # عرض كل تنبيه
        for leave in leave_alerts:
            days_text = "اليوم" if leave["days_remaining"] == 0 else f"خلال {leave['days_remaining']} يوم"
            alert_text = f"• {leave['employee']} - {leave['type']} - ينتهي {days_text}"

            # إطار للتنبيه
            alert_frame = tk.Frame(parent_frame, bg="#fff5f5", relief=tk.RIDGE, bd=1)
            alert_frame.pack(fill="x", padx=20, pady=2)

            color = "#e74c3c" if leave["days_remaining"] <= 1 else "#f39c12"
            alert_label = tk.Label(alert_frame, text=alert_text,
                                 font=("Arial", 12, "bold"), fg=color, bg="#fff5f5")
            alert_label.pack(anchor="w", padx=10, pady=3)

        # خط فاصل
        separator = tk.Frame(parent_frame, height=1, bg="#bdc3c7")
        separator.pack(fill="x", pady=10)

    def display_promotion_alerts(self, parent_frame, promotion_alerts):
        """عرض تنبيهات الترقيات"""
        # عنوان القسم
        section_title = tk.Label(parent_frame, text="⬆️ تنبيهات الترقيات المستحقة:",
                               font=("Arial", 15, "bold"), fg="#9b59b6", bg="#fff5f5")
        section_title.pack(anchor="w", pady=(0, 5))

        # عرض كل تنبيه
        for promo in promotion_alerts:
            alert_text = f"• {promo['employee']} ({promo['emp_id']}) - {promo['current_grade']} - {promo['years_in_grade']} سنة"

            # إطار للتنبيه
            alert_frame = tk.Frame(parent_frame, bg="#fff5f5", relief=tk.RIDGE, bd=1)
            alert_frame.pack(fill="x", padx=20, pady=2)

            alert_label = tk.Label(alert_frame, text=alert_text,
                                 font=("Arial", 12, "bold"), fg="#9b59b6", bg="#fff5f5")
            alert_label.pack(anchor="w", padx=10, pady=3)

        # خط فاصل
        separator = tk.Frame(parent_frame, height=1, bg="#bdc3c7")
        separator.pack(fill="x", pady=10)

    def display_system_alerts(self, parent_frame, system_alerts):
        """عرض تنبيهات النظام"""
        # عنوان القسم
        section_title = tk.Label(parent_frame, text="⚙️ تنبيهات النظام:",
                               font=("Arial", 15, "bold"), fg="#e67e22", bg="#fff5f5")
        section_title.pack(anchor="w", pady=(0, 5))

        # عرض كل تنبيه
        for alert in system_alerts:
            alert_text = f"• {alert['message']}"

            # إطار للتنبيه
            alert_frame = tk.Frame(parent_frame, bg="#fff5f5", relief=tk.RIDGE, bd=1)
            alert_frame.pack(fill="x", padx=20, pady=2)

            # لون حسب الأهمية
            color = "#e74c3c" if alert["severity"] == "high" else "#f39c12"
            alert_label = tk.Label(alert_frame, text=alert_text,
                                 font=("Arial", 12, "bold"), fg=color, bg="#fff5f5")
            alert_label.pack(anchor="w", padx=10, pady=3)

        # خط فاصل
        separator = tk.Frame(parent_frame, height=1, bg="#bdc3c7")
        separator.pack(fill="x", pady=10)

    def display_data_alerts(self, parent_frame, data_alerts):
        """عرض تنبيهات البيانات"""
        # عنوان القسم
        section_title = tk.Label(parent_frame, text="📊 تنبيهات البيانات:",
                               font=("Arial", 15, "bold"), fg="#3498db", bg="#fff5f5")
        section_title.pack(anchor="w", pady=(0, 5))

        # عرض كل تنبيه
        for alert in data_alerts:
            alert_text = f"• {alert['message']}"

            # إطار للتنبيه
            alert_frame = tk.Frame(parent_frame, bg="#fff5f5", relief=tk.RIDGE, bd=1)
            alert_frame.pack(fill="x", padx=20, pady=2)

            alert_label = tk.Label(alert_frame, text=alert_text,
                                 font=("Arial", 12, "bold"), fg="#3498db", bg="#fff5f5")
            alert_label.pack(anchor="w", padx=10, pady=3)

        # خط فاصل
        separator = tk.Frame(parent_frame, height=1, bg="#bdc3c7")
        separator.pack(fill="x", pady=10)

    def load_leaves_data(self):
        """تحميل بيانات الإجازات من ملف Excel"""
        try:
            from openpyxl import load_workbook

            leaves_file = "leaves_data.xlsx"
            if not os.path.exists(leaves_file):
                return []

            wb = load_workbook(leaves_file)
            # محاولة العثور على الورقة بأسماء مختلفة
            if "الإجازات" in wb.sheetnames:
                ws = wb["الإجازات"]
            elif "Leaves" in wb.sheetnames:
                ws = wb["Leaves"]
            else:
                ws = wb.active

            data = []
            headers = [cell.value for cell in ws[1]]

            for row in ws.iter_rows(min_row=2, values_only=True):
                if any(row):
                    data.append(dict(zip(headers, row)))

            return data

        except Exception as e:
            print(f"خطأ في تحميل بيانات الإجازات: {e}")
            return []

    def refresh_alerts(self):
        """تحديث جميع التنبيهات المهمة"""
        try:
            # إعادة إنشاء جميع التنبيهات المهمة
            self.create_important_alerts(self.alerts_display_frame)

            print("🔄 تم تحديث جميع التنبيهات المهمة")

        except Exception as e:
            print(f"خطأ في تحديث التنبيهات: {e}")

    def refresh_stats(self):
        """تحديث الإحصائيات"""
        try:
            # حساب الإحصائيات الجديدة
            stats_data = self.calculate_dashboard_stats()

            # تحديث القيم في الواجهة
            if hasattr(self, 'stats_labels'):
                new_values = [
                    str(stats_data["employees_count"]),
                    str(stats_data["leaves_this_month"]),
                    str(stats_data["pending_promotions"]),
                    str(stats_data["total_leaves"])
                ]

                for i, (title_label, value_label, title, bg_color, fg_color) in enumerate(self.stats_labels):
                    if i < len(new_values):
                        value_label.config(text=new_values[i])

            print("📊 تم تحديث الإحصائيات")

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def cleanup_temp_files(self):
        """تنظيف جميع الملفات المؤقتة في النظام"""
        try:
            # تنظيف ملفات نظام الطباعة
            from printing_system import PrintingSystem
            printer = PrintingSystem()
            printer.cleanup()
            print("🗑️ تم تنظيف ملفات نظام الطباعة المؤقتة")
        except Exception as e:
            print(f"⚠️ تعذر تنظيف ملفات الطباعة: {e}")

    def close_all_windows(self, exclude_window=None):
        """إغلاق جميع النوافذ المفتوحة مع إمكانية استثناء نافذة معينة"""
        try:
            # تنظيف الملفات المؤقتة قبل الإغلاق
            self.cleanup_temp_files()

            # إغلاق النوافذ مع تأثير سلس
            windows_to_close = []
            for window_name, window in list(self.open_windows.items()):
                if exclude_window and window == exclude_window:
                    continue
                windows_to_close.append((window_name, window))

            # إغلاق النوافذ بشكل متدرج
            for window_name, window in windows_to_close:
                try:
                    if window and window.winfo_exists():
                        # إضافة تأثير بصري قبل الإغلاق
                        window.withdraw()  # إخفاء النافذة أولاً
                        window.after(50, lambda w=window: self._safe_destroy_window(w))

                        # إزالة من القاموس
                        if window_name in self.open_windows:
                            del self.open_windows[window_name]

                        print(f"🔄 تم إغلاق نافذة: {window_name}")
                except Exception as e:
                    print(f"⚠️ خطأ في إغلاق النافذة {window_name}: {e}")
                    # إزالة من القاموس حتى لو فشل الإغلاق
                    if window_name in self.open_windows:
                        del self.open_windows[window_name]

        except Exception as e:
            print(f"خطأ في إغلاق النوافذ: {e}")
            # تنظيف القاموس في حالة الخطأ
            self.open_windows.clear()

    def _safe_destroy_window(self, window):
        """إغلاق آمن للنافذة"""
        try:
            if window and window.winfo_exists():
                window.destroy()
        except Exception as e:
            print(f"خطأ في إغلاق النافذة: {e}")

    def close_specific_window(self, window_name):
        """إغلاق نافذة محددة"""
        try:
            if window_name in self.open_windows:
                window = self.open_windows[window_name]
                if window and window.winfo_exists():
                    window.withdraw()
                    window.after(50, lambda: self._safe_destroy_window(window))
                del self.open_windows[window_name]
                print(f"🔄 تم إغلاق نافذة: {window_name}")
        except Exception as e:
            print(f"خطأ في إغلاق النافذة {window_name}: {e}")
            if window_name in self.open_windows:
                del self.open_windows[window_name]

    def _show_window_smoothly(self, window, window_title):
        """إظهار النافذة مع تأثير بصري سلس"""
        try:
            if window and window.winfo_exists():
                # إظهار النافذة
                window.deiconify()

                # رفع النافذة للمقدمة
                window.lift()
                window.focus_force()

                # تأثير بصري بسيط
                window.attributes('-alpha', 0.0)  # شفافية كاملة

                # تدرج الشفافية
                def fade_in(alpha=0.0):
                    if alpha < 1.0 and window.winfo_exists():
                        alpha += 0.1
                        window.attributes('-alpha', alpha)
                        window.after(30, lambda: fade_in(alpha))
                    else:
                        if window.winfo_exists():
                            window.attributes('-alpha', 1.0)

                fade_in()

                print(f"✨ تم فتح {window_title} بنجاح")

        except Exception as e:
            print(f"خطأ في إظهار النافذة: {e}")
            # في حالة فشل التأثير، إظهار النافذة بشكل عادي
            try:
                if window and window.winfo_exists():
                    window.deiconify()
                    window.lift()
                    window.focus_force()
            except:
                pass

    def get_window_status(self):
        """الحصول على حالة النوافذ المفتوحة"""
        status = {}
        for window_name, window in self.open_windows.items():
            try:
                if window and window.winfo_exists():
                    status[window_name] = "مفتوحة"
                else:
                    status[window_name] = "مغلقة"
            except:
                status[window_name] = "خطأ"
        return status

    def print_windows_status(self):
        """طباعة حالة النوافذ (للتشخيص)"""
        status = self.get_window_status()
        print("📋 حالة النوافذ:")
        for window_name, window_status in status.items():
            print(f"   {window_name}: {window_status}")
        if not status:
            print("   لا توجد نوافذ مفتوحة")

    def update_windows_status_display(self):
        """تحديث مؤشر حالة النوافذ في الواجهة"""
        try:
            if hasattr(self, 'windows_status_label'):
                status = self.get_window_status()
                open_windows = [name for name, stat in status.items() if stat == "مفتوحة"]

                if open_windows:
                    # ترجمة أسماء النوافذ للعربية
                    window_names = {
                        "employees": "👥 الموظفين",
                        "leaves": "🏖️ الإجازات",
                        "promotions": "⬆️ الترقيات",
                        "reports": "📊 التقارير",
                        "template_manager": "🎨 مدير القوالب",
                        "user_management": "🔐 إدارة المستخدمين"
                    }

                    translated_names = [window_names.get(name, name) for name in open_windows]
                    status_text = f"📋 النوافذ المفتوحة: {', '.join(translated_names)}"
                    color = "#27ae60"  # أخضر
                else:
                    status_text = "📋 النوافذ المفتوحة: لا يوجد"
                    color = "#95a5a6"  # رمادي

                self.windows_status_label.config(text=status_text, fg=color)

        except Exception as e:
            print(f"خطأ في تحديث مؤشر النوافذ: {e}")

    def _update_status_after_action(self):
        """تحديث مؤشر الحالة بعد فتح أو إغلاق نافذة"""
        # تأخير قصير للتأكد من اكتمال العملية
        if hasattr(self, 'main_root'):
            self.main_root.after(300, self.update_windows_status_display)

    def open_employees(self):
        """فتح نظام إدارة الموظفين"""
        try:
            # فحص صلاحيات المستخدم
            if not self.check_user_permission('manage_employees'):
                messagebox.showerror("خطأ في الصلاحيات",
                                   "ليس لديك صلاحية للوصول لإدارة الموظفين\n"
                                   "تحتاج إلى صلاحية: manage_employees")
                return

            # فحص إذا كانت النافذة مفتوحة بالفعل
            if "employees" in self.open_windows:
                existing_window = self.open_windows["employees"]
                if existing_window and existing_window.winfo_exists():
                    # رفع النافذة الموجودة للمقدمة
                    existing_window.lift()
                    existing_window.focus_force()
                    print("📋 نافذة إدارة الموظفين مفتوحة بالفعل")
                    return

            # إغلاق النوافذ الأخرى (ما عدا النافذة الحالية)
            self.close_all_windows()

            # إنشاء النافذة الجديدة مع تأثير بصري
            emp_window = tk.Toplevel(self.main_root)
            emp_window.title("👥 إدارة الموظفين")
            emp_window.geometry("1200x700")
            emp_window.state('zoomed')  # ملء الشاشة

            # تأثير بصري عند الفتح
            emp_window.withdraw()  # إخفاء مؤقت

            # تحسين مظهر النافذة
            emp_window.configure(bg="#f0f0f0")
            emp_window.iconify()
            emp_window.deiconify()

            # استيراد وتشغيل نظام إدارة الموظفين المحسن
            import employee_management
            emp_system = employee_management.EmployeeManagementSystem(emp_window, self.current_user)
            print("✅ تم تحميل نظام إدارة الموظفين المحسن")

            # تسجيل النافذة
            self.open_windows["employees"] = emp_window

            # إظهار النافذة مع تأثير
            emp_window.after(100, lambda: self._show_window_smoothly(emp_window, "👥 إدارة الموظفين"))

            # دالة إغلاق محسنة
            def on_close():
                self.close_specific_window("employees")
                self._update_status_after_action()

            emp_window.protocol("WM_DELETE_WINDOW", on_close)

            # تحديث مؤشر الحالة
            self._update_status_after_action()

        except Exception as e:
            messagebox.showerror("خطأ", f"تعذر فتح نظام إدارة الموظفين:\n{str(e)}")
            # تنظيف في حالة الخطأ
            if "employees" in self.open_windows:
                del self.open_windows["employees"]
    
    def open_leaves(self):
        """فتح نظام إدارة الإجازات"""
        try:
            # فحص صلاحيات المستخدم
            if not self.check_user_permission('manage_leaves'):
                messagebox.showerror("خطأ في الصلاحيات",
                                   "ليس لديك صلاحية للوصول لإدارة الإجازات\n"
                                   "تحتاج إلى صلاحية: manage_leaves")
                return
            # فحص إذا كانت النافذة مفتوحة بالفعل
            if "leaves" in self.open_windows:
                existing_window = self.open_windows["leaves"]
                if existing_window and existing_window.winfo_exists():
                    # رفع النافذة الموجودة للمقدمة
                    existing_window.lift()
                    existing_window.focus_force()
                    print("🏖️ نافذة إدارة الإجازات مفتوحة بالفعل")
                    return

            # إغلاق النوافذ الأخرى
            self.close_all_windows()

            # إنشاء النافذة الجديدة
            leave_window = tk.Toplevel(self.main_root)
            leave_window.title("🏖️ إدارة الإجازات")
            leave_window.geometry("1200x700")
            leave_window.state('zoomed')  # ملء الشاشة

            # تأثير بصري عند الفتح
            leave_window.withdraw()

            # تحسين مظهر النافذة
            leave_window.configure(bg="#f0f0f0")

            import leave_management
            leave_system = leave_management.LeaveManagementSystem(leave_window)

            # تسجيل النافذة
            self.open_windows["leaves"] = leave_window

            # إظهار النافذة مع تأثير
            leave_window.after(100, lambda: self._show_window_smoothly(leave_window, "🏖️ إدارة الإجازات"))

            # دالة إغلاق محسنة
            def on_close():
                self.close_specific_window("leaves")

            leave_window.protocol("WM_DELETE_WINDOW", on_close)

        except Exception as e:
            messagebox.showerror("خطأ", f"تعذر فتح نظام إدارة الإجازات:\n{str(e)}")
            if "leaves" in self.open_windows:
                del self.open_windows["leaves"]

    def open_leave_balance(self):
        """فتح نظام إدارة رصيد الإجازات السنوي"""
        try:
            # فحص صلاحيات المستخدم
            if not self.check_user_permission('manage_leaves'):
                messagebox.showerror("خطأ في الصلاحيات",
                                   "ليس لديك صلاحية للوصول لإدارة رصيد الإجازات\n"
                                   "تحتاج إلى صلاحية: manage_leaves")
                return

            # فحص إذا كانت النافذة مفتوحة بالفعل
            if "leave_balance" in self.open_windows:
                existing_window = self.open_windows["leave_balance"]
                if existing_window and existing_window.winfo_exists():
                    # رفع النافذة الموجودة للمقدمة
                    existing_window.lift()
                    existing_window.focus_force()
                    print("💰 نافذة رصيد الإجازات مفتوحة بالفعل")
                    return

            # إغلاق النوافذ الأخرى
            self.close_all_windows()

            # إنشاء النافذة الجديدة
            balance_window = tk.Toplevel(self.main_root)
            balance_window.title("💰 إدارة رصيد الإجازات السنوي")
            balance_window.geometry("1400x800")
            balance_window.state('zoomed')  # ملء الشاشة

            # تأثير بصري عند الفتح
            balance_window.withdraw()

            # تحسين مظهر النافذة
            balance_window.configure(bg="#f0f0f0")

            # استيراد وتشغيل نظام رصيد الإجازات
            import leave_balance_system
            balance_system = leave_balance_system.LeaveBalanceSystem(balance_window)

            # تسجيل النافذة
            self.open_windows["leave_balance"] = balance_window

            # إظهار النافذة مع تأثير
            balance_window.after(100, lambda: self._show_window_smoothly(balance_window, "💰 رصيد الإجازات السنوي"))

            # دالة إغلاق محسنة
            def on_close():
                self.close_specific_window("leave_balance")

            balance_window.protocol("WM_DELETE_WINDOW", on_close)

        except Exception as e:
            messagebox.showerror("خطأ", f"تعذر فتح نظام رصيد الإجازات:\n{str(e)}")
            if "leave_balance" in self.open_windows:
                del self.open_windows["leave_balance"]
    
    def open_promotions(self):
        """فتح نظام الترقيات"""
        try:
            # فحص صلاحيات المستخدم
            if not self.check_user_permission('manage_promotions'):
                messagebox.showerror("خطأ في الصلاحيات",
                                   "ليس لديك صلاحية للوصول لنظام الترقيات\n"
                                   "تحتاج إلى صلاحية: manage_promotions")
                return
            # فحص إذا كانت النافذة مفتوحة بالفعل
            if "promotions" in self.open_windows:
                existing_window = self.open_windows["promotions"]
                if existing_window and existing_window.winfo_exists():
                    # رفع النافذة الموجودة للمقدمة
                    existing_window.lift()
                    existing_window.focus_force()
                    print("⬆️ نافذة نظام الترقيات مفتوحة بالفعل")
                    return

            # إغلاق النوافذ الأخرى
            self.close_all_windows()

            # إنشاء النافذة الجديدة
            promo_window = tk.Toplevel(self.main_root)
            promo_window.title("⬆️ نظام الترقيات")
            promo_window.geometry("1200x700")
            promo_window.state('zoomed')  # ملء الشاشة

            # تأثير بصري عند الفتح
            promo_window.withdraw()

            # تحسين مظهر النافذة
            promo_window.configure(bg="#f0f0f0")

            try:
                import promotion_system_safe
                promo_system = promotion_system_safe.PromotionSystem(promo_window)
                print("✅ تم تحميل نظام الترقيات الآمن")
            except Exception as e:
                print(f"❌ فشل في تحميل نظام الترقيات الآمن: {e}")
                try:
                    import promotion_system
                    promo_system = promotion_system.PromotionSystem(promo_window)
                    print("✅ تم تحميل نظام الترقيات الأصلي")
                except Exception as e2:
                    messagebox.showerror("خطأ", f"فشل في تحميل نظام الترقيات:\n{e2}")
                    promo_window.destroy()
                    return

            # تسجيل النافذة
            self.open_windows["promotions"] = promo_window

            # إظهار النافذة مع تأثير
            promo_window.after(100, lambda: self._show_window_smoothly(promo_window, "⬆️ نظام الترقيات"))

            # دالة إغلاق محسنة
            def on_close():
                self.close_specific_window("promotions")

            promo_window.protocol("WM_DELETE_WINDOW", on_close)

        except Exception as e:
            messagebox.showerror("خطأ", f"تعذر فتح نظام الترقيات:\n{str(e)}")
            if "promotions" in self.open_windows:
                del self.open_windows["promotions"]
    

    def open_reports(self):
        """فتح نظام التقارير"""
        try:
            # فحص صلاحيات المستخدم
            if not self.check_user_permission('manage_reports'):
                messagebox.showerror("خطأ في الصلاحيات",
                                   "ليس لديك صلاحية للوصول للتقارير والإحصائيات\n"
                                   "تحتاج إلى صلاحية: manage_reports")
                return
            # فحص إذا كانت النافذة مفتوحة بالفعل
            if "reports" in self.open_windows:
                existing_window = self.open_windows["reports"]
                if existing_window and existing_window.winfo_exists():
                    # رفع النافذة الموجودة للمقدمة
                    existing_window.lift()
                    existing_window.focus_force()
                    print("📊 نافذة التقارير والإحصائيات مفتوحة بالفعل")
                    return

            # إغلاق النوافذ الأخرى
            self.close_all_windows()

            # إنشاء النافذة الجديدة
            reports_window = tk.Toplevel(self.main_root)
            reports_window.title("📊 التقارير والإحصائيات")
            reports_window.geometry("1400x900")
            reports_window.state('zoomed')  # ملء الشاشة

            # تأثير بصري عند الفتح
            reports_window.withdraw()

            # تحسين مظهر النافذة
            reports_window.configure(bg="#f0f0f0")

            import reports_system
            reports_sys = reports_system.ReportsSystem(reports_window)

            # تسجيل النافذة
            self.open_windows["reports"] = reports_window

            # إظهار النافذة مع تأثير
            reports_window.after(100, lambda: self._show_window_smoothly(reports_window, "📊 التقارير والإحصائيات"))

            # دالة إغلاق محسنة
            def on_close():
                self.close_specific_window("reports")

            reports_window.protocol("WM_DELETE_WINDOW", on_close)

        except Exception as e:
            messagebox.showerror("خطأ", f"تعذر فتح نظام التقارير:\n{str(e)}")
            if "reports" in self.open_windows:
                del self.open_windows["reports"]

    def open_template_manager(self):
        """فتح مدير قوالب المراسلات في لوحة التحكم الرئيسية"""
        try:
            # فحص صلاحيات المستخدم
            if not self.check_user_permission('manage_templates'):
                messagebox.showerror("خطأ في الصلاحيات",
                                   "ليس لديك صلاحية للوصول لمدير قوالب المراسلات\n"
                                   "تحتاج إلى صلاحية: manage_templates")
                return
            # فحص إذا كانت النافذة مفتوحة بالفعل
            if "template_manager" in self.open_windows:
                existing_window = self.open_windows["template_manager"]
                if existing_window and existing_window.winfo_exists():
                    # رفع النافذة الموجودة للمقدمة
                    existing_window.lift()
                    existing_window.focus_force()
                    print("🎨 نافذة مدير قوالب المراسلات مفتوحة بالفعل")
                    return

            # إغلاق النوافذ الأخرى
            self.close_all_windows()

            # إنشاء النافذة الجديدة
            template_window = tk.Toplevel(self.main_root)
            template_window.title("🎨 مدير قوالب المراسلات")
            template_window.geometry("1400x900")
            template_window.state('zoomed')  # ملء الشاشة

            # تأثير بصري عند الفتح
            template_window.withdraw()

            # تحسين مظهر النافذة
            template_window.configure(bg="#f0f0f0")

            # إنشاء مدير القوالب
            from template_manager import TemplateManager
            template_manager = TemplateManager(template_window)

            # تسجيل النافذة
            self.open_windows["template_manager"] = template_window

            # إظهار النافذة مع تأثير
            template_window.after(100, lambda: self._show_window_smoothly(template_window, "🎨 مدير قوالب المراسلات"))

            # دالة إغلاق محسنة
            def on_close():
                self.close_specific_window("template_manager")
                self._update_status_after_action()

            template_window.protocol("WM_DELETE_WINDOW", on_close)

            # تحديث مؤشر الحالة
            self._update_status_after_action()

        except Exception as e:
            messagebox.showerror("خطأ", f"تعذر فتح مدير قوالب المراسلات:\n{str(e)}")
            if "template_manager" in self.open_windows:
                del self.open_windows["template_manager"]

    def open_user_management(self):
        """فتح نظام إدارة المستخدمين (للمشرف العام فقط)"""
        print("🔐 محاولة فتح نظام إدارة المستخدمين...")

        try:
            # التحقق من صلاحيات المستخدم
            print(f"🔍 فحص صلاحيات المستخدم: {self.current_user}")

            if not self.check_super_admin_permissions():
                print(f"❌ المستخدم {self.current_user} ليس مشرف عام")
                messagebox.showerror("خطأ في الصلاحيات",
                                   f"هذه الوظيفة متاحة للمشرف العام فقط\n"
                                   f"المستخدم الحالي: {self.current_user}\n"
                                   f"يجب تسجيل الدخول باسم: يوسف")
                return

            print("✅ تم التحقق من الصلاحيات بنجاح")

            # فحص إذا كانت النافذة مفتوحة بالفعل
            if "user_management" in self.open_windows:
                existing_window = self.open_windows["user_management"]
                if existing_window and existing_window.winfo_exists():
                    # رفع النافذة الموجودة للمقدمة
                    existing_window.lift()
                    existing_window.focus_force()
                    print("🔐 نافذة إدارة المستخدمين مفتوحة بالفعل")
                    return

            # إغلاق النوافذ الأخرى
            print("🔄 إغلاق النوافذ الأخرى...")
            self.close_all_windows()

            # إنشاء النافذة الجديدة
            print("🪟 إنشاء نافذة إدارة المستخدمين...")
            user_window = tk.Toplevel(self.main_root)
            user_window.title("🔐 إدارة المستخدمين")
            user_window.geometry("1400x900")
            user_window.state('zoomed')  # ملء الشاشة

            # تأثير بصري عند الفتح
            user_window.withdraw()

            # تحسين مظهر النافذة
            user_window.configure(bg="#f0f0f0")

            # إنشاء نظام إدارة المستخدمين
            print("📦 استيراد وإنشاء نظام إدارة المستخدمين...")
            try:
                from user_management import UserManagement
                print("✅ تم استيراد UserManagement بنجاح")

                # استخراج اسم المستخدم من القاموس
                if isinstance(self.current_user, dict):
                    username = self.current_user.get('username', '')
                else:
                    username = str(self.current_user)

                user_manager = UserManagement(user_window, username)
                print("✅ تم إنشاء UserManagement بنجاح")

            except Exception as import_error:
                print(f"❌ خطأ في استيراد أو إنشاء UserManagement: {import_error}")
                user_window.destroy()
                raise import_error

            # تسجيل النافذة
            self.open_windows["user_management"] = user_window
            print("📋 تم تسجيل النافذة في النظام")

            # إظهار النافذة مع تأثير
            user_window.after(100, lambda: self._show_window_smoothly(user_window, "🔐 إدارة المستخدمين"))
            print("🎉 تم إظهار النافذة بنجاح")

            # دالة إغلاق محسنة
            def on_close():
                print("🔒 إغلاق نافذة إدارة المستخدمين...")
                self.close_specific_window("user_management")
                self._update_status_after_action()

            user_window.protocol("WM_DELETE_WINDOW", on_close)

            # تحديث مؤشر الحالة
            self._update_status_after_action()

        except Exception as e:
            print(f"❌ خطأ في فتح نظام إدارة المستخدمين: {e}")
            import traceback
            print("📋 تفاصيل الخطأ:")
            traceback.print_exc()

            messagebox.showerror("خطأ", f"تعذر فتح نظام إدارة المستخدمين:\n{str(e)}\n\nراجع الكونسول للتفاصيل")
            if "user_management" in self.open_windows:
                del self.open_windows["user_management"]

    def check_super_admin_permissions(self):
        """التحقق من صلاحيات المشرف العام"""
        try:
            print(f"🔍 فحص صلاحيات المستخدم: {self.current_user}")

            # استخراج اسم المستخدم من القاموس أو النص
            if isinstance(self.current_user, dict):
                username = self.current_user.get('username', '')
                current_role = self.current_user.get('role', '')
                print(f"👤 اسم المستخدم من القاموس: {username}")
                print(f"🎭 الدور الحالي: {current_role}")
            else:
                username = str(self.current_user)
                print(f"👤 اسم المستخدم: {username}")

            import json
            import os

            # فحص وجود ملف المستخدمين
            if not os.path.exists("users.json"):
                print("❌ ملف users.json غير موجود")
                return False

            with open("users.json", 'r', encoding='utf-8') as f:
                users_data = json.load(f)

            print(f"📋 تم تحميل بيانات {len(users_data)} مستخدم")
            print(f"📋 المستخدمون المتاحون: {list(users_data.keys())}")

            if username in users_data:
                user_info = users_data[username]
                user_role = user_info.get('role', '')
                print(f"👤 دور المستخدم {username}: {user_role}")

                is_super_admin = user_role == 'super_admin'
                print(f"🔐 هل هو مشرف عام؟ {is_super_admin}")

                return is_super_admin
            else:
                print(f"❌ المستخدم {username} غير موجود في قاعدة البيانات")
                return False

        except Exception as e:
            print(f"❌ خطأ في فحص الصلاحيات: {e}")
            import traceback
            traceback.print_exc()
            return False

    def check_user_permission(self, permission_name):
        """التحقق من صلاحية محددة للمستخدم"""
        try:
            print(f"🔍 فحص صلاحية '{permission_name}' للمستخدم: {self.current_user}")

            # استخراج اسم المستخدم من القاموس أو النص
            if isinstance(self.current_user, dict):
                username = self.current_user.get('username', '')
                current_role = self.current_user.get('role', '')
                print(f"👤 اسم المستخدم من القاموس: {username}")
                print(f"🎭 الدور الحالي: {current_role}")
            else:
                username = str(self.current_user)
                print(f"👤 اسم المستخدم: {username}")

            import json
            import os

            # فحص وجود ملف المستخدمين
            if not os.path.exists("users.json"):
                print("❌ ملف users.json غير موجود")
                return False

            with open("users.json", 'r', encoding='utf-8') as f:
                users_data = json.load(f)

            if username in users_data:
                user_info = users_data[username]
                user_role = user_info.get('role', '')
                permissions = user_info.get('permissions', {})

                print(f"👤 دور المستخدم {username}: {user_role}")
                print(f"📋 صلاحيات المستخدم: {permissions}")

                # إذا كان مشرف عام، له جميع الصلاحيات
                if user_role == 'super_admin':
                    print(f"✅ مشرف عام - صلاحية '{permission_name}' مسموحة")
                    return True

                # فحص الصلاحية المحددة
                has_permission = permissions.get(permission_name, False)
                print(f"🔐 صلاحية '{permission_name}': {has_permission}")

                return has_permission
            else:
                print(f"❌ المستخدم {username} غير موجود في قاعدة البيانات")
                return False

        except Exception as e:
            print(f"❌ خطأ في فحص الصلاحية '{permission_name}': {e}")
            import traceback
            traceback.print_exc()
            return False



    def logout(self):
        """تسجيل الخروج مع إمكانية العودة لشاشة تسجيل الدخول"""
        if messagebox.askyesno("تسجيل الخروج", "هل أنت متأكد من تسجيل الخروج؟\n\nسيتم إغلاق النظام والعودة لشاشة تسجيل الدخول"):
            print("🚪 بدء عملية تسجيل الخروج...")

            # طباعة حالة النوافذ قبل الإغلاق
            self.print_windows_status()

            # تنظيف الملفات المؤقتة
            self.cleanup_temp_files()

            # إغلاق جميع النوافذ المفتوحة بشكل سلس
            self.close_all_windows()

            # إخفاء النافذة الرئيسية
            self.main_root.withdraw()

            # انتظار قصير للتأكد من إغلاق النوافذ
            self.main_root.after(200, self._complete_logout)

    def _complete_logout(self):
        """إكمال عملية تسجيل الخروج والعودة لشاشة تسجيل الدخول"""
        try:
            print("🔄 إغلاق النافذة الرئيسية...")
            self.main_root.destroy()
            print("✅ تم تسجيل الخروج بنجاح")

            # إعادة تعيين المستخدم الحالي
            self.current_user = None

            # إعادة تعيين النوافذ المفتوحة
            self.open_windows = {}

            # العودة لشاشة تسجيل الدخول
            print("🔄 العودة لشاشة تسجيل الدخول...")
            self.show_login()

        except Exception as e:
            print(f"خطأ في إكمال تسجيل الخروج: {e}")
            # محاولة إغلاق قسري
            try:
                self.main_root.quit()
            except:
                pass
            # في حالة الفشل، إظهار شاشة تسجيل الدخول
            try:
                self.show_login()
            except:
                pass

    def show_system_monitor(self):
        """عرض مراقب النظام"""
        try:
            from system_monitor import show_system_monitor
            show_system_monitor(self.main_root)
        except ImportError:
            messagebox.showinfo("مراقب النظام",
                              "مراقب النظام المحسن غير متاح\n"
                              "يرجى التأكد من وجود ملف system_monitor.py")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تشغيل مراقب النظام: {e}")

    def show_help(self):
        """عرض المساعدة"""
        help_window = tk.Toplevel(self.main_root)
        help_window.title("المساعدة")
        help_window.geometry("600x400")
        help_window.grab_set()
        
        help_text = tk.Text(help_window, wrap=tk.WORD, font=("Arial", 11))
        help_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        help_content = """
مرحباً بك في منظومة إدارة الموارد البشرية

الوحدات المتاحة:

1. إدارة الموظفين:
   - إضافة وتعديل وحذف بيانات الموظفين
   - البحث والتصفية
   - تصدير البيانات

2. إدارة الإجازات:
   - تسجيل إجازات جديدة
   - موافقة أو رفض الإجازات
   - عرض تقارير الإجازات

3. نظام الترقيات:
   - حساب الترقيات المستحقة
   - عرض تقارير الترقيات

4. طباعة النماذج:
   - إنشاء نماذج المباشرة والمغادرة
   - طباعة الوثائق الرسمية

5. التقارير والإحصائيات:
   - تقارير شاملة
   - إحصائيات متقدمة

للمساعدة الإضافية، راجع دليل الاستخدام.
        """
        
        help_text.insert(tk.END, help_content)
        help_text.config(state=tk.DISABLED)
        
        close_btn = tk.Button(help_window, text="إغلاق", command=help_window.destroy)
        close_btn.pack(pady=10)

def initialize_system():
    """تهيئة النظام عند التشغيل لأول مرة (بدون إنشاء نوافذ)"""
    try:
        # استخدام الأدوات الآمنة لتهيئة القوالب
        from template_utils import safe_template_initialization
        safe_template_initialization()
        print("✅ تم تهيئة نظام القوالب")
    except ImportError:
        # في حالة عدم وجود template_utils، استخدم الطريقة الأساسية
        try:
            templates_dir = "correspondence_templates"
            if not os.path.exists(templates_dir):
                os.makedirs(templates_dir)
            print("✅ تم تهيئة نظام القوالب")
        except Exception as e:
            print(f"⚠️ تحذير: فشل في تهيئة القوالب: {e}")
    except Exception as e:
        print(f"⚠️ تحذير: فشل في تهيئة القوالب: {e}")
        print("💡 يمكنك تشغيل: python initialize_templates.py")

def cleanup_on_exit():
    """تنظيف شامل عند إغلاق النظام"""
    try:
        from printing_system import PrintingSystem
        printer = PrintingSystem()
        printer.cleanup()
        print("🗑️ تم تنظيف جميع الملفات المؤقتة عند الخروج")
    except:
        pass

def main():
    """تشغيل النظام"""
    try:
        # تسجيل دالة التنظيف عند الخروج
        import atexit
        atexit.register(cleanup_on_exit)

        # تهيئة النظام
        initialize_system()

        # إخفاء الكونسول بعد التهيئة الناجحة
        hide_console()

        # تشغيل النظام الرئيسي
        hr_system = HRMainSystem()
        hr_system.show_login()
    except Exception as e:
        # إظهار الكونسول عند حدوث خطأ
        show_console()
        print(f"خطأ في تشغيل النظام: {e}")
        input("اضغط Enter للخروج...")
    finally:
        # تنظيف نهائي
        cleanup_on_exit()

if __name__ == "__main__":
    main()
