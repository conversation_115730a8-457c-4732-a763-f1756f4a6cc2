#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح زر البحث
Test Search Button Fix
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_search_button():
    """اختبار زر البحث"""
    print("🔍 اختبار زر البحث")
    print("=" * 50)
    
    try:
        # استيراد النظام
        print("📦 استيراد نظام إدارة الموظفين...")
        import employee_management
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة اختبار...")
        root = tk.Tk()
        root.title("اختبار زر البحث")
        root.geometry("1200x800")
        
        # إنشاء النظام
        print("🔧 إنشاء نظام إدارة الموظفين...")
        current_user = {"username": "test", "name": "مستخدم اختبار"}
        emp_system = employee_management.EmployeeManagementSystem(root, current_user)
        print("✅ تم إنشاء النظام بنجاح")
        
        # اختبار زر البحث مع حقل فارغ
        print("\n🧪 اختبار 1: زر البحث مع حقل فارغ")
        try:
            emp_system.search_var.set("")  # تأكد من أن الحقل فارغ
            emp_system.perform_search()  # محاكاة الضغط على زر البحث
            print("✅ زر البحث مع حقل فارغ: يعرض رسالة تنبيه")
        except Exception as e:
            print(f"❌ خطأ في اختبار زر البحث مع حقل فارغ: {e}")
        
        # اختبار زر البحث مع نص
        print("\n🧪 اختبار 2: زر البحث مع نص")
        try:
            emp_system.search_var.set("أحمد")  # إدخال نص للبحث
            emp_system.perform_search()  # محاكاة الضغط على زر البحث
            print("✅ زر البحث مع نص: يعمل بشكل صحيح")
        except Exception as e:
            print(f"❌ خطأ في اختبار زر البحث مع نص: {e}")
        
        # اختبار البحث التلقائي
        print("\n🧪 اختبار 3: البحث التلقائي")
        try:
            emp_system.search_var.set("")  # مسح الحقل
            emp_system.auto_search()  # محاكاة البحث التلقائي
            print("✅ البحث التلقائي مع حقل فارغ: يعرض جميع البيانات")
            
            emp_system.search_var.set("أحمد")  # إدخال نص
            emp_system.auto_search()  # محاكاة البحث التلقائي
            print("✅ البحث التلقائي مع نص: يعمل بشكل صحيح")
        except Exception as e:
            print(f"❌ خطأ في اختبار البحث التلقائي: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 انتهى الاختبار - النظام جاهز للاستخدام!")
        
        # إضافة تعليمات للمستخدم
        instructions_frame = tk.Frame(root, bg="#f0f0f0")
        instructions_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
        
        instructions_text = """
📝 تعليمات الاختبار:
1. اتركي حقل البحث فارغاً واضغطي زر "🔍 بحث" - يجب أن تظهر رسالة تنبيه
2. اكتبي نص في حقل البحث واضغطي زر "🔍 بحث" - يجب أن تظهر النتائج
3. اكتبي نص في حقل البحث (بدون الضغط على الزر) - يجب أن تظهر النتائج تلقائياً
4. امسحي النص من حقل البحث - يجب أن تظهر جميع البيانات تلقائياً
        """
        
        instructions_label = tk.Label(instructions_frame, text=instructions_text,
                                    bg="#f0f0f0", fg="#2c3e50",
                                    font=("Arial", 10), justify=tk.LEFT)
        instructions_label.pack(anchor=tk.W)
        
        # إضافة زر إغلاق سريع
        close_btn = tk.Button(root, text="❌ إغلاق الاختبار", 
                             command=root.destroy,
                             bg="#e74c3c", fg="white", 
                             font=("Arial", 12, "bold"))
        close_btn.pack(side=tk.BOTTOM, pady=10)
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def test_search_functions_separately():
    """اختبار دوال البحث منفصلة"""
    print("\n🔍 اختبار دوال البحث منفصلة")
    print("-" * 40)
    
    # محاكاة دالة البحث العادية (زر البحث)
    def mock_perform_search(search_term):
        """محاكاة دالة البحث العادية"""
        search_term = search_term.strip().lower()
        print(f"🔍 البحث عن: '{search_term}'")
        
        if not search_term:
            print("⚠️ لا يوجد نص بحث - عرض رسالة تنبيه")
            return "تنبيه: الرجاء إدخال نص للبحث"
        
        # محاكاة البحث
        mock_results = ["أحمد محمد", "فاطمة أحمد"] if "أحمد" in search_term else []
        print(f"📊 نتائج البحث: {len(mock_results)}")
        return f"تم العثور على {len(mock_results)} نتيجة"
    
    # محاكاة دالة البحث التلقائي
    def mock_auto_search(search_term):
        """محاكاة دالة البحث التلقائي"""
        search_term = search_term.strip().lower()
        print(f"🔍 البحث التلقائي عن: '{search_term}'")
        
        if not search_term:
            print("📋 لا يوجد نص بحث - عرض جميع البيانات")
            return "عرض جميع الموظفين (9)"
        
        # محاكاة البحث
        mock_results = ["أحمد محمد", "فاطمة أحمد"] if "أحمد" in search_term else []
        print(f"📊 نتائج البحث التلقائي: {len(mock_results)}")
        return f"تم العثور على {len(mock_results)} نتيجة"
    
    # اختبارات مختلفة
    test_cases = [
        ("", "حقل فارغ"),
        ("أحمد", "نص موجود"),
        ("غير موجود", "نص غير موجود")
    ]
    
    for search_term, description in test_cases:
        print(f"\n📝 اختبار: {description}")
        print("  🔍 زر البحث:")
        result1 = mock_perform_search(search_term)
        print(f"    النتيجة: {result1}")
        
        print("  🔄 البحث التلقائي:")
        result2 = mock_auto_search(search_term)
        print(f"    النتيجة: {result2}")

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار إصلاح زر البحث")
    print("=" * 50)
    
    # اختبار الدوال منفصلة أولاً
    test_search_functions_separately()
    
    print("\n" + "=" * 50)
    
    # اختبار النظام الكامل
    response = input("هل تريد اختبار النظام الكامل؟ (y/n): ")
    if response.lower() in ['y', 'yes', 'نعم', 'ن']:
        test_search_button()
    else:
        print("🏁 انتهى الاختبار")

if __name__ == "__main__":
    main()
