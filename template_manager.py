#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قوالب المراسلات
يدعم إنشاء وتعديل قوالب Word مع متغيرات قابلة للسحب والإفلات
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import os
from datetime import datetime
from reference_data import get_bank_names, get_nationalities, get_qualifications, get_work_places

# فحص توفر مكتبة python-docx
try:
    from docx import Document
    from docx.shared import Inches, Pt
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    print("تحذير: مكتبة python-docx غير مثبتة")

class TemplateManager:
    def __init__(self, parent=None):
        if parent is None:
            self.root = tk.Tk()
            self.root.title("🎨 مدير قوالب المراسلات")
            self.root.geometry("1200x800")
            self.parent = self.root
        else:
            self.parent = parent
            
        self.templates_dir = "correspondence_templates"
        self.current_template_path = None
        
        # إنشاء مجلد القوالب
        if not os.path.exists(self.templates_dir):
            os.makedirs(self.templates_dir)
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل القوالب المتاحة
        self.load_templates_list()

    def create_interface(self):
        """إنشاء واجهة مدير القوالب"""
        # إطار رئيسي
        main_frame = tk.Frame(self.parent, bg="#f0f0f0")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # العنوان
        title_frame = tk.Frame(main_frame, bg="#8e44ad", relief=tk.RAISED, bd=3)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        title_label = tk.Label(title_frame, text="🎨 مدير قوالب المراسلات", 
                              font=("Arial", 20, "bold"), fg="white", bg="#8e44ad")
        title_label.pack(pady=15)

        # إطار المحتوى الرئيسي
        content_frame = tk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # الجانب الأيسر - قائمة القوالب وأدوات الإدارة
        left_frame = tk.LabelFrame(content_frame, text="📋 إدارة القوالب", 
                                 font=("Arial", 14, "bold"), fg="#2c3e50",
                                 padx=15, pady=15, relief=tk.GROOVE, bd=2)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        # قائمة القوالب
        templates_frame = tk.LabelFrame(left_frame, text="القوالب المتاحة", 
                                      font=("Arial", 12, "bold"))
        templates_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        self.templates_listbox = tk.Listbox(templates_frame, font=("Arial", 11))
        templates_scroll = tk.Scrollbar(templates_frame, orient=tk.VERTICAL, 
                                      command=self.templates_listbox.yview)
        self.templates_listbox.configure(yscrollcommand=templates_scroll.set)
        self.templates_listbox.bind("<<ListboxSelect>>", self.on_template_selected)
        
        self.templates_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        templates_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # أزرار إدارة القوالب
        buttons_frame = tk.Frame(left_frame)
        buttons_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Button(buttons_frame, text="📝 قالب جديد", 
                 command=self.create_new_template,
                 bg="#27ae60", fg="white", font=("Arial", 10, "bold")).pack(fill=tk.X, pady=2)

        tk.Button(buttons_frame, text="📂 استيراد قالب", 
                 command=self.import_template,
                 bg="#3498db", fg="white", font=("Arial", 10, "bold")).pack(fill=tk.X, pady=2)

        tk.Button(buttons_frame, text="✏️ تعديل القالب", 
                 command=self.edit_template,
                 bg="#f39c12", fg="white", font=("Arial", 10, "bold")).pack(fill=tk.X, pady=2)

        tk.Button(buttons_frame, text="🗑️ حذف القالب", 
                 command=self.delete_template,
                 bg="#e74c3c", fg="white", font=("Arial", 10, "bold")).pack(fill=tk.X, pady=2)

        # المتغيرات المتاحة
        variables_frame = tk.LabelFrame(left_frame, text="🔧 المتغيرات المتاحة", 
                                      font=("Arial", 12, "bold"))
        variables_frame.pack(fill=tk.X, pady=(0, 15))

        # قائمة المتغيرات
        self.variables_listbox = tk.Listbox(variables_frame, height=8, font=("Arial", 10))
        variables_scroll = tk.Scrollbar(variables_frame, orient=tk.VERTICAL, 
                                      command=self.variables_listbox.yview)
        self.variables_listbox.configure(yscrollcommand=variables_scroll.set)
        
        self.variables_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        variables_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # تحديث قائمة المتغيرات
        self.update_variables_list()

        # الجانب الأيمن - معاينة القالب ونظام المراسلات
        right_frame = tk.LabelFrame(content_frame, text="👁️ معاينة القالب ونظام المراسلات",
                                  font=("Arial", 14, "bold"), fg="#2c3e50",
                                  padx=15, pady=15, relief=tk.GROOVE, bd=2)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))

        # إنشاء Notebook للتبويبات
        self.notebook = ttk.Notebook(right_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # تبويب معاينة القالب
        preview_tab = tk.Frame(self.notebook)
        self.notebook.add(preview_tab, text="👁️ معاينة القالب")

        # معاينة القالب
        self.preview_text = tk.Text(preview_tab, wrap=tk.WORD, font=("Arial", 12),
                                  state=tk.DISABLED)
        preview_scroll = tk.Scrollbar(preview_tab, orient=tk.VERTICAL,
                                   command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=preview_scroll.set)

        self.preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        preview_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # تبويب نظام المراسلات
        correspondence_tab = tk.Frame(self.notebook)
        self.notebook.add(correspondence_tab, text="📝 إنشاء مراسلة")

        # إنشاء نظام المراسلات
        self.create_correspondence_system(correspondence_tab)

        # إطار الحالة
        status_frame = tk.Frame(main_frame, bg="#ecf0f1", relief=tk.SUNKEN, bd=1)
        status_frame.pack(fill=tk.X, pady=(20, 0))

        self.status_label = tk.Label(status_frame, text="جاهز لإنشاء وتعديل القوالب",
                                   font=("Arial", 11), bg="#ecf0f1", fg="#2c3e50")
        self.status_label.pack(pady=10)

    def create_correspondence_system(self, parent_frame):
        """إنشاء نظام المراسلات داخل التبويب"""
        # إطار اختيار الموظف
        employee_frame = tk.LabelFrame(parent_frame, text="👤 اختيار الموظف",
                                     font=("Arial", 12, "bold"))
        employee_frame.pack(fill=tk.X, padx=10, pady=5)

        # زر اختيار الموظف
        select_employee_frame = tk.Frame(employee_frame)
        select_employee_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Button(select_employee_frame, text="🔍 اختيار موظف",
                 command=self.select_employee_for_correspondence,
                 bg="#3498db", fg="white", font=("Arial", 12, "bold")).pack(side=tk.LEFT)

        # عرض بيانات الموظف المختار
        self.selected_employee_label = tk.Label(select_employee_frame,
                                               text="لم يتم اختيار موظف",
                                               font=("Arial", 11), fg="#7f8c8d")
        self.selected_employee_label.pack(side=tk.LEFT, padx=(10, 0))

        # إطار خيارات المراسلة
        options_frame = tk.LabelFrame(parent_frame, text="⚙️ خيارات المراسلة",
                                    font=("Arial", 12, "bold"))
        options_frame.pack(fill=tk.X, padx=10, pady=5)

        # خيارات الفتح
        self.auto_open_var = tk.BooleanVar(master=self.parent, value=True)
        tk.Checkbutton(options_frame, text="فتح المستند بعد الإنشاء للمراجعة والحفظ",
                      variable=self.auto_open_var, font=("Arial", 11)).pack(anchor="w", padx=10)

        # ملاحظة توضيحية
        note_label = tk.Label(options_frame,
                            text="💡 سيتم إنشاء المستند كملف مؤقت وفتحه في Word\n"
                                 "يمكنك مراجعته وحفظه في المكان الذي تختاره باستخدام 'حفظ باسم'",
                            font=("Arial", 9), fg="#666666", justify="right")
        note_label.pack(anchor="w", padx=10, pady=5)

        # إطار أزرار الإنشاء
        create_frame = tk.LabelFrame(parent_frame, text="📄 إنشاء المراسلة",
                                   font=("Arial", 12, "bold"))
        create_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # أزرار الإنشاء
        buttons_frame = tk.Frame(create_frame)
        buttons_frame.pack(expand=True, pady=20)

        tk.Button(buttons_frame, text="📄 إنشاء المراسلة",
                 command=self.create_correspondence_document,
                 bg="#27ae60", fg="white", font=("Arial", 14, "bold"),
                 width=20, height=2).pack(pady=5)

        tk.Button(buttons_frame, text="👁️ معاينة قبل الإنشاء",
                 command=self.preview_correspondence_document,
                 bg="#95a5a6", fg="white", font=("Arial", 12),
                 width=20).pack(pady=5)

        # متغير لحفظ بيانات الموظف المختار
        self.selected_employee_data = None

    def load_employees_from_excel(self):
        """تحميل بيانات الموظفين من ملف Excel"""
        try:
            # محاولة استيراد openpyxl
            try:
                from openpyxl import load_workbook
            except ImportError:
                messagebox.showerror("خطأ", "مكتبة openpyxl غير مثبتة\nالرجاء تثبيتها باستخدام: pip install openpyxl")
                return {}

            excel_file = "employees_data.xlsx"

            if not os.path.exists(excel_file):
                messagebox.showwarning("تحذير", f"ملف البيانات غير موجود: {excel_file}")
                return {}

            # تحميل البيانات من Excel
            wb = load_workbook(excel_file)

            # البحث عن ورقة العمل المناسبة
            sheet_name = None
            possible_sheet_names = ["الموظفين", "Employees", "employees", "Sheet1"]

            for name in possible_sheet_names:
                if name in wb.sheetnames:
                    sheet_name = name
                    break

            if not sheet_name:
                messagebox.showwarning("تحذير",
                    f"لم يتم العثور على ورقة عمل مناسبة في الملف\n"
                    f"الأوراق المتاحة: {', '.join(wb.sheetnames)}\n"
                    f"الأوراق المدعومة: {', '.join(possible_sheet_names)}")
                return {}

            ws = wb[sheet_name]

            # قراءة العناوين من الصف الأول
            headers = [cell.value for cell in ws[1] if cell.value]

            if not headers:
                messagebox.showwarning("تحذير", "لا توجد عناوين في ورقة العمل")
                return {}

            # قراءة البيانات
            employees_data = {}
            for row_num, row in enumerate(ws.iter_rows(min_row=2, values_only=True), start=1):
                if any(row):  # تجاهل الصفوف الفارغة
                    # إنشاء قاموس البيانات مع التأكد من عدم تجاوز عدد العناوين
                    emp_data = {}
                    for i, value in enumerate(row):
                        if i < len(headers) and headers[i]:
                            emp_data[headers[i]] = value if value is not None else ""

                    # استخدام الرقم الوظيفي كمفتاح
                    emp_id = emp_data.get("الرقم الوظيفي")
                    if emp_id:
                        employees_data[f"emp_{row_num}"] = emp_data

            print(f"✅ تم تحميل {len(employees_data)} موظف من ملف Excel (ورقة: {sheet_name})")
            return employees_data

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات الموظفين من Excel:\n{str(e)}")
            return {}

    def update_variables_list(self):
        """تحديث قائمة المتغيرات المتاحة"""
        self.variables_listbox.delete(0, tk.END)
        
        variables = [
            "{{الرقم_الوظيفي}} - الرقم الوظيفي",
            "{{الاسم_العربي}} - الاسم العربي",
            "{{الاسم_الانجليزي}} - الاسم الإنجليزي", 
            "{{الرقم_الوطني}} - الرقم الوطني",
            "{{المسمى_الوظيفي}} - المسمى الوظيفي",
            "{{الدرجة_الحالية}} - الدرجة الحالية",
            "{{مكان_العمل_الحالي}} - مكان العمل الحالي",
            "{{تاريخ_التعيين}} - تاريخ التعيين",
            "{{المؤهل}} - المؤهل العلمي",
            "{{الجنسية}} - الجنسية",
            "{{تاريخ_الميلاد}} - تاريخ الميلاد",
            "{{الراتب_الأساسي}} - الراتب الأساسي",
            "{{التاريخ_الحالي}} - التاريخ الحالي",
            "{{التاريخ_الهجري}} - التاريخ الهجري",
            "{{اسم_المؤسسة}} - اسم المؤسسة",
            "{{عنوان_المؤسسة}} - عنوان المؤسسة"
        ]
        
        for var in variables:
            self.variables_listbox.insert(tk.END, var)

    def load_templates_list(self):
        """تحميل قائمة القوالب"""
        self.templates_listbox.delete(0, tk.END)
        
        if os.path.exists(self.templates_dir):
            for file in os.listdir(self.templates_dir):
                if file.endswith('.docx'):
                    template_name = file.replace('.docx', '')
                    self.templates_listbox.insert(tk.END, template_name)

    def on_template_selected(self, event=None):
        """عند اختيار قالب من القائمة"""
        selection = self.templates_listbox.curselection()
        if not selection:
            return
        
        template_name = self.templates_listbox.get(selection[0])
        template_path = os.path.join(self.templates_dir, f"{template_name}.docx")
        
        if os.path.exists(template_path):
            self.load_template_preview(template_path)
            self.current_template_path = template_path
            self.status_label.config(text=f"تم تحميل القالب: {template_name}")

    def load_template_preview(self, template_path):
        """تحميل معاينة القالب"""
        if not DOCX_AVAILABLE:
            self.preview_text.config(state=tk.NORMAL)
            self.preview_text.delete(1.0, tk.END)
            self.preview_text.insert(tk.END, "مكتبة python-docx غير متاحة لمعاينة القالب")
            self.preview_text.config(state=tk.DISABLED)
            return
        
        try:
            doc = Document(template_path)
            
            # استخراج النص من القالب
            full_text = []
            for paragraph in doc.paragraphs:
                full_text.append(paragraph.text)
            
            # عرض النص في المعاينة
            self.preview_text.config(state=tk.NORMAL)
            self.preview_text.delete(1.0, tk.END)
            self.preview_text.insert(tk.END, '\n'.join(full_text))
            self.preview_text.config(state=tk.DISABLED)
            
        except Exception as e:
            self.preview_text.config(state=tk.NORMAL)
            self.preview_text.delete(1.0, tk.END)
            self.preview_text.insert(tk.END, f"خطأ في تحميل القالب: {str(e)}")
            self.preview_text.config(state=tk.DISABLED)

    def create_new_template(self):
        """إنشاء قالب جديد"""
        # طلب اسم القالب
        template_name = simpledialog.askstring("قالب جديد", "أدخل اسم القالب الجديد:")
        if not template_name:
            return
        
        # تنظيف اسم الملف
        template_name = template_name.replace(" ", "_")
        template_path = os.path.join(self.templates_dir, f"{template_name}.docx")
        
        if os.path.exists(template_path):
            if not messagebox.askyesno("تأكيد", "القالب موجود بالفعل. هل تريد استبداله؟"):
                return
        
        # إنشاء قالب افتراضي
        self.create_default_template(template_path, template_name)
        
        # تحديث قائمة القوالب
        self.load_templates_list()
        
        # تحديد القالب الجديد
        for i in range(self.templates_listbox.size()):
            if self.templates_listbox.get(i) == template_name:
                self.templates_listbox.selection_set(i)
                self.on_template_selected()
                break
        
        self.status_label.config(text=f"تم إنشاء قالب جديد: {template_name}")

    def create_default_template(self, template_path, template_name):
        """إنشاء قالب افتراضي"""
        if not DOCX_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة python-docx غير مثبتة")
            return
        
        try:
            doc = Document()
            
            # إعداد الصفحة
            section = doc.sections[0]
            section.page_height = Inches(11.69)  # A4
            section.page_width = Inches(8.27)
            section.left_margin = Inches(1)
            section.right_margin = Inches(1)
            section.top_margin = Inches(1)
            section.bottom_margin = Inches(1)
            
            # العنوان
            title = doc.add_heading(f'قالب {template_name}', level=1)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            doc.add_paragraph()
            
            # المحتوى الافتراضي
            content = """بسم الله الرحمن الرحيم

{{اسم_المؤسسة}}
{{عنوان_المؤسسة}}

التاريخ: {{التاريخ_الحالي}}

السيد/ة: {{الاسم_العربي}}
الرقم الوظيفي: {{الرقم_الوظيفي}}
المسمى الوظيفي: {{المسمى_الوظيفي}}
الدرجة: {{الدرجة_الحالية}}
مكان العمل: {{مكان_العمل_الحالي}}

تحية طيبة وبعد،

نفيدكم بأنه [اكتب الغرض من المراسلة هنا]

يمكنك تعديل هذا النص وإضافة المتغيرات من القائمة المتاحة.
المتغيرات محاطة بأقواس مجعدة مزدوجة مثل {{الاسم_العربي}}

وتفضلوا بقبول فائق الاحترام والتقدير.


التوقيع: _______________

الاسم: _______________

المنصب: _______________

التاريخ: {{التاريخ_الحالي}}"""
            
            for line in content.split('\n'):
                p = doc.add_paragraph(line)
                p.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            
            # حفظ القالب
            doc.save(template_path)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء القالب الافتراضي: {str(e)}")

    def import_template(self):
        """استيراد قالب من ملف خارجي"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف القالب",
            filetypes=[("Word Documents", "*.docx"), ("All Files", "*.*")]
        )
        
        if not file_path:
            return
        
        try:
            # نسخ الملف إلى مجلد القوالب
            import shutil
            filename = os.path.basename(file_path)
            destination = os.path.join(self.templates_dir, filename)
            
            shutil.copy2(file_path, destination)
            
            # تحديث قائمة القوالب
            self.load_templates_list()
            
            # تحديد القالب المستورد
            template_name = filename.replace('.docx', '')
            for i in range(self.templates_listbox.size()):
                if self.templates_listbox.get(i) == template_name:
                    self.templates_listbox.selection_set(i)
                    self.on_template_selected()
                    break
            
            self.status_label.config(text=f"تم استيراد القالب: {filename}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في استيراد القالب: {str(e)}")

    def edit_template(self):
        """تعديل القالب المحدد"""
        if not self.current_template_path:
            messagebox.showwarning("تحذير", "الرجاء اختيار قالب للتعديل")
            return
        
        # فتح القالب للتعديل
        try:
            import subprocess
            import platform
            
            system = platform.system()
            if system == "Windows":
                os.startfile(self.current_template_path)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", self.current_template_path])
            else:  # Linux
                subprocess.run(["xdg-open", self.current_template_path])
            
            messagebox.showinfo("تعديل القالب", 
                              "تم فتح القالب للتعديل في Microsoft Word.\n\n"
                              "يمكنك:\n"
                              "• تعديل النص والتنسيق\n"
                              "• إضافة المتغيرات مثل {{الاسم_العربي}}\n"
                              "• تغيير مواضع الحقول بالسحب والإفلات\n"
                              "• تنسيق الخطوط والألوان\n\n"
                              "احفظ الملف وأغلقه ثم اضغط 'تحديث المعاينة'")
            
            # إضافة زر تحديث المعاينة
            self.add_refresh_button()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح القالب للتعديل: {str(e)}")

    def add_refresh_button(self):
        """إضافة زر تحديث المعاينة"""
        # البحث عن الزر إذا كان موجوداً
        for widget in self.parent.winfo_children():
            if hasattr(widget, 'refresh_button_added'):
                return  # الزر موجود بالفعل
        
        # إنشاء زر تحديث المعاينة
        refresh_frame = tk.Frame(self.parent, bg="#e8f5e8", relief=tk.RAISED, bd=2)
        refresh_frame.pack(fill=tk.X, padx=20, pady=5)
        refresh_frame.refresh_button_added = True
        
        tk.Button(refresh_frame, text="🔄 تحديث المعاينة", 
                 command=self.refresh_preview,
                 bg="#27ae60", fg="white", font=("Arial", 12, "bold")).pack(pady=10)

    def refresh_preview(self):
        """تحديث معاينة القالب"""
        if self.current_template_path:
            self.load_template_preview(self.current_template_path)
            self.status_label.config(text="تم تحديث المعاينة")

    def delete_template(self):
        """حذف القالب المحدد"""
        selection = self.templates_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "الرجاء اختيار قالب للحذف")
            return
        
        template_name = self.templates_listbox.get(selection[0])
        
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف القالب '{template_name}'؟"):
            try:
                template_path = os.path.join(self.templates_dir, f"{template_name}.docx")
                os.remove(template_path)
                
                # تحديث قائمة القوالب
                self.load_templates_list()
                
                # مسح المعاينة
                self.preview_text.config(state=tk.NORMAL)
                self.preview_text.delete(1.0, tk.END)
                self.preview_text.config(state=tk.DISABLED)
                self.current_template_path = None
                
                self.status_label.config(text=f"تم حذف القالب: {template_name}")
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف القالب: {str(e)}")

    def select_employee_for_correspondence(self):
        """اختيار موظف لإنشاء المراسلة"""
        try:
            # فتح نافذة اختيار الموظف
            employee_window = tk.Toplevel(self.parent)
            employee_window.title("🔍 اختيار موظف")
            employee_window.geometry("800x600")
            employee_window.grab_set()

            # تحميل بيانات الموظفين من Excel
            employees_data = self.load_employees_from_excel()

            if not employees_data:
                messagebox.showwarning("تحذير", "لا توجد بيانات موظفين\n\nتأكد من وجود ملف employees_data.xlsx مع بيانات الموظفين")
                employee_window.destroy()
                return

            # إطار البحث
            search_frame = tk.Frame(employee_window)
            search_frame.pack(fill=tk.X, padx=10, pady=5)

            tk.Label(search_frame, text="البحث:", font=("Arial", 12)).pack(side=tk.LEFT)
            search_var = tk.StringVar(master=employee_window)
            search_entry = tk.Entry(search_frame, textvariable=search_var, font=("Arial", 12))
            search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

            # قائمة الموظفين
            employees_frame = tk.Frame(employee_window)
            employees_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

            # إنشاء Treeview
            columns = ("الرقم الوظيفي", "الاسم العربي", "المسمى الوظيفي", "مكان العمل")
            employees_tree = ttk.Treeview(employees_frame, columns=columns, show="headings")

            # تعيين عناوين الأعمدة
            for col in columns:
                employees_tree.heading(col, text=col)
                employees_tree.column(col, width=150)

            # إضافة البيانات
            for emp_id, emp_data in employees_data.items():
                employees_tree.insert("", "end", values=(
                    emp_data.get("الرقم الوظيفي", ""),
                    emp_data.get("الاسم العربي", ""),
                    emp_data.get("المسمى الوظيفي", ""),
                    emp_data.get("مكان العمل الحالي", "")
                ))

            employees_tree.pack(fill=tk.BOTH, expand=True)

            # دالة البحث
            def search_employees():
                search_text = search_var.get().lower()
                employees_tree.delete(*employees_tree.get_children())

                for emp_id, emp_data in employees_data.items():
                    if (search_text in emp_data.get("الاسم العربي", "").lower() or
                        search_text in emp_data.get("الرقم الوظيفي", "").lower() or
                        search_text in emp_data.get("المسمى الوظيفي", "").lower()):

                        employees_tree.insert("", "end", values=(
                            emp_data.get("الرقم الوظيفي", ""),
                            emp_data.get("الاسم العربي", ""),
                            emp_data.get("المسمى الوظيفي", ""),
                            emp_data.get("مكان العمل الحالي", "")
                        ))

            search_entry.bind("<KeyRelease>", lambda e: search_employees())

            # أزرار التحكم
            buttons_frame = tk.Frame(employee_window)
            buttons_frame.pack(fill=tk.X, padx=10, pady=10)

            def select_employee():
                selection = employees_tree.selection()
                if not selection:
                    messagebox.showwarning("تحذير", "الرجاء اختيار موظف")
                    return

                # الحصول على بيانات الموظف المختار
                item = employees_tree.item(selection[0])
                emp_id = item['values'][0]

                # البحث عن بيانات الموظف الكاملة مع مقارنة محسنة
                selected_emp_data = None
                search_emp_id = str(emp_id).strip()

                for emp_key, emp_data in employees_data.items():
                    stored_emp_id = str(emp_data.get("الرقم الوظيفي", "")).strip()
                    if stored_emp_id == search_emp_id:
                        selected_emp_data = emp_data
                        break

                    # محاولة البحث بتنسيقات مختلفة
                    if search_emp_id.isdigit():
                        for zero_count in [2, 3, 4]:
                            padded_id = search_emp_id.zfill(zero_count)
                            if stored_emp_id == padded_id:
                                selected_emp_data = emp_data
                                break
                        if selected_emp_data:
                            break

                if selected_emp_data:
                    self.selected_employee_data = selected_emp_data
                    emp_name = selected_emp_data.get('الاسم العربي', '')
                    self.selected_employee_label.config(
                        text=f"تم اختيار: {emp_name} - {emp_id}",
                        fg="#27ae60"
                    )
                    print(f"✅ تم اختيار الموظف: {emp_name} - {emp_id}")
                else:
                    messagebox.showerror("خطأ", f"لم يتم العثور على بيانات الموظف: {emp_id}")
                    return

                employee_window.destroy()

            tk.Button(buttons_frame, text="✅ اختيار", command=select_employee,
                     bg="#27ae60", fg="white", font=("Arial", 12)).pack(side=tk.LEFT, padx=5)

            tk.Button(buttons_frame, text="❌ إلغاء", command=employee_window.destroy,
                     bg="#e74c3c", fg="white", font=("Arial", 12)).pack(side=tk.LEFT, padx=5)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح قائمة الموظفين: {str(e)}")

    def preview_correspondence_document(self):
        """معاينة المراسلة قبل الإنشاء"""
        if not self.current_template_path:
            messagebox.showwarning("تحذير", "الرجاء اختيار قالب أولاً")
            return

        if not self.selected_employee_data:
            messagebox.showwarning("تحذير", "الرجاء اختيار موظف أولاً")
            return

        # إنشاء نافذة المعاينة
        preview_window = tk.Toplevel(self.parent)
        preview_window.title("👁️ معاينة المراسلة")
        preview_window.geometry("800x600")
        preview_window.grab_set()

        # محتوى المعاينة
        preview_text = tk.Text(preview_window, wrap=tk.WORD, font=("Arial", 12))
        preview_scroll = tk.Scrollbar(preview_window, orient=tk.VERTICAL, command=preview_text.yview)
        preview_text.configure(yscrollcommand=preview_scroll.set)

        preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        preview_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # تحميل المحتوى مع استبدال المتغيرات
        try:
            filled_content = self.fill_template_variables()
            preview_text.insert(tk.END, filled_content)
            preview_text.config(state=tk.DISABLED)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء المعاينة: {str(e)}")

    def create_correspondence_document(self):
        """إنشاء المراسلة النهائية وفتحها للمراجعة والحفظ"""
        if not self.current_template_path:
            messagebox.showwarning("تحذير", "الرجاء اختيار قالب أولاً")
            return

        # تشخيص مفصل لمشكلة اختيار الموظف
        if not self.selected_employee_data:
            # فحص حالة المتغير
            print(f"🔍 تشخيص مشكلة اختيار الموظف:")
            print(f"   selected_employee_data: {self.selected_employee_data}")
            print(f"   نوع البيانات: {type(self.selected_employee_data)}")

            # فحص النص المعروض
            if hasattr(self, 'selected_employee_label'):
                label_text = self.selected_employee_label.cget("text")
                print(f"   نص التسمية: '{label_text}'")

            messagebox.showwarning("تحذير",
                "الرجاء اختيار موظف أولاً\n\n"
                "تأكد من:\n"
                "• اختيار موظف من القائمة\n"
                "• الضغط على زر 'اختيار'\n"
                "• ظهور اسم الموظف في الأعلى")
            return

        if not DOCX_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة python-docx غير مثبتة")
            return

        try:
            # إنشاء المستند الجديد
            doc = Document(self.current_template_path)

            # استبدال المتغيرات في جميع الفقرات
            for paragraph in doc.paragraphs:
                self.replace_variables_in_paragraph(paragraph)

            # استبدال المتغيرات في الجداول
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            self.replace_variables_in_paragraph(paragraph)

            # إنشاء ملف مؤقت
            import tempfile
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            emp_id = self.selected_employee_data.get('الرقم الوظيفي', 'unknown')
            emp_name = self.selected_employee_data.get('الاسم العربي', 'موظف')
            filename = f"مراسلة_{emp_name}_{emp_id}_{timestamp}.docx"

            # حفظ في مجلد مؤقت
            temp_dir = tempfile.gettempdir()
            temp_filepath = os.path.join(temp_dir, filename)
            doc.save(temp_filepath)

            # فتح المستند للمراجعة والحفظ
            self.open_file(temp_filepath)

            self.status_label.config(text=f"تم إنشاء المراسلة: {filename}")

            # رسالة توضيحية للمستخدم
            messagebox.showinfo("تم إنشاء المراسلة",
                f"تم إنشاء المراسلة بنجاح وفتحها في Microsoft Word\n\n"
                f"📄 اسم الملف: {filename}\n"
                f"👤 الموظف: {emp_name} ({emp_id})\n\n"
                f"💡 يمكنك الآن:\n"
                f"• مراجعة المراسلة وتعديلها حسب الحاجة\n"
                f"• حفظ الملف في المكان الذي تختاره\n"
                f"• استخدام 'حفظ باسم' لاختيار المجلد والاسم\n\n"
                f"📁 الملف المؤقت سيتم حذفه تلقائياً عند إغلاق الكمبيوتر")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء المراسلة: {str(e)}")

    def replace_variables_in_paragraph(self, paragraph):
        """استبدال المتغيرات في فقرة"""
        if not self.selected_employee_data:
            return

        # الحصول على قاموس المتغيرات
        variables = self.get_variables_dict()

        # فحص وجود متغيرات في الفقرة
        has_variables = any(variable in paragraph.text for variable in variables.keys())

        # استبدال المتغيرات في النص
        for variable, value in variables.items():
            if variable in paragraph.text:
                # طريقة محسنة للاستبدال
                original_text = paragraph.text
                new_text = original_text.replace(variable, str(value))

                if original_text != new_text:
                    # مسح النص الحالي
                    for run in paragraph.runs:
                        run.text = ""

                    # إضافة النص الجديد
                    if paragraph.runs:
                        paragraph.runs[0].text = new_text
                    else:
                        paragraph.add_run(new_text)

    def get_variables_dict(self):
        """الحصول على قاموس المتغيرات وقيمها"""
        if not self.selected_employee_data:
            return {}

        variables = {}

        # المتغيرات من بيانات الموظف مع تطابق دقيق للأسماء
        field_mapping = {
            'الرقم الوظيفي': '{{الرقم_الوظيفي}}',
            'الاسم العربي': '{{الاسم_العربي}}',
            'الاسم الإنجليزي': '{{الاسم_الانجليزي}}',
            'الرقم الوطني': '{{الرقم_الوطني}}',
            'المسمى الوظيفي': '{{المسمى_الوظيفي}}',
            'الدرجة الحالية': '{{الدرجة_الحالية}}',
            'مكان العمل الحالي': '{{مكان_العمل_الحالي}}',
            'تاريخ التعيين': '{{تاريخ_التعيين}}',
            'المؤهل': '{{المؤهل}}',
            'الجنسية': '{{الجنسية}}',
            'تاريخ الميلاد': '{{تاريخ_الميلاد}}',
            'الراتب الأساسي': '{{الراتب_الأساسي}}',
            'رقم الهاتف': '{{رقم_الهاتف}}',
            'التخصص': '{{التخصص}}',
            'اسم المصرف': '{{اسم_المصرف}}',
            'رقم الحساب': '{{رقم_الحساب}}'
        }

        # تطبيق التطابق
        for field_name, var_name in field_mapping.items():
            value = self.selected_employee_data.get(field_name, '')
            variables[var_name] = str(value) if value is not None else ''

        # المتغيرات الإضافية
        now = datetime.now()
        variables["{{التاريخ_الحالي}}"] = now.strftime("%Y-%m-%d")
        variables["{{التاريخ_الهجري}}"] = now.strftime("%Y-%m-%d")  # يمكن تحسينه لاحقاً
        variables["{{اسم_المؤسسة}}"] = "منظومة إدارة الموارد البشرية"
        variables["{{عنوان_المؤسسة}}"] = "المملكة العربية السعودية"

        return variables

    def fill_template_variables(self):
        """ملء متغيرات القالب للمعاينة"""
        if not DOCX_AVAILABLE:
            return "مكتبة python-docx غير متاحة"

        try:
            doc = Document(self.current_template_path)
            variables = self.get_variables_dict()

            # استخراج النص مع استبدال المتغيرات
            full_text = []
            for paragraph in doc.paragraphs:
                text = paragraph.text
                for variable, value in variables.items():
                    text = text.replace(variable, str(value))
                full_text.append(text)

            return '\n'.join(full_text)

        except Exception as e:
            return f"خطأ في معالجة القالب: {str(e)}"

    def open_file(self, filepath):
        """فتح ملف بالبرنامج الافتراضي"""
        try:
            import subprocess
            import platform

            system = platform.system()
            if system == "Windows":
                os.startfile(filepath)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", filepath])
            else:  # Linux
                subprocess.run(["xdg-open", filepath])

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح الملف: {str(e)}")

if __name__ == "__main__":
    # تشغيل مدير القوالب كتطبيق مستقل
    app = TemplateManager()
    if hasattr(app, 'root'):
        app.root.mainloop()
