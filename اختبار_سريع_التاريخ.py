#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لنافذة اختيار التاريخ
Quick Test for Date Selection Window
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_date_window():
    """اختبار سريع لنافذة التاريخ"""
    print("📅 اختبار سريع لنافذة اختيار التاريخ")
    print("=" * 50)
    
    try:
        # استيراد قسم الإجازات
        print("📦 استيراد قسم الإجازات...")
        import leave_department_system
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة رئيسية
        root = tk.Tk()
        root.title("اختبار سريع - نافذة التاريخ")
        root.geometry("400x300")
        root.configure(bg="#f0f8ff")
        
        # إنشاء قسم الإجازات
        leave_dept = leave_department_system.LeaveDepartmentSystem(root)
        
        # إنشاء واجهة الاختبار
        test_frame = tk.Frame(root, bg="#f0f8ff")
        test_frame.pack(expand=True, fill=tk.BOTH, padx=20, pady=20)
        
        # عنوان
        title_label = tk.Label(test_frame, text="📅 اختبار نافذة التاريخ المحسنة", 
                              bg="#f0f8ff", fg="#1565c0", font=("Arial", 16, "bold"))
        title_label.pack(pady=20)
        
        # معلومات
        info_text = """
🔧 التحسينات المطبقة:
• حجم النافذة: 650x650 (بدلاً من 450x400)
• أزرار كبيرة: font=("Arial", 16, "bold")
• مساحات أكبر: padx=30, pady=15
• فاصل بصري قبل الأزرار
• مؤشر اليد: cursor="hand2"

📋 للاختبار:
اضغط الزر أدناه لفتح نافذة التاريخ
        """
        
        info_label = tk.Label(test_frame, text=info_text, bg="#f0f8ff", 
                             fg="#1565c0", font=("Arial", 11), justify=tk.LEFT)
        info_label.pack(pady=20)
        
        # متغير للتاريخ
        test_date_var = tk.StringVar()
        
        def open_date_window():
            """فتح نافذة اختيار التاريخ"""
            print("\n📅 فتح نافذة اختيار التاريخ...")
            leave_dept.select_date(test_date_var)
            print("✅ تم فتح النافذة - راجع الحجم والأزرار")
        
        def show_selected_date():
            """عرض التاريخ المختار"""
            date = test_date_var.get()
            if date:
                messagebox.showinfo("التاريخ المختار", f"التاريخ المحفوظ: {date}")
            else:
                messagebox.showinfo("التاريخ المختار", "لم يتم اختيار تاريخ بعد")
        
        # أزرار الاختبار
        buttons_frame = tk.Frame(test_frame, bg="#f0f8ff")
        buttons_frame.pack(pady=30)
        
        # زر فتح نافذة التاريخ
        open_btn = tk.Button(buttons_frame, text="📅 فتح نافذة التاريخ", 
                            command=open_date_window,
                            bg="#4caf50", fg="white", font=("Arial", 14, "bold"),
                            padx=20, pady=10, cursor="hand2")
        open_btn.pack(pady=10)
        
        # زر عرض التاريخ المختار
        show_btn = tk.Button(buttons_frame, text="👁️ عرض التاريخ المختار", 
                            command=show_selected_date,
                            bg="#2196f3", fg="white", font=("Arial", 14, "bold"),
                            padx=20, pady=10, cursor="hand2")
        show_btn.pack(pady=10)
        
        # زر إغلاق
        close_btn = tk.Button(buttons_frame, text="❌ إغلاق", 
                             command=root.destroy,
                             bg="#f44336", fg="white", font=("Arial", 14, "bold"),
                             padx=20, pady=10, cursor="hand2")
        close_btn.pack(pady=10)
        
        # تعليمات
        instructions_label = tk.Label(test_frame, 
                                     text="📋 اضغط 'فتح نافذة التاريخ' وراجع الحجم الجديد والأزرار الكبيرة", 
                                     bg="#f0f8ff", fg="#ff5722", font=("Arial", 12, "bold"))
        instructions_label.pack(pady=20)
        
        print("\n🎉 الاختبار جاهز!")
        print("📋 اضغط على 'فتح نافذة التاريخ' لاختبار التحسينات")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    test_date_window()

if __name__ == "__main__":
    main()
