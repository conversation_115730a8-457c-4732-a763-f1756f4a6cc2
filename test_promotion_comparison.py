#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مقارنة أرقام الترقيات بين النظامين
Test Promotion Numbers Comparison Between Systems
"""

import sys
import datetime
import os

def test_promotion_comparison():
    """اختبار مقارنة أرقام الترقيات"""
    print("🔍 اختبار مقارنة أرقام الترقيات بين النظامين")
    print("=" * 70)
    
    # اختبار 1: حساب الترقيات في نظام الترقيات
    print("\n🧪 اختبار 1: حساب الترقيات في نظام الترقيات")
    print("-" * 50)
    
    try:
        import tkinter as tk
        from promotion_system_safe import PromotionSystem
        
        # إنشاء نافذة وهمية للاختبار
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # إنشاء نظام الترقيات
        promotion_sys = PromotionSystem(root)
        
        # الحصول على الإحصائيات
        stats = promotion_sys.promotion_stats
        promotion_count = stats.get('eligible_for_promotion', 0)
        near_count = stats.get('near_eligibility', 0)
        not_eligible_count = stats.get('not_eligible', 0)
        total_count = stats.get('total_employees', 0)
        
        print(f"📊 نظام الترقيات:")
        print(f"   👥 إجمالي الموظفين: {total_count}")
        print(f"   ⬆️ مستحق للترقية: {promotion_count}")
        print(f"   ⏳ قريب من الاستحقاق: {near_count}")
        print(f"   ❌ غير مستحق: {not_eligible_count}")
        
        # عرض تفاصيل الموظفين المستحقين
        print(f"\n📋 الموظفين المستحقين للترقية:")
        eligible_list = []
        for promotion in promotion_sys.promotion_list:
            if promotion["حالة الترقية"] == "مستحق للترقية":
                name = promotion["الاسم العربي"]
                emp_id = promotion["الرقم الوظيفي"]
                grade = promotion["الدرجة الحالية"]
                years = promotion["سنوات في الدرجة"]
                eligible_list.append(f"{name} ({emp_id}) - الدرجة {grade} - {years} سنة")
        
        for i, emp in enumerate(eligible_list, 1):
            print(f"   {i}. {emp}")
        
        root.destroy()
        
    except Exception as e:
        print(f"❌ خطأ في نظام الترقيات: {e}")
        promotion_count = 0
        total_count = 0
    
    # اختبار 2: حساب الترقيات بطريقة مباشرة من ملف البيانات
    print("\n🧪 اختبار 2: حساب الترقيات مباشرة من ملف البيانات")
    print("-" * 50)
    
    try:
        from openpyxl import load_workbook
        from datetime import datetime
        
        # تحميل ملف البيانات
        employees_file = "employees_data.xlsx"
        if not os.path.exists(employees_file):
            print(f"❌ ملف البيانات غير موجود: {employees_file}")
            return
        
        wb = load_workbook(employees_file)
        ws = wb["الموظفين"]
        
        # قراءة البيانات
        headers = [cell.value for cell in ws[1]]
        employees_data = []
        for row in ws.iter_rows(min_row=2, values_only=True):
            if any(row):
                employees_data.append(dict(zip(headers, row)))
        
        print(f"📊 تم تحميل {len(employees_data)} موظف من الملف")
        
        # حساب الترقيات بنفس معايير نظام الترقيات
        current_date = datetime.now()
        eligible_count = 0
        near_count = 0
        not_eligible_count = 0
        max_grade_count = 0
        teachers_count = 0
        
        direct_eligible_list = []
        
        for emp in employees_data:
            try:
                job_title = emp.get("المسمى الوظيفي", "")
                current_grade = emp.get("الدرجة الحالية", "")
                grade_date_str = emp.get("تاريخ الدرجة الحالية", "")
                name = emp.get("الاسم العربي", "غير محدد")
                emp_id = emp.get("الرقم الوظيفي", "غير محدد")
                
                # تجاهل المعلمين
                if "معلم" in job_title:
                    teachers_count += 1
                    continue
                
                if not current_grade or not grade_date_str:
                    not_eligible_count += 1
                    continue
                
                # تحويل التاريخ
                grade_date = None
                for date_format in ["%Y-%m-%d", "%d/%m/%Y", "%d-%m-%Y"]:
                    try:
                        grade_date = datetime.strptime(str(grade_date_str), date_format)
                        break
                    except:
                        continue
                
                if not grade_date:
                    not_eligible_count += 1
                    continue
                
                # حساب السنوات في الدرجة
                years_in_grade = (current_date - grade_date).days / 365.25
                
                # فحص الحد الأقصى
                try:
                    current_grade_num = int(current_grade)
                    if current_grade_num >= 15:
                        max_grade_count += 1
                        continue
                except:
                    pass
                
                # تصنيف الموظف
                if years_in_grade >= 4:
                    eligible_count += 1
                    direct_eligible_list.append(f"{name} ({emp_id}) - الدرجة {current_grade} - {years_in_grade:.1f} سنة")
                elif years_in_grade >= 3:
                    near_count += 1
                else:
                    not_eligible_count += 1
                    
            except Exception as e:
                print(f"خطأ في معالجة موظف: {e}")
                not_eligible_count += 1
                continue
        
        print(f"\n📊 الحساب المباشر من الملف:")
        print(f"   👥 إجمالي الموظفين: {len(employees_data)}")
        print(f"   ⬆️ مستحق للترقية: {eligible_count}")
        print(f"   ⏳ قريب من الاستحقاق: {near_count}")
        print(f"   ❌ غير مستحق: {not_eligible_count}")
        print(f"   🔝 وصل للحد الأقصى: {max_grade_count}")
        print(f"   👨‍🏫 معلمين (مستثنون): {teachers_count}")
        
        print(f"\n📋 الموظفين المستحقين (حساب مباشر):")
        for i, emp in enumerate(direct_eligible_list, 1):
            print(f"   {i}. {emp}")
        
    except Exception as e:
        print(f"❌ خطأ في الحساب المباشر: {e}")
        eligible_count = 0
    
    # اختبار 3: محاولة حساب الترقيات بطريقة الواجهة الرئيسية
    print("\n🧪 اختبار 3: محاولة حساب الترقيات بطريقة الواجهة الرئيسية")
    print("-" * 50)
    
    try:
        # نسخ الكود من hr_system.py لحساب الترقيات
        from datetime import datetime
        from openpyxl import load_workbook
        
        # تحميل البيانات
        employees_file = "employees_data.xlsx"
        wb = load_workbook(employees_file)
        ws = wb["الموظفين"]
        
        headers = [cell.value for cell in ws[1]]
        employees_data = []
        for row in ws.iter_rows(min_row=2, values_only=True):
            if any(row):
                employees_data.append(dict(zip(headers, row)))
        
        # حساب الترقيات بطريقة الواجهة الرئيسية المحدثة
        count = 0
        current_date = datetime.now()
        main_eligible_list = []
        
        for employee in employees_data:
            try:
                # استخدام نفس معايير نظام الترقيات
                job_title = employee.get("المسمى الوظيفي", "")
                current_grade = employee.get("الدرجة الحالية", "")
                grade_date_str = employee.get("تاريخ الدرجة الحالية", "")
                name = employee.get("الاسم العربي", "غير محدد")
                emp_id = employee.get("الرقم الوظيفي", "غير محدد")
                
                # تجاهل المعلمين (حسب نظام الترقيات)
                if "معلم" in job_title:
                    continue
                
                if not current_grade or not grade_date_str:
                    continue

                # محاولة تحويل التاريخ
                grade_date = None
                for date_format in ["%Y-%m-%d", "%d/%m/%Y", "%d-%m-%Y"]:
                    try:
                        grade_date = datetime.strptime(str(grade_date_str), date_format)
                        break
                    except:
                        continue

                if grade_date:
                    # حساب السنوات في الدرجة الحالية
                    years_in_grade = (current_date - grade_date).days / 365.25
                    
                    # فحص الحد الأقصى للدرجة
                    try:
                        current_grade_num = int(current_grade)
                        if current_grade_num >= 15:  # الحد الأقصى
                            continue
                    except:
                        pass
                    
                    # معيار الاستحقاق: 4 سنوات أو أكثر (نفس نظام الترقيات)
                    if years_in_grade >= 4:
                        count += 1
                        main_eligible_list.append(f"{name} ({emp_id}) - الدرجة {current_grade} - {years_in_grade:.1f} سنة")

            except Exception as e:
                continue
        
        print(f"📊 طريقة الواجهة الرئيسية المحدثة: {count} موظف مستحق")
        
        print(f"\n📋 الموظفين المستحقين (طريقة الواجهة الرئيسية):")
        for i, emp in enumerate(main_eligible_list, 1):
            print(f"   {i}. {emp}")
        
    except Exception as e:
        print(f"❌ خطأ في طريقة الواجهة الرئيسية: {e}")
        count = 0
    
    # المقارنة النهائية
    print("\n" + "=" * 70)
    print("🔍 المقارنة النهائية:")
    print(f"   📊 نظام الترقيات: {promotion_count} موظف مستحق")
    print(f"   📊 الحساب المباشر: {eligible_count} موظف مستحق")
    print(f"   📊 طريقة الواجهة الرئيسية: {count} موظف مستحق")
    
    if promotion_count == eligible_count == count:
        print("   ✅ جميع الطرق تعطي نفس النتيجة!")
        print("   🎉 المعايير موحدة بنجاح!")
    else:
        print("   ⚠️ هناك اختلافات في النتائج")
        if promotion_count == eligible_count:
            print("   ✅ نظام الترقيات والحساب المباشر متطابقان")
        if promotion_count == count:
            print("   ✅ نظام الترقيات والواجهة الرئيسية متطابقان")
        if eligible_count == count:
            print("   ✅ الحساب المباشر والواجهة الرئيسية متطابقان")
    
    print("=" * 70)

def main():
    """تشغيل الاختبار"""
    print("🔍 اختبار مقارنة أرقام الترقيات بين النظامين")
    print(f"📅 التاريخ: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    test_promotion_comparison()
    
    print("\n📋 الخلاصة:")
    print("هذا الاختبار يوضح سبب الاختلاف في أرقام الترقيات")
    print("ويساعد في توحيد المعايير بين جميع أجزاء النظام")
    
    print("\n🏁 انتهى اختبار المقارنة")

if __name__ == "__main__":
    main()
