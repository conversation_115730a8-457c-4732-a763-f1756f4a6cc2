#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لنظام الترقيات مع البيانات الحقيقية
Complete Test for Promotion System with Real Data
"""

import sys
import datetime
import os

def test_complete_promotion_system():
    """اختبار شامل لنظام الترقيات مع البيانات الحقيقية"""
    print("🔔 اختبار شامل لنظام الترقيات مع البيانات الحقيقية")
    print("=" * 70)
    
    test_results = {
        'total_tests': 0,
        'passed_tests': 0,
        'failed_tests': 0
    }
    
    # اختبار 1: فحص ملف البيانات
    print("\n🧪 اختبار 1: فحص ملف البيانات")
    print("-" * 50)
    
    employees_file = "employees_data.xlsx"
    if os.path.exists(employees_file):
        print(f"✅ ملف البيانات موجود: {employees_file}")
        test_results['passed_tests'] += 1
        
        try:
            from openpyxl import load_workbook
            wb = load_workbook(employees_file)
            if "الموظفين" in wb.sheetnames:
                print("✅ ورقة الموظفين موجودة")
                test_results['passed_tests'] += 1
                
                ws = wb["الموظفين"]
                employee_count = ws.max_row - 1  # -1 للعناوين
                if employee_count > 0:
                    print(f"✅ يحتوي على {employee_count} موظف")
                    test_results['passed_tests'] += 1
                else:
                    print("❌ لا يحتوي على بيانات موظفين")
                    test_results['failed_tests'] += 1
            else:
                print("❌ ورقة الموظفين غير موجودة")
                test_results['failed_tests'] += 1
            
            test_results['total_tests'] += 2
            
        except Exception as e:
            print(f"❌ خطأ في قراءة ملف البيانات: {e}")
            test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
    else:
        print(f"❌ ملف البيانات غير موجود: {employees_file}")
        test_results['failed_tests'] += 1
    test_results['total_tests'] += 1
    
    # اختبار 2: تشغيل نظام الترقيات
    print("\n🧪 اختبار 2: تشغيل نظام الترقيات")
    print("-" * 50)
    
    try:
        import tkinter as tk
        from promotion_system_safe import PromotionSystem
        
        # إنشاء نافذة وهمية للاختبار
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # إنشاء نظام الترقيات
        promotion_sys = PromotionSystem(root)
        
        print("✅ تم تشغيل نظام الترقيات بنجاح")
        test_results['passed_tests'] += 1
        
        # فحص تحميل البيانات
        if promotion_sys.employees_data:
            print(f"✅ تم تحميل {len(promotion_sys.employees_data)} موظف")
            test_results['passed_tests'] += 1
        else:
            print("❌ لم يتم تحميل بيانات الموظفين")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
        
        # فحص حساب الترقيات
        if promotion_sys.promotion_list:
            print(f"✅ تم حساب ترقيات {len(promotion_sys.promotion_list)} موظف")
            test_results['passed_tests'] += 1
        else:
            print("❌ لم يتم حساب الترقيات")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
        
        # فحص الإحصائيات
        stats = promotion_sys.promotion_stats
        eligible_count = stats.get('eligible_for_promotion', 0)
        near_count = stats.get('near_eligibility', 0)
        not_eligible_count = stats.get('not_eligible', 0)
        
        print(f"📊 إحصائيات الترقيات:")
        print(f"   ⬆️ مستحق للترقية: {eligible_count}")
        print(f"   ⏳ قريب من الاستحقاق: {near_count}")
        print(f"   ❌ غير مستحق: {not_eligible_count}")
        
        if eligible_count > 0:
            print("✅ يوجد موظفين مستحقين للترقية")
            test_results['passed_tests'] += 1
        else:
            print("⚠️ لا يوجد موظفين مستحقين للترقية")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
        
        root.destroy()
    
    except Exception as e:
        print(f"❌ خطأ في تشغيل نظام الترقيات: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 3: إنشاء ملف الترقيات
    print("\n🧪 اختبار 3: إنشاء ملف الترقيات")
    print("-" * 50)
    
    try:
        import tkinter as tk
        from promotion_system_safe import PromotionSystem
        
        # إنشاء نافذة وهمية للاختبار
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # إنشاء نظام الترقيات
        promotion_sys = PromotionSystem(root)
        
        # حذف ملف الترقيات إذا كان موجود للاختبار
        if os.path.exists(promotion_sys.promotions_file):
            os.remove(promotion_sys.promotions_file)
            print(f"🗑️ تم حذف الملف السابق: {promotion_sys.promotions_file}")
        
        # إنشاء ملف الترقيات
        promotion_sys.create_promotions_file()
        
        # فحص وجود الملف
        if os.path.exists(promotion_sys.promotions_file):
            print(f"✅ تم إنشاء ملف الترقيات: {promotion_sys.promotions_file}")
            test_results['passed_tests'] += 1
            
            # فحص حجم الملف
            file_size = os.path.getsize(promotion_sys.promotions_file)
            if file_size > 0:
                print(f"✅ حجم الملف: {file_size} بايت")
                test_results['passed_tests'] += 1
            else:
                print("❌ الملف فارغ")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
        else:
            print("❌ لم يتم إنشاء ملف الترقيات")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
        
        root.destroy()
    
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء ملف الترقيات: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 4: قراءة محتوى ملف الترقيات
    print("\n🧪 اختبار 4: قراءة محتوى ملف الترقيات")
    print("-" * 50)
    
    try:
        from openpyxl import load_workbook
        
        promotions_file = "ترقيات.xlsx"
        if os.path.exists(promotions_file):
            wb = load_workbook(promotions_file)
            ws = wb.active
            
            # فحص العناوين
            headers_count = 0
            for cell in ws[1]:
                if cell.value:
                    headers_count += 1
                else:
                    break
            
            expected_headers = 19
            if headers_count == expected_headers:
                print(f"✅ عدد العناوين صحيح: {headers_count}")
                test_results['passed_tests'] += 1
            else:
                print(f"❌ عدد العناوين غير صحيح: {headers_count} (متوقع: {expected_headers})")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
            
            # فحص البيانات
            data_rows = ws.max_row - 1  # -1 للعناوين
            if data_rows > 0:
                print(f"✅ يحتوي على {data_rows} صف من البيانات")
                test_results['passed_tests'] += 1
                
                # فحص أول صف من البيانات
                first_row_data = []
                for col in range(1, headers_count + 1):
                    cell_value = ws.cell(row=2, column=col).value
                    if cell_value:
                        first_row_data.append(str(cell_value))
                
                if first_row_data:
                    print(f"✅ البيانات متاحة - أول موظف: {first_row_data[1] if len(first_row_data) > 1 else 'غير محدد'}")
                    test_results['passed_tests'] += 1
                else:
                    print("❌ لا توجد بيانات في الصفوف")
                    test_results['failed_tests'] += 1
                test_results['total_tests'] += 1
            else:
                print("⚠️ لا توجد صفوف بيانات (قد يكون هناك رسالة توضيحية)")
                test_results['passed_tests'] += 1  # هذا مقبول إذا لم يكن هناك مستحقين
            test_results['total_tests'] += 1
            
        else:
            print(f"❌ ملف الترقيات غير موجود: {promotions_file}")
            test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
    
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف الترقيات: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 5: اختبار التنبيه التلقائي
    print("\n🧪 اختبار 5: اختبار التنبيه التلقائي")
    print("-" * 50)
    
    try:
        import tkinter as tk
        from promotion_system_safe import PromotionSystem
        
        # إنشاء نافذة وهمية للاختبار
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # إنشاء نظام الترقيات
        promotion_sys = PromotionSystem(root)
        
        # فحص وجود دالة التنبيه
        if hasattr(promotion_sys, 'show_promotion_alert'):
            print("✅ دالة التنبيه التلقائي موجودة")
            test_results['passed_tests'] += 1
        else:
            print("❌ دالة التنبيه التلقائي مفقودة")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
        
        # فحص الإحصائيات للتنبيه
        eligible_count = promotion_sys.promotion_stats.get('eligible_for_promotion', 0)
        if eligible_count > 0:
            print(f"✅ سيظهر تنبيه للـ {eligible_count} موظف مستحق")
            test_results['passed_tests'] += 1
        else:
            print("⚠️ لن يظهر تنبيه (لا يوجد مستحقين)")
            test_results['passed_tests'] += 1  # هذا مقبول
        test_results['total_tests'] += 1
        
        root.destroy()
    
    except Exception as e:
        print(f"❌ خطأ في اختبار التنبيه التلقائي: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    return test_results

def main():
    """تشغيل جميع الاختبارات"""
    print("🔔 اختبار شامل لنظام الترقيات مع البيانات الحقيقية")
    print(f"📅 التاريخ: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 إصدار Python: {sys.version}")
    print()
    
    # تشغيل الاختبارات
    results = test_complete_promotion_system()
    
    # عرض النتائج النهائية
    print("\n" + "=" * 70)
    print("📊 ملخص نتائج الاختبار الشامل:")
    print(f"   📈 إجمالي الاختبارات: {results['total_tests']}")
    print(f"   ✅ نجح: {results['passed_tests']}")
    print(f"   ❌ فشل: {results['failed_tests']}")
    
    success_rate = (results['passed_tests'] / results['total_tests']) * 100 if results['total_tests'] > 0 else 0
    print(f"   📊 معدل النجاح: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("\n🎉 ممتاز! نظام الترقيات يعمل بشكل مثالي مع البيانات الحقيقية")
    elif success_rate >= 75:
        print("\n✅ جيد! النظام يعمل بشكل جيد مع بعض التحسينات المطلوبة")
    elif success_rate >= 50:
        print("\n⚠️ متوسط! النظام يحتاج إلى تحسينات إضافية")
    else:
        print("\n❌ ضعيف! النظام يحتاج إلى مراجعة شاملة")
    
    print("=" * 70)
    
    # عرض تعليمات الاستخدام النهائية
    print("\n📋 كيفية استخدام النظام الآن:")
    print("1. افتح نظام الترقيات من القائمة الرئيسية")
    print("2. سيظهر تنبيه تلقائي إذا كان هناك موظفين مستحقين")
    print("3. اضغط 'فتح ملف الترقيات' لرؤية البيانات المفصلة")
    print("4. حرر البيانات في Excel حسب الحاجة")
    print("5. اضغط 'تحديث الترقيات' لإعادة تحميل البيانات")
    
    # فحص الملفات النهائية
    print("\n📁 الملفات المتاحة:")
    files_to_check = [
        "employees_data.xlsx",
        "ترقيات.xlsx",
        "promotion_system_safe.py"
    ]
    
    for file in files_to_check:
        if os.path.exists(file):
            file_size = os.path.getsize(file)
            print(f"   ✅ {file} ({file_size} بايت)")
        else:
            print(f"   ❌ {file} (غير موجود)")
    
    print("\n🏁 انتهى الاختبار الشامل لنظام الترقيات")

if __name__ == "__main__":
    main()
