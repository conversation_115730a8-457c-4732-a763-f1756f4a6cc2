#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للنظام المحسن
Comprehensive Enhanced System Test
"""

import os
import sys
import time
import datetime

def test_enhanced_features():
    """اختبار الميزات المحسنة"""
    print("🚀 بدء اختبار النظام المحسن")
    print("=" * 80)
    
    test_results = {
        'total_tests': 0,
        'passed_tests': 0,
        'failed_tests': 0,
        'warnings': 0
    }
    
    # اختبار 1: استيراد أنظمة التحسين
    print("\n🧪 اختبار 1: استيراد أنظمة التحسين")
    print("-" * 50)
    
    try:
        from data_validation import DataValidator, CalculationEngine
        print("✅ تم استيراد نظام التحقق من البيانات")
        test_results['passed_tests'] += 1
    except ImportError as e:
        print(f"❌ فشل استيراد نظام التحقق: {e}")
        test_results['failed_tests'] += 1
    test_results['total_tests'] += 1
    
    try:
        from error_handler import error_handler, backup_manager
        print("✅ تم استيراد نظام إدارة الأخطاء")
        test_results['passed_tests'] += 1
    except ImportError as e:
        print(f"❌ فشل استيراد نظام الأخطاء: {e}")
        test_results['failed_tests'] += 1
    test_results['total_tests'] += 1
    
    try:
        from performance_optimizer import performance_monitor, memory_optimizer
        print("✅ تم استيراد نظام تحسين الأداء")
        test_results['passed_tests'] += 1
    except ImportError as e:
        print(f"❌ فشل استيراد نظام الأداء: {e}")
        test_results['failed_tests'] += 1
    test_results['total_tests'] += 1
    
    try:
        from system_monitor import show_system_monitor
        print("✅ تم استيراد مراقب النظام")
        test_results['passed_tests'] += 1
    except ImportError as e:
        print(f"❌ فشل استيراد مراقب النظام: {e}")
        test_results['failed_tests'] += 1
    test_results['total_tests'] += 1
    
    # اختبار 2: نظام التحقق من البيانات
    print("\n🧪 اختبار 2: نظام التحقق من البيانات")
    print("-" * 50)
    
    try:
        from data_validation import DataValidator
        validator = DataValidator()
        
        # اختبار التحقق من الرقم الوظيفي
        test_data = [
            ("123", True, "رقم صحيح"),
            ("abc", False, "رقم يحتوي على أحرف"),
            ("", False, "رقم فارغ"),
            ("12", False, "رقم قصير"),
            ("12345678901", False, "رقم طويل")
        ]

        # اختبار التحقق من الرقم الوطني
        national_id_tests = [
            ("1234567890", True, "رقم وطني 10 أرقام صحيح"),
            ("123456789012", True, "رقم وطني 12 رقم صحيح"),
            ("12345", False, "رقم وطني قصير"),
            ("12345678901234", False, "رقم وطني طويل"),
            ("123456789a", False, "رقم وطني يحتوي على أحرف"),
            ("", True, "رقم وطني فارغ (تحذير)")
        ]
        
        for emp_id, expected, description in test_data:
            validator.clear_messages()
            result = validator.validate_employee_id(emp_id)
            if result == expected:
                print(f"✅ {description}: نجح")
                test_results['passed_tests'] += 1
            else:
                print(f"❌ {description}: فشل")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1

        # اختبار التحقق من الرقم الوطني
        for national_id, expected, description in national_id_tests:
            validator.clear_messages()
            result = validator.validate_national_id(national_id)
            if result == expected:
                print(f"✅ {description}: نجح")
                test_results['passed_tests'] += 1
            else:
                print(f"❌ {description}: فشل")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحقق: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 3: محرك الحسابات
    print("\n🧪 اختبار 3: محرك الحسابات")
    print("-" * 50)
    
    try:
        from data_validation import CalculationEngine
        calculator = CalculationEngine()
        
        # اختبار حساب سنوات الخدمة
        test_date = "2020-01-01"
        service_years = calculator.calculate_service_years(test_date)
        expected_years = (datetime.datetime.now() - datetime.datetime(2020, 1, 1)).days / 365.25
        
        if abs(service_years - expected_years) < 0.1:
            print(f"✅ حساب سنوات الخدمة: {service_years:.2f} سنة")
            test_results['passed_tests'] += 1
        else:
            print(f"❌ خطأ في حساب سنوات الخدمة: {service_years} != {expected_years}")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
        
        # اختبار حساب رصيد الإجازات
        balance_tests = [
            (5, "موظف", 150),
            (3, "موظفة", 90),
            (4, "معلم", 0),
            (2, "معلمة", 0)
        ]
        
        for years, job_title, expected_balance in balance_tests:
            balance = calculator.calculate_leave_balance(years, job_title)
            if balance == expected_balance:
                print(f"✅ رصيد {job_title} ({years} سنوات): {balance} يوم")
                test_results['passed_tests'] += 1
            else:
                print(f"❌ خطأ في رصيد {job_title}: {balance} != {expected_balance}")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الحسابات: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 4: نظام إدارة الأخطاء
    print("\n🧪 اختبار 4: نظام إدارة الأخطاء")
    print("-" * 50)
    
    try:
        from error_handler import error_handler
        
        # اختبار تسجيل خطأ
        error_handler.log_error("TEST_ERROR", "اختبار تسجيل الأخطاء", "تفاصيل الاختبار")
        print("✅ تم تسجيل خطأ اختبار")
        test_results['passed_tests'] += 1
        
        # اختبار تسجيل نشاط
        error_handler.log_activity("TEST_ACTIVITY", "اختبار تسجيل النشاط", success=True)
        print("✅ تم تسجيل نشاط اختبار")
        test_results['passed_tests'] += 1
        
        # اختبار الحصول على إحصائيات
        stats = error_handler.get_error_stats()
        if isinstance(stats, dict) and 'total_errors' in stats:
            print(f"✅ إحصائيات الأخطاء: {stats['total_errors']} خطأ إجمالي")
            test_results['passed_tests'] += 1
        else:
            print("❌ فشل في الحصول على إحصائيات الأخطاء")
            test_results['failed_tests'] += 1
        
        test_results['total_tests'] += 3
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إدارة الأخطاء: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 5: نظام النسخ الاحتياطية
    print("\n🧪 اختبار 5: نظام النسخ الاحتياطية")
    print("-" * 50)
    
    try:
        from error_handler import backup_manager
        
        # إنشاء ملف اختبار
        test_file = "test_backup_file.txt"
        with open(test_file, "w", encoding="utf-8") as f:
            f.write("ملف اختبار للنسخ الاحتياطية")
        
        # اختبار إنشاء نسخة احتياطية
        backup_success = backup_manager.create_backup(test_file, "test_backup.txt")
        if backup_success:
            print("✅ تم إنشاء نسخة احتياطية اختبار")
            test_results['passed_tests'] += 1
        else:
            print("❌ فشل في إنشاء نسخة احتياطية")
            test_results['failed_tests'] += 1
        
        # اختبار قائمة النسخ الاحتياطية
        backups = backup_manager.list_backups()
        if isinstance(backups, list):
            print(f"✅ قائمة النسخ الاحتياطية: {len(backups)} نسخة")
            test_results['passed_tests'] += 1
        else:
            print("❌ فشل في الحصول على قائمة النسخ الاحتياطية")
            test_results['failed_tests'] += 1
        
        # تنظيف ملفات الاختبار
        if os.path.exists(test_file):
            os.remove(test_file)
        
        test_results['total_tests'] += 2
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النسخ الاحتياطية: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 6: نظام مراقبة الأداء
    print("\n🧪 اختبار 6: نظام مراقبة الأداء")
    print("-" * 50)
    
    try:
        from performance_optimizer import performance_monitor, memory_optimizer
        
        # اختبار بدء المراقبة
        performance_monitor.start_monitoring(0.1)  # فترة قصيرة للاختبار
        time.sleep(0.5)  # انتظار قصير
        performance_monitor.stop_monitoring()
        print("✅ تم اختبار مراقبة الأداء")
        test_results['passed_tests'] += 1
        
        # اختبار تحسين الذاكرة
        result = memory_optimizer.optimize_memory()
        if isinstance(result, dict) and 'collected_objects' in result:
            print(f"✅ تحسين الذاكرة: تم جمع {result['collected_objects']} كائن")
            test_results['passed_tests'] += 1
        else:
            print("❌ فشل في تحسين الذاكرة")
            test_results['failed_tests'] += 1
        
        # اختبار إنشاء تقرير الأداء
        report = performance_monitor.generate_performance_report()
        if isinstance(report, str) and len(report) > 0:
            print("✅ تم إنشاء تقرير الأداء")
            test_results['passed_tests'] += 1
        else:
            print("❌ فشل في إنشاء تقرير الأداء")
            test_results['failed_tests'] += 1
        
        test_results['total_tests'] += 3
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مراقبة الأداء: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 7: التكامل مع الأنظمة الموجودة
    print("\n🧪 اختبار 7: التكامل مع الأنظمة الموجودة")
    print("-" * 50)
    
    try:
        # اختبار استيراد الأنظمة الموجودة مع التحسينات
        import employee_management
        print("✅ تم استيراد نظام إدارة الموظفين المحسن")
        test_results['passed_tests'] += 1
        
        import leave_balance_system
        print("✅ تم استيراد نظام رصيد الإجازات المحسن")
        test_results['passed_tests'] += 1
        
        import leave_management
        print("✅ تم استيراد نظام إدارة الإجازات")
        test_results['passed_tests'] += 1
        
        test_results['total_tests'] += 3
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # عرض النتائج النهائية
    print("\n" + "=" * 80)
    print("📊 ملخص نتائج الاختبار:")
    print(f"   📈 إجمالي الاختبارات: {test_results['total_tests']}")
    print(f"   ✅ نجح: {test_results['passed_tests']}")
    print(f"   ❌ فشل: {test_results['failed_tests']}")
    print(f"   ⚠️ تحذيرات: {test_results['warnings']}")
    
    success_rate = (test_results['passed_tests'] / test_results['total_tests']) * 100 if test_results['total_tests'] > 0 else 0
    print(f"   📊 معدل النجاح: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("\n🎉 ممتاز! النظام المحسن يعمل بشكل مثالي")
    elif success_rate >= 75:
        print("\n✅ جيد! النظام المحسن يعمل بشكل جيد مع بعض التحسينات المطلوبة")
    elif success_rate >= 50:
        print("\n⚠️ متوسط! النظام يحتاج إلى تحسينات إضافية")
    else:
        print("\n❌ ضعيف! النظام يحتاج إلى مراجعة شاملة")
    
    print("=" * 80)
    
    return test_results

if __name__ == "__main__":
    print("🔧 اختبار النظام المحسن لإدارة الموارد البشرية")
    print(f"📅 التاريخ: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 إصدار Python: {sys.version}")
    print(f"📁 المجلد الحالي: {os.getcwd()}")
    
    results = test_enhanced_features()
    
    # حفظ النتائج في ملف
    try:
        with open("test_results.txt", "w", encoding="utf-8") as f:
            f.write(f"نتائج اختبار النظام المحسن\n")
            f.write(f"التاريخ: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"إجمالي الاختبارات: {results['total_tests']}\n")
            f.write(f"نجح: {results['passed_tests']}\n")
            f.write(f"فشل: {results['failed_tests']}\n")
            f.write(f"معدل النجاح: {(results['passed_tests'] / results['total_tests']) * 100:.1f}%\n")
        
        print(f"\n💾 تم حفظ النتائج في ملف: test_results.txt")
    
    except Exception as e:
        print(f"❌ فشل في حفظ النتائج: {e}")
    
    print("\n🏁 انتهى الاختبار")
