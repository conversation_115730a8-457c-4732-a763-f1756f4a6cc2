#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لإصلاح البحث
Quick Search Fix Test
"""

import tkinter as tk
import sys
import os

def test_employee_management_search():
    """اختبار البحث في نظام إدارة الموظفين"""
    print("🔍 اختبار البحث في نظام إدارة الموظفين")
    print("=" * 60)
    
    try:
        # استيراد النظام
        print("📦 استيراد نظام إدارة الموظفين...")
        import employee_management
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة اختبار...")
        root = tk.Tk()
        root.title("اختبار البحث")
        root.geometry("1200x800")
        
        # إنشاء النظام
        print("🔧 إنشاء نظام إدارة الموظفين...")
        current_user = {"username": "test", "name": "مستخدم اختبار"}
        emp_system = employee_management.EmployeeManagementSystem(root, current_user)
        print("✅ تم إنشاء النظام بنجاح")
        
        # اختبار متغيرات البحث
        print("\n🔍 اختبار متغيرات البحث:")
        
        # فحص وجود متغير البحث
        if hasattr(emp_system, 'search_var') and emp_system.search_var is not None:
            print("✅ متغير البحث موجود")
            
            # اختبار تعيين قيمة
            try:
                emp_system.search_var.set("اختبار")
                search_value = emp_system.search_var.get()
                print(f"✅ تم تعيين قيمة البحث: '{search_value}'")
            except Exception as e:
                print(f"❌ خطأ في تعيين قيمة البحث: {e}")
        else:
            print("❌ متغير البحث غير موجود")
        
        # فحص وجود متغيرات التصفية
        filter_vars = [
            ('filter_workplace_var', 'مكان العمل'),
            ('filter_qualification_var', 'المؤهل'),
            ('filter_nationality_var', 'الجنسية')
        ]
        
        for var_name, display_name in filter_vars:
            if hasattr(emp_system, var_name) and getattr(emp_system, var_name) is not None:
                print(f"✅ متغير {display_name} موجود")
            else:
                print(f"❌ متغير {display_name} غير موجود")
        
        # فحص وجود الجدول
        if hasattr(emp_system, 'employees_table') and emp_system.employees_table is not None:
            print("✅ جدول الموظفين موجود")
        else:
            print("❌ جدول الموظفين غير موجود")
        
        # فحص وجود البيانات
        if hasattr(emp_system, 'employees_data'):
            print(f"✅ بيانات الموظفين موجودة: {len(emp_system.employees_data)} موظف")
        else:
            print("❌ بيانات الموظفين غير موجودة")
        
        # اختبار دالة البحث
        print("\n🧪 اختبار دالة البحث:")
        try:
            emp_system.perform_search()
            print("✅ دالة البحث تعمل بدون أخطاء")
        except Exception as e:
            print(f"❌ خطأ في دالة البحث: {e}")
        
        # اختبار دالة التصفية
        print("\n🧪 اختبار دالة التصفية:")
        try:
            emp_system.apply_filters()
            print("✅ دالة التصفية تعمل بدون أخطاء")
        except Exception as e:
            print(f"❌ خطأ في دالة التصفية: {e}")
        
        # اختبار دالة التشخيص
        print("\n🧪 اختبار دالة التشخيص:")
        try:
            emp_system.debug_search_system()
            print("✅ دالة التشخيص تعمل بدون أخطاء")
        except Exception as e:
            print(f"❌ خطأ في دالة التشخيص: {e}")
        
        print("\n" + "=" * 60)
        print("🎉 انتهى الاختبار - النظام جاهز للاستخدام!")
        print("💡 يمكنك الآن فتح النظام واختبار البحث يدوياً")
        
        # عرض النافذة للاختبار اليدوي
        print("\n🖥️ عرض النافذة للاختبار اليدوي...")
        print("📝 جرب كتابة نص في حقل البحث")
        print("🔧 استخدم زر 'تشخيص' إذا واجهت مشكلة")
        
        # إضافة زر إغلاق سريع
        close_btn = tk.Button(root, text="❌ إغلاق الاختبار", 
                             command=root.destroy,
                             bg="#e74c3c", fg="white", 
                             font=("Arial", 12, "bold"))
        close_btn.pack(side=tk.BOTTOM, pady=10)
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def test_search_logic_only():
    """اختبار منطق البحث فقط بدون واجهة"""
    print("\n🔍 اختبار منطق البحث فقط")
    print("-" * 40)
    
    # بيانات تجريبية
    test_data = [
        {
            "الرقم الوظيفي": "001",
            "الاسم العربي": "أحمد محمد علي",
            "الاسم الإنجليزي": "Ahmed Mohamed Ali",
            "المسمى الوظيفي": "موظف",
            "الرقم الوطني": "123456789",
            "المؤهل": "بكالوريوس",
            "مكان العمل الحالي": "الإدارة العامة",
            "التخصص": "إدارة أعمال",
            "الجنسية": "ليبي"
        },
        {
            "الرقم الوظيفي": "002",
            "الاسم العربي": "فاطمة أحمد سالم",
            "الاسم الإنجليزي": "Fatima Ahmed Salem",
            "المسمى الوظيفي": "موظفة",
            "الرقم الوطني": "987654321",
            "المؤهل": "ماجستير",
            "مكان العمل الحالي": "إدارة الموارد البشرية",
            "التخصص": "موارد بشرية",
            "الجنسية": "ليبي"
        }
    ]
    
    def search_in_data(search_term, data):
        """منطق البحث"""
        search_term = search_term.strip().lower()
        if not search_term:
            return data
        
        search_fields = ["الرقم الوظيفي", "الاسم العربي", "الاسم الإنجليزي", "المسمى الوظيفي", "الرقم الوطني"]
        additional_fields = ["المؤهل", "مكان العمل الحالي", "التخصص", "الجنسية"]
        all_fields = search_fields + additional_fields
        
        filtered = []
        for emp in data:
            for field in all_fields:
                value = str(emp.get(field, "")).lower()
                if search_term in value:
                    filtered.append(emp)
                    print(f"✅ وجد '{search_term}' في {field}: {emp.get('الاسم العربي', 'غير محدد')}")
                    break
        
        return filtered
    
    # اختبارات مختلفة
    test_cases = ["أحمد", "محمد", "001", "موظف", "بكالوريوس", "الإدارة", "ليبي"]
    
    print(f"📊 إجمالي البيانات: {len(test_data)}")
    
    for test_term in test_cases:
        print(f"\n🔍 البحث عن: '{test_term}'")
        results = search_in_data(test_term, test_data)
        print(f"📊 النتائج: {len(results)} من أصل {len(test_data)}")
        
        if not results:
            print("   لا توجد نتائج")

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار إصلاح البحث في نظام إدارة الموظفين")
    print("=" * 60)
    
    # اختبار منطق البحث أولاً
    test_search_logic_only()
    
    print("\n" + "=" * 60)
    
    # اختبار النظام الكامل
    response = input("هل تريد اختبار النظام الكامل؟ (y/n): ")
    if response.lower() in ['y', 'yes', 'نعم', 'ن']:
        test_employee_management_search()
    else:
        print("🏁 انتهى الاختبار")

if __name__ == "__main__":
    main()
