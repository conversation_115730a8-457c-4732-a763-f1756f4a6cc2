#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة التنبيه التلقائي وملف Excel للترقيات
Test Promotion Alerts and Excel File Features
"""

import sys
import datetime
import os

def test_promotion_alerts_and_excel():
    """اختبار ميزة التنبيه التلقائي وملف Excel للترقيات"""
    print("🔔 اختبار ميزة التنبيه التلقائي وملف Excel للترقيات")
    print("=" * 70)
    
    test_results = {
        'total_tests': 0,
        'passed_tests': 0,
        'failed_tests': 0
    }
    
    # اختبار 1: استيراد النظام المحدث
    print("\n🧪 اختبار 1: استيراد النظام المحدث")
    print("-" * 50)
    
    try:
        import promotion_system_safe
        print("✅ تم استيراد نظام الترقيات الآمن المحدث")
        test_results['passed_tests'] += 1
    except ImportError as e:
        print(f"❌ فشل في استيراد النظام: {e}")
        test_results['failed_tests'] += 1
    test_results['total_tests'] += 1
    
    # اختبار 2: فحص الدوال الجديدة
    print("\n🧪 اختبار 2: فحص الدوال الجديدة")
    print("-" * 50)
    
    try:
        from promotion_system_safe import PromotionSystem
        
        new_methods = [
            'show_promotion_alert',
            'create_promotions_file',
            'open_promotions_file',
            'refresh_promotions_from_file'
        ]
        
        for method_name in new_methods:
            if hasattr(PromotionSystem, method_name):
                print(f"✅ دالة {method_name}: موجودة")
                test_results['passed_tests'] += 1
            else:
                print(f"❌ دالة {method_name}: مفقودة")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
    
    except Exception as e:
        print(f"❌ خطأ في فحص الدوال: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 3: فحص متغيرات ملف الترقيات
    print("\n🧪 اختبار 3: فحص متغيرات ملف الترقيات")
    print("-" * 50)
    
    try:
        import tkinter as tk
        from promotion_system_safe import PromotionSystem
        
        # إنشاء نافذة وهمية للاختبار
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # إنشاء نظام الترقيات
        promotion_sys = PromotionSystem(root)
        
        # فحص المتغيرات الجديدة
        if hasattr(promotion_sys, 'promotions_file'):
            print(f"✅ متغير promotions_file: {promotion_sys.promotions_file}")
            test_results['passed_tests'] += 1
        else:
            print("❌ متغير promotions_file: مفقود")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
        
        if hasattr(promotion_sys, 'promotions_sheet_name'):
            print(f"✅ متغير promotions_sheet_name: {promotion_sys.promotions_sheet_name}")
            test_results['passed_tests'] += 1
        else:
            print("❌ متغير promotions_sheet_name: مفقود")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
        
        root.destroy()
    
    except Exception as e:
        print(f"❌ خطأ في فحص المتغيرات: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 4: فحص إنشاء ملف الترقيات
    print("\n🧪 اختبار 4: فحص إنشاء ملف الترقيات")
    print("-" * 50)
    
    try:
        import tkinter as tk
        from promotion_system_safe import PromotionSystem
        
        # إنشاء نافذة وهمية للاختبار
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # إنشاء نظام الترقيات
        promotion_sys = PromotionSystem(root)
        
        # محاولة إنشاء ملف الترقيات
        promotion_sys.create_promotions_file()
        
        # فحص وجود الملف
        if os.path.exists(promotion_sys.promotions_file):
            print(f"✅ تم إنشاء ملف الترقيات: {promotion_sys.promotions_file}")
            test_results['passed_tests'] += 1
            
            # فحص حجم الملف
            file_size = os.path.getsize(promotion_sys.promotions_file)
            if file_size > 0:
                print(f"✅ حجم الملف: {file_size} بايت")
                test_results['passed_tests'] += 1
            else:
                print("❌ الملف فارغ")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
        else:
            print("❌ لم يتم إنشاء ملف الترقيات")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
        
        root.destroy()
    
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء الملف: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 5: فحص الأزرار الجديدة
    print("\n🧪 اختبار 5: فحص الأزرار الجديدة")
    print("-" * 50)
    
    try:
        import tkinter as tk
        from promotion_system_safe import PromotionSystem
        
        # إنشاء نافذة وهمية للاختبار
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # إنشاء نظام الترقيات
        promotion_sys = PromotionSystem(root)
        
        # فحص عدد الأزرار (يجب أن يكون 6 أزرار الآن)
        buttons_frame = tk.Frame(root)
        promotion_sys.create_control_buttons(buttons_frame)
        
        button_count = len(buttons_frame.winfo_children())
        expected_buttons = 6  # 6 أزرار جديدة
        
        if button_count == expected_buttons:
            print(f"✅ عدد الأزرار صحيح: {button_count}")
            test_results['passed_tests'] += 1
        else:
            print(f"❌ عدد الأزرار غير صحيح: {button_count} (متوقع: {expected_buttons})")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
        
        root.destroy()
    
    except Exception as e:
        print(f"❌ خطأ في فحص الأزرار: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 6: فحص مكتبة openpyxl
    print("\n🧪 اختبار 6: فحص مكتبة openpyxl")
    print("-" * 50)
    
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        print("✅ مكتبة openpyxl متاحة مع جميع الميزات")
        test_results['passed_tests'] += 1
    except ImportError:
        print("❌ مكتبة openpyxl غير متاحة أو ناقصة")
        test_results['failed_tests'] += 1
    test_results['total_tests'] += 1
    
    return test_results

def main():
    """تشغيل جميع الاختبارات"""
    print("🔔 اختبار ميزة التنبيه التلقائي وملف Excel للترقيات")
    print(f"📅 التاريخ: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 إصدار Python: {sys.version}")
    print()
    
    # تشغيل الاختبارات
    results = test_promotion_alerts_and_excel()
    
    # عرض النتائج النهائية
    print("\n" + "=" * 70)
    print("📊 ملخص نتائج الاختبار:")
    print(f"   📈 إجمالي الاختبارات: {results['total_tests']}")
    print(f"   ✅ نجح: {results['passed_tests']}")
    print(f"   ❌ فشل: {results['failed_tests']}")
    
    success_rate = (results['passed_tests'] / results['total_tests']) * 100 if results['total_tests'] > 0 else 0
    print(f"   📊 معدل النجاح: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("\n🎉 ممتاز! ميزة التنبيه التلقائي وملف Excel تعمل بشكل مثالي")
    elif success_rate >= 75:
        print("\n✅ جيد! الميزات تعمل بشكل جيد مع بعض التحسينات المطلوبة")
    elif success_rate >= 50:
        print("\n⚠️ متوسط! الميزات تحتاج إلى تحسينات إضافية")
    else:
        print("\n❌ ضعيف! الميزات تحتاج إلى مراجعة شاملة")
    
    print("=" * 70)
    
    # عرض تعليمات الاستخدام
    print("\n📋 كيفية استخدام الميزات الجديدة:")
    print("1. افتح نظام الترقيات من القائمة الرئيسية")
    print("2. سيظهر تنبيه تلقائي إذا كان هناك موظفين مستحقين للترقية")
    print("3. اضغط 'فتح ملف الترقيات' لفتح ملف Excel الخاص")
    print("4. حرر البيانات في الملف حسب الحاجة")
    print("5. اضغط 'تحديث الترقيات' لإعادة تحميل البيانات من الملف")
    
    print("\n🏁 انتهى اختبار ميزة التنبيه التلقائي وملف Excel")

if __name__ == "__main__":
    main()
