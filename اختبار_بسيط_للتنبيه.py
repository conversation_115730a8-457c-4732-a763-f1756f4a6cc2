#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لإصلاح التنبيه المكرر
Simple Test for Duplicate Alert Fix
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_leave_balance_system():
    """اختبار نظام رصيد الإجازات مباشرة"""
    print("🔔 اختبار نظام رصيد الإجازات مباشرة")
    print("=" * 60)
    
    try:
        # استيراد نظام رصيد الإجازات
        print("📦 استيراد نظام رصيد الإجازات...")
        import leave_balance_system
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة الاختبار...")
        root = tk.Tk()
        root.title("اختبار نظام رصيد الإجازات")
        root.geometry("1200x800")
        
        # مراقب الرسائل
        message_count = {"count": 0, "messages": []}
        original_showinfo = messagebox.showinfo
        
        def mock_showinfo(title, message):
            message_count["count"] += 1
            message_count["messages"].append(f"{title}: {message}")
            print(f"📢 رسالة #{message_count['count']}: {title} - {message}")
            return original_showinfo(title, message)
        
        messagebox.showinfo = mock_showinfo
        
        # إنشاء واجهة الاختبار
        test_frame = tk.Frame(root, bg="#f0f8ff", padx=20, pady=20)
        test_frame.pack(fill=tk.BOTH, expand=True)
        
        title_label = tk.Label(test_frame, text="🔔 اختبار إصلاح التنبيه المكرر", 
                              font=("Arial", 16, "bold"), bg="#f0f8ff", fg="#1e3a8a")
        title_label.pack(pady=10)
        
        result_var = tk.StringVar()
        result_label = tk.Label(test_frame, textvariable=result_var, 
                               font=("Arial", 12, "bold"), bg="#f0f8ff", fg="#1e3a8a")
        result_label.pack(pady=10)
        
        def test_with_auto_refresh():
            """اختبار مع التحديث التلقائي"""
            print("\n🔄 اختبار مع التحديث التلقائي (auto_refresh=True):")
            message_count["count"] = 0
            message_count["messages"] = []
            
            try:
                # إنشاء نافذة مؤقتة
                temp_root = tk.Toplevel(root)
                temp_root.withdraw()
                
                # إنشاء النظام مع التحديث التلقائي
                balance_system = leave_balance_system.LeaveBalanceSystem(temp_root, auto_refresh=True)
                
                # انتظار قصير
                root.after(2000, lambda: check_messages("مع التحديث التلقائي", temp_root))
                
            except Exception as e:
                print(f"   ❌ خطأ: {e}")
                result_var.set(f"❌ خطأ في الاختبار: {e}")
        
        def test_without_auto_refresh():
            """اختبار بدون التحديث التلقائي"""
            print("\n🚫 اختبار بدون التحديث التلقائي (auto_refresh=False):")
            message_count["count"] = 0
            message_count["messages"] = []
            
            try:
                # إنشاء نافذة مؤقتة
                temp_root = tk.Toplevel(root)
                temp_root.withdraw()
                
                # إنشاء النظام بدون التحديث التلقائي
                balance_system = leave_balance_system.LeaveBalanceSystem(temp_root, auto_refresh=False)
                
                # انتظار قصير
                root.after(2000, lambda: check_messages("بدون التحديث التلقائي", temp_root))
                
            except Exception as e:
                print(f"   ❌ خطأ: {e}")
                result_var.set(f"❌ خطأ في الاختبار: {e}")
        
        def check_messages(test_name, temp_window):
            """فحص الرسائل"""
            count = message_count["count"]
            messages = message_count["messages"]
            
            print(f"\n📊 نتائج اختبار {test_name}:")
            print(f"   عدد الرسائل: {count}")
            
            if messages:
                print("   الرسائل:")
                for i, msg in enumerate(messages, 1):
                    print(f"     {i}. {msg}")
            
            if count == 0:
                result_var.set(f"✅ {test_name}: لا توجد رسائل (ممتاز!)")
                print(f"   ✅ {test_name}: لا توجد رسائل - الإصلاح نجح!")
            elif count == 1:
                result_var.set(f"⚠️ {test_name}: رسالة واحدة")
                print(f"   ⚠️ {test_name}: رسالة واحدة")
            else:
                result_var.set(f"❌ {test_name}: {count} رسائل")
                print(f"   ❌ {test_name}: {count} رسائل")
            
            # إغلاق النافذة المؤقتة
            try:
                temp_window.destroy()
            except:
                pass
        
        def test_manual_refresh():
            """اختبار التحديث اليدوي"""
            print("\n🔄 اختبار التحديث اليدوي:")
            message_count["count"] = 0
            message_count["messages"] = []
            
            try:
                # إنشاء نافذة مؤقتة
                temp_root = tk.Toplevel(root)
                temp_root.withdraw()
                
                # إنشاء النظام بدون التحديث التلقائي
                balance_system = leave_balance_system.LeaveBalanceSystem(temp_root, auto_refresh=False)
                
                # تحديث يدوي مع إظهار الرسالة
                balance_system.refresh_all_balances(show_message=True)
                
                # انتظار قصير
                root.after(1000, lambda: check_messages("التحديث اليدوي", temp_root))
                
            except Exception as e:
                print(f"   ❌ خطأ: {e}")
                result_var.set(f"❌ خطأ في التحديث اليدوي: {e}")
        
        def show_comparison():
            """عرض مقارنة النتائج"""
            comparison = """
📊 مقارنة النتائج:

🔴 قبل الإصلاح:
   • auto_refresh=True → رسالة تحديث تلقائية
   • استدعاءات متعددة → رسائل مكررة
   • تجربة مستخدم مزعجة

🟢 بعد الإصلاح:
   • auto_refresh=False للنسخ المؤقتة → لا توجد رسائل
   • show_message=False للتحديث التلقائي → لا توجد رسائل
   • show_message=True للتحديث اليدوي → رسالة واحدة فقط

✅ النتيجة:
   • إلغاء التنبيهات المكررة
   • الاحتفاظ بالوظائف الأساسية
   • تجربة مستخدم محسنة

🎯 الهدف المحقق:
   نظام هادئ وفعال بدون إزعاج
            """
            
            messagebox.showinfo("مقارنة النتائج", comparison)
            result_var.set("📊 تم عرض مقارنة النتائج")
        
        def restore_and_exit():
            """استعادة الدالة الأصلية والخروج"""
            messagebox.showinfo = original_showinfo
            root.destroy()
        
        # أزرار الاختبار
        buttons_frame = tk.Frame(test_frame, bg="#f0f8ff")
        buttons_frame.pack(pady=20)
        
        tk.Button(buttons_frame, text="🔄 اختبار مع التحديث التلقائي",
                 command=test_with_auto_refresh,
                 bg="#3b82f6", fg="white", font=("Arial", 11), padx=10, pady=5).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="🚫 اختبار بدون التحديث التلقائي",
                 command=test_without_auto_refresh,
                 bg="#10b981", fg="white", font=("Arial", 11), padx=10, pady=5).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="🔄 اختبار التحديث اليدوي",
                 command=test_manual_refresh,
                 bg="#f59e0b", fg="white", font=("Arial", 11), padx=10, pady=5).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="📊 مقارنة النتائج",
                 command=show_comparison,
                 bg="#8b5cf6", fg="white", font=("Arial", 11), padx=10, pady=5).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="❌ إغلاق",
                 command=restore_and_exit,
                 bg="#ef4444", fg="white", font=("Arial", 11), padx=10, pady=5).pack(side=tk.LEFT, padx=5)
        
        # معلومات
        info_text = """
🎯 الهدف: اختبار إصلاح التنبيه المكرر "تم تحديث جميع الأرصدة بنجاح"

🔧 الإصلاحات المطبقة:
• إضافة معامل auto_refresh للتحكم في التحديث التلقائي
• إضافة معامل show_message للتحكم في إظهار الرسائل
• تحديث الاستدعاءات في hr_system.py لاستخدام auto_refresh=False

📋 التعليمات:
1. اضغط على الأزرار لاختبار السيناريوهات المختلفة
2. راقب النتائج في وحدة التحكم
3. تحقق من عدد الرسائل المعروضة
        """
        
        info_label = tk.Label(test_frame, text=info_text, 
                             font=("Arial", 10), bg="#f0f8ff", fg="#1e3a8a", justify=tk.LEFT)
        info_label.pack(pady=10)
        
        print("\n🎉 انتهى إعداد الاختبار - الواجهة التفاعلية جاهزة")
        print("\n📋 تعليمات الاختبار:")
        print("   • اضغط على الأزرار لاختبار السيناريوهات المختلفة")
        print("   • راقب وحدة التحكم للرسائل")
        print("   • تحقق من النتائج")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    print("🔔 اختبار بسيط لإصلاح التنبيه المكرر")
    print("=" * 60)
    
    test_leave_balance_system()

if __name__ == "__main__":
    main()
