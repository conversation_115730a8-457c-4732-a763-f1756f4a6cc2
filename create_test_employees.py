#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف بيانات موظفين للاختبار مع مستحقين للترقية
Create Test Employees Data with Promotion Eligible Employees
"""

from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from datetime import datetime, timedelta
import random

def create_test_employees_data():
    """إنشاء ملف بيانات موظفين للاختبار"""
    print("👥 إنشاء ملف بيانات موظفين للاختبار")
    print("=" * 60)
    
    # إنشاء ملف Excel جديد
    wb = Workbook()
    ws = wb.active
    ws.title = "الموظفين"
    
    # العناوين
    headers = [
        "الرقم الوظيفي", "الاسم العربي", "الاسم الإنجليزي", "الرقم المالي", 
        "الرقم الوطني", "المؤهل", "مكان العمل الحالي", "رقم الحساب", 
        "اسم المصرف", "تاريخ أول مباشرة", "الدرجة الحالية", "العلاوة", 
        "تاريخ الدرجة الحالية", "التخصص", "المسمى الوظيفي", "تاريخ التعيين", 
        "رقم الهاتف", "الجنسية", "تاريخ الميلاد"
    ]
    
    # تنسيق العناوين
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # كتابة العناوين
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border
    
    # بيانات الموظفين للاختبار
    employees_data = [
        # موظفين مستحقين للترقية (4+ سنوات في الدرجة)
        {
            "الرقم الوظيفي": "EMP001",
            "الاسم العربي": "أحمد محمد علي",
            "الاسم الإنجليزي": "Ahmed Mohammed Ali",
            "الرقم المالي": "FIN001",
            "الرقم الوطني": "1234567890123",
            "المؤهل": "بكالوريوس",
            "مكان العمل الحالي": "الإدارة العامة",
            "رقم الحساب": "ACC001",
            "اسم المصرف": "مصرف الجمهورية",
            "تاريخ أول مباشرة": "2015-01-15",
            "الدرجة الحالية": "8",
            "العلاوة": "5",
            "تاريخ الدرجة الحالية": "2020-01-15",  # 5 سنوات - مستحق
            "التخصص": "إدارة أعمال",
            "المسمى الوظيفي": "موظف",
            "تاريخ التعيين": "2015-01-15",
            "رقم الهاتف": "0911234567",
            "الجنسية": "ليبي",
            "تاريخ الميلاد": "1985-05-20"
        },
        {
            "الرقم الوظيفي": "EMP002",
            "الاسم العربي": "فاطمة أحمد سالم",
            "الاسم الإنجليزي": "Fatima Ahmed Salem",
            "الرقم المالي": "FIN002",
            "الرقم الوطني": "1234567890124",
            "المؤهل": "ماجستير",
            "مكان العمل الحالي": "قسم الموارد البشرية",
            "رقم الحساب": "ACC002",
            "اسم المصرف": "مصرف الوحدة",
            "تاريخ أول مباشرة": "2016-03-10",
            "الدرجة الحالية": "10",
            "العلاوة": "3",
            "تاريخ الدرجة الحالية": "2019-03-10",  # 6 سنوات - مستحق
            "التخصص": "موارد بشرية",
            "المسمى الوظيفي": "موظفة",
            "تاريخ التعيين": "2016-03-10",
            "رقم الهاتف": "0921234567",
            "الجنسية": "ليبية",
            "تاريخ الميلاد": "1988-08-15"
        },
        {
            "الرقم الوظيفي": "EMP003",
            "الاسم العربي": "محمد سالم عبدالله",
            "الاسم الإنجليزي": "Mohammed Salem Abdullah",
            "الرقم المالي": "FIN003",
            "الرقم الوطني": "1234567890125",
            "المؤهل": "بكالوريوس",
            "مكان العمل الحالي": "قسم المالية",
            "رقم الحساب": "ACC003",
            "اسم المصرف": "مصرف التجارة والتنمية",
            "تاريخ أول مباشرة": "2017-06-01",
            "الدرجة الحالية": "7",
            "العلاوة": "2",
            "تاريخ الدرجة الحالية": "2020-06-01",  # 4.5 سنة - مستحق
            "التخصص": "محاسبة",
            "المسمى الوظيفي": "موظف",
            "تاريخ التعيين": "2017-06-01",
            "رقم الهاتف": "0941234567",
            "الجنسية": "ليبي",
            "تاريخ الميلاد": "1990-12-03"
        },
        # موظفين قريبين من الاستحقاق (3+ سنوات)
        {
            "الرقم الوظيفي": "EMP004",
            "الاسم العربي": "عائشة علي محمد",
            "الاسم الإنجليزي": "Aisha Ali Mohammed",
            "الرقم المالي": "FIN004",
            "الرقم الوطني": "1234567890126",
            "المؤهل": "بكالوريوس",
            "مكان العمل الحالي": "قسم التقنية",
            "رقم الحساب": "ACC004",
            "اسم المصرف": "مصرف الجمهورية",
            "تاريخ أول مباشرة": "2018-09-15",
            "الدرجة الحالية": "6",
            "العلاوة": "1",
            "تاريخ الدرجة الحالية": "2021-09-15",  # 3.25 سنة - قريب
            "التخصص": "تقنية معلومات",
            "المسمى الوظيفي": "موظفة",
            "تاريخ التعيين": "2018-09-15",
            "رقم الهاتف": "0951234567",
            "الجنسية": "ليبية",
            "تاريخ الميلاد": "1992-04-18"
        },
        # موظفين غير مستحقين (أقل من 3 سنوات)
        {
            "الرقم الوظيفي": "EMP005",
            "الاسم العربي": "خالد عبدالرحمن سعد",
            "الاسم الإنجليزي": "Khalid Abdulrahman Saad",
            "الرقم المالي": "FIN005",
            "الرقم الوطني": "1234567890127",
            "المؤهل": "بكالوريوس",
            "مكان العمل الحالي": "قسم الخدمات",
            "رقم الحساب": "ACC005",
            "اسم المصرف": "مصرف الوحدة",
            "تاريخ أول مباشرة": "2022-01-10",
            "الدرجة الحالية": "5",
            "العلاوة": "0",
            "تاريخ الدرجة الحالية": "2022-01-10",  # 2.9 سنة - غير مستحق
            "التخصص": "إدارة عامة",
            "المسمى الوظيفي": "موظف",
            "تاريخ التعيين": "2022-01-10",
            "رقم الهاتف": "0911234568",
            "الجنسية": "ليبي",
            "تاريخ الميلاد": "1995-07-22"
        },
        # معلمين (لا يستحقون ترقيات حسب النظام)
        {
            "الرقم الوظيفي": "TCH001",
            "الاسم العربي": "سعاد محمد الطاهر",
            "الاسم الإنجليزي": "Suad Mohammed Al-Taher",
            "الرقم المالي": "FIN006",
            "الرقم الوطني": "1234567890128",
            "المؤهل": "بكالوريوس تربية",
            "مكان العمل الحالي": "مدرسة النور",
            "رقم الحساب": "ACC006",
            "اسم المصرف": "مصرف التجارة والتنمية",
            "تاريخ أول مباشرة": "2018-09-01",
            "الدرجة الحالية": "9",
            "العلاوة": "4",
            "تاريخ الدرجة الحالية": "2019-09-01",  # 5+ سنوات لكن معلمة
            "التخصص": "رياضيات",
            "المسمى الوظيفي": "معلمة",
            "تاريخ التعيين": "2018-09-01",
            "رقم الهاتف": "0921234568",
            "الجنسية": "ليبية",
            "تاريخ الميلاد": "1987-11-12"
        },
        # موظف وصل للحد الأقصى
        {
            "الرقم الوظيفي": "EMP006",
            "الاسم العربي": "عبدالسلام أحمد قاسم",
            "الاسم الإنجليزي": "Abdulsalam Ahmed Qasim",
            "الرقم المالي": "FIN007",
            "الرقم الوطني": "1234567890129",
            "المؤهل": "دكتوراه",
            "مكان العمل الحالي": "الإدارة العليا",
            "رقم الحساب": "ACC007",
            "اسم المصرف": "مصرف الجمهورية",
            "تاريخ أول مباشرة": "2010-01-01",
            "الدرجة الحالية": "15",  # الحد الأقصى
            "العلاوة": "10",
            "تاريخ الدرجة الحالية": "2018-01-01",  # 6+ سنوات لكن وصل للحد الأقصى
            "التخصص": "إدارة عليا",
            "المسمى الوظيفي": "موظف",
            "تاريخ التعيين": "2010-01-01",
            "رقم الهاتف": "0941234568",
            "الجنسية": "ليبي",
            "تاريخ الميلاد": "1975-03-08"
        }
    ]
    
    # كتابة بيانات الموظفين
    for row, emp in enumerate(employees_data, 2):
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=emp.get(header, ""))
            cell.border = border
            cell.alignment = Alignment(horizontal="center", vertical="center")
            
            # تلوين الصفوف بالتناوب
            if row % 2 == 0:
                cell.fill = PatternFill(start_color="F2F2F2", end_color="F2F2F2", fill_type="solid")
    
    # تعديل عرض الأعمدة
    for col in range(1, len(headers) + 1):
        ws.column_dimensions[chr(64 + col)].width = 15
    
    # حفظ الملف
    filename = "employees_data.xlsx"
    wb.save(filename)
    
    print(f"✅ تم إنشاء ملف البيانات: {filename}")
    print(f"📊 عدد الموظفين: {len(employees_data)}")
    
    # إحصائيات الموظفين
    eligible_count = 0
    near_count = 0
    not_eligible_count = 0
    max_grade_count = 0
    teachers_count = 0
    
    current_date = datetime.now()
    
    for emp in employees_data:
        job_title = emp.get("المسمى الوظيفي", "")
        grade_date_str = emp.get("تاريخ الدرجة الحالية", "")
        current_grade = emp.get("الدرجة الحالية", "")
        
        if "معلم" in job_title:
            teachers_count += 1
            continue
        
        try:
            grade_date = datetime.strptime(grade_date_str, "%Y-%m-%d")
            years_in_grade = (current_date - grade_date).days / 365.25
            current_grade_num = int(current_grade)
            
            if current_grade_num >= 15:
                max_grade_count += 1
            elif years_in_grade >= 4:
                eligible_count += 1
            elif years_in_grade >= 3:
                near_count += 1
            else:
                not_eligible_count += 1
        except:
            not_eligible_count += 1
    
    print("\n📈 إحصائيات الترقيات المتوقعة:")
    print(f"   ⬆️ مستحق للترقية: {eligible_count} موظف")
    print(f"   ⏳ قريب من الاستحقاق: {near_count} موظف")
    print(f"   ❌ غير مستحق: {not_eligible_count} موظف")
    print(f"   🔝 وصل للحد الأقصى: {max_grade_count} موظف")
    print(f"   👨‍🏫 معلمين (مستثنون): {teachers_count} موظف")
    
    return filename, len(employees_data), eligible_count

def main():
    """تشغيل إنشاء ملف البيانات"""
    print("👥 إنشاء ملف بيانات موظفين للاختبار")
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        filename, total_employees, eligible_count = create_test_employees_data()
        
        print("\n" + "=" * 60)
        print("🎉 تم إنشاء ملف البيانات بنجاح!")
        print(f"📁 اسم الملف: {filename}")
        print(f"👥 إجمالي الموظفين: {total_employees}")
        print(f"⬆️ مستحقين للترقية: {eligible_count}")
        
        if eligible_count > 0:
            print(f"\n🔔 سيظهر تنبيه للترقيات عند فتح النظام!")
            print(f"📋 سيتم إنشاء ملف ترقيات.xlsx مع {eligible_count} موظف مستحق")
        
        print("\n📋 الخطوات التالية:")
        print("1. افتح نظام الترقيات")
        print("2. سيظهر تنبيه تلقائي للموظفين المستحقين")
        print("3. اضغط 'فتح ملف الترقيات' لرؤية البيانات")
        print("4. يمكن تحرير الملف في Excel")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف البيانات: {e}")
    
    print("\n🏁 انتهى إنشاء ملف البيانات")

if __name__ == "__main__":
    main()
