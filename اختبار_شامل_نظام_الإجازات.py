#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لنظام الإجازات
Comprehensive Leave Management System Test
"""

import tkinter as tk
from tkinter import messagebox, ttk
import sys
import os
from datetime import datetime, timedelta
import traceback

class LeaveSystemTester:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔍 اختبار شامل لنظام الإجازات")
        self.root.geometry("1400x900")
        self.root.configure(bg="#f0f8ff")
        
        self.leave_dept = None
        self.test_results = []
        self.current_test = ""
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة الاختبار"""
        
        # عنوان رئيسي
        title_frame = tk.Frame(self.root, bg="#2c3e50", height=80)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)
        
        tk.Label(title_frame, text="🔍 اختبار شامل لنظام الإجازات", 
                font=("Arial", 20, "bold"), bg="#2c3e50", fg="white").pack(expand=True)
        
        # إطار المحتوى الرئيسي
        main_frame = tk.Frame(self.root, bg="#f0f8ff")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # إطار الاختبارات (يسار)
        tests_frame = tk.LabelFrame(main_frame, text="🧪 الاختبارات المتاحة", 
                                   font=("Arial", 14, "bold"), fg="#2c3e50", bg="#f0f8ff")
        tests_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # إطار النتائج (يمين)
        results_frame = tk.LabelFrame(main_frame, text="📊 نتائج الاختبارات", 
                                     font=("Arial", 14, "bold"), fg="#2c3e50", bg="#f0f8ff")
        results_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        self.setup_tests_panel(tests_frame)
        self.setup_results_panel(results_frame)
        
    def setup_tests_panel(self, parent):
        """إعداد لوحة الاختبارات"""
        
        # معلومات النظام
        info_frame = tk.LabelFrame(parent, text="ℹ️ معلومات النظام", 
                                  font=("Arial", 12, "bold"), fg="#2c3e50", bg="#f0f8ff")
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.info_text = tk.Text(info_frame, height=6, font=("Arial", 10), 
                                bg="white", fg="#2c3e50", wrap=tk.WORD)
        self.info_text.pack(fill=tk.X, padx=10, pady=10)
        
        # أزرار الاختبارات
        buttons_frame = tk.Frame(parent, bg="#f0f8ff")
        buttons_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # اختبارات أساسية
        basic_frame = tk.LabelFrame(buttons_frame, text="🔧 اختبارات أساسية", 
                                   font=("Arial", 12, "bold"), fg="#2c3e50", bg="#f0f8ff")
        basic_frame.pack(fill=tk.X, pady=5)
        
        basic_tests = [
            ("🚀 تهيئة النظام", self.test_system_initialization, "#3498db"),
            ("📊 تحميل البيانات", self.test_data_loading, "#2ecc71"),
            ("🖼️ واجهة المستخدم", self.test_user_interface, "#9b59b6"),
            ("📅 نافذة التاريخ", self.test_date_window, "#e67e22")
        ]
        
        for i, (text, command, color) in enumerate(basic_tests):
            row = i // 2
            col = i % 2
            btn = tk.Button(basic_frame, text=text, command=command,
                           bg=color, fg="white", font=("Arial", 11, "bold"),
                           padx=15, pady=8, cursor="hand2")
            btn.grid(row=row, column=col, padx=5, pady=5, sticky="ew")
        
        basic_frame.grid_columnconfigure(0, weight=1)
        basic_frame.grid_columnconfigure(1, weight=1)
        
        # اختبارات وظيفية
        functional_frame = tk.LabelFrame(buttons_frame, text="⚙️ اختبارات وظيفية", 
                                        font=("Arial", 12, "bold"), fg="#2c3e50", bg="#f0f8ff")
        functional_frame.pack(fill=tk.X, pady=5)
        
        functional_tests = [
            ("👤 اختيار الموظف", self.test_employee_selection, "#34495e"),
            ("📝 طلب إجازة", self.test_leave_request, "#27ae60"),
            ("🧮 حساب الأيام", self.test_days_calculation, "#f39c12"),
            ("✅ الموافقة", self.test_approval_system, "#e74c3c")
        ]
        
        for i, (text, command, color) in enumerate(functional_tests):
            row = i // 2
            col = i % 2
            btn = tk.Button(functional_frame, text=text, command=command,
                           bg=color, fg="white", font=("Arial", 11, "bold"),
                           padx=15, pady=8, cursor="hand2")
            btn.grid(row=row, column=col, padx=5, pady=5, sticky="ew")
        
        functional_frame.grid_columnconfigure(0, weight=1)
        functional_frame.grid_columnconfigure(1, weight=1)
        
        # اختبارات متقدمة
        advanced_frame = tk.LabelFrame(buttons_frame, text="🎯 اختبارات متقدمة", 
                                      font=("Arial", 12, "bold"), fg="#2c3e50", bg="#f0f8ff")
        advanced_frame.pack(fill=tk.X, pady=5)
        
        advanced_tests = [
            ("📊 التقارير", self.test_reports, "#8e44ad"),
            ("💾 حفظ البيانات", self.test_data_saving, "#16a085"),
            ("🔄 التحديث", self.test_refresh_functions, "#d35400"),
            ("🛡️ التحقق", self.test_validation, "#c0392b")
        ]
        
        for i, (text, command, color) in enumerate(advanced_tests):
            row = i // 2
            col = i % 2
            btn = tk.Button(advanced_frame, text=text, command=command,
                           bg=color, fg="white", font=("Arial", 11, "bold"),
                           padx=15, pady=8, cursor="hand2")
            btn.grid(row=row, column=col, padx=5, pady=5, sticky="ew")
        
        advanced_frame.grid_columnconfigure(0, weight=1)
        advanced_frame.grid_columnconfigure(1, weight=1)
        
        # أزرار التحكم
        control_frame = tk.Frame(buttons_frame, bg="#f0f8ff")
        control_frame.pack(fill=tk.X, pady=10)
        
        tk.Button(control_frame, text="🔍 تشغيل جميع الاختبارات", 
                 command=self.run_all_tests,
                 bg="#2c3e50", fg="white", font=("Arial", 14, "bold"),
                 padx=20, pady=10, cursor="hand2").pack(side=tk.LEFT, padx=5)
        
        tk.Button(control_frame, text="📋 تقرير شامل", 
                 command=self.generate_comprehensive_report,
                 bg="#27ae60", fg="white", font=("Arial", 14, "bold"),
                 padx=20, pady=10, cursor="hand2").pack(side=tk.LEFT, padx=5)
        
        tk.Button(control_frame, text="🔧 إصلاح النواقص", 
                 command=self.fix_issues,
                 bg="#e74c3c", fg="white", font=("Arial", 14, "bold"),
                 padx=20, pady=10, cursor="hand2").pack(side=tk.LEFT, padx=5)
        
    def setup_results_panel(self, parent):
        """إعداد لوحة النتائج"""
        
        # حالة الاختبار الحالي
        status_frame = tk.LabelFrame(parent, text="📊 حالة الاختبار", 
                                    font=("Arial", 12, "bold"), fg="#2c3e50", bg="#f0f8ff")
        status_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.status_var = tk.StringVar(value="🔄 جاهز للاختبار...")
        self.status_label = tk.Label(status_frame, textvariable=self.status_var,
                                    font=("Arial", 12, "bold"), fg="#2980b9", bg="#f0f8ff")
        self.status_label.pack(pady=10)
        
        # نتائج الاختبارات
        results_list_frame = tk.LabelFrame(parent, text="📝 سجل النتائج", 
                                          font=("Arial", 12, "bold"), fg="#2c3e50", bg="#f0f8ff")
        results_list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # جدول النتائج
        columns = ("الاختبار", "النتيجة", "التفاصيل", "الوقت")
        self.results_tree = ttk.Treeview(results_list_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.results_tree.heading(col, text=col)
            if col == "الاختبار":
                self.results_tree.column(col, width=150)
            elif col == "النتيجة":
                self.results_tree.column(col, width=80)
            elif col == "التفاصيل":
                self.results_tree.column(col, width=200)
            else:
                self.results_tree.column(col, width=100)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(results_list_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=scrollbar.set)
        
        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # إحصائيات
        stats_frame = tk.LabelFrame(parent, text="📈 إحصائيات", 
                                   font=("Arial", 12, "bold"), fg="#2c3e50", bg="#f0f8ff")
        stats_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.stats_text = tk.Text(stats_frame, height=4, font=("Arial", 10), 
                                 bg="white", fg="#2c3e50", wrap=tk.WORD)
        self.stats_text.pack(fill=tk.X, padx=10, pady=10)
        
    def log_result(self, test_name, result, details=""):
        """تسجيل نتيجة الاختبار"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # إضافة للجدول
        self.results_tree.insert("", tk.END, values=(test_name, result, details, timestamp))
        
        # إضافة للقائمة
        self.test_results.append({
            "test": test_name,
            "result": result,
            "details": details,
            "timestamp": timestamp
        })
        
        # تحديث الإحصائيات
        self.update_statistics()
        
        # التمرير للأسفل
        self.results_tree.see(self.results_tree.get_children()[-1])
        
    def update_statistics(self):
        """تحديث الإحصائيات"""
        total = len(self.test_results)
        passed = len([r for r in self.test_results if r["result"] == "✅ نجح"])
        failed = len([r for r in self.test_results if r["result"] == "❌ فشل"])
        warnings = len([r for r in self.test_results if r["result"] == "⚠️ تحذير"])
        
        success_rate = (passed / total * 100) if total > 0 else 0
        
        stats_text = f"""
📊 إجمالي الاختبارات: {total}
✅ نجح: {passed}
❌ فشل: {failed}
⚠️ تحذيرات: {warnings}
📈 معدل النجاح: {success_rate:.1f}%
        """
        
        self.stats_text.delete("1.0", tk.END)
        self.stats_text.insert("1.0", stats_text.strip())
        
    def test_system_initialization(self):
        """اختبار تهيئة النظام"""
        self.current_test = "تهيئة النظام"
        self.status_var.set(f"🔄 جاري اختبار: {self.current_test}")
        self.root.update()
        
        try:
            print(f"\n🧪 اختبار: {self.current_test}")
            
            # استيراد النظام
            import leave_department_system
            
            # إنشاء النظام
            self.leave_dept = leave_department_system.LeaveDepartmentSystem(self.root)
            
            # فحص الخصائص الأساسية
            required_attrs = [
                'employees_data', 'balance_data', 'leaves_data', 
                'emp_id_var', 'emp_name_var', 'leave_type_var'
            ]
            
            missing_attrs = []
            for attr in required_attrs:
                if not hasattr(self.leave_dept, attr):
                    missing_attrs.append(attr)
            
            if missing_attrs:
                self.log_result(self.current_test, "❌ فشل", f"خصائص مفقودة: {', '.join(missing_attrs)}")
                return False
            
            # تحديث معلومات النظام
            info = f"""
✅ تم تهيئة النظام بنجاح
📊 عدد الموظفين: {len(self.leave_dept.employees_data)}
💰 عدد الأرصدة: {len(self.leave_dept.balance_data)}
📝 عدد الإجازات: {len(self.leave_dept.leaves_data)}
🕒 وقت التهيئة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """
            
            self.info_text.delete("1.0", tk.END)
            self.info_text.insert("1.0", info.strip())
            
            self.log_result(self.current_test, "✅ نجح", "تم تهيئة النظام بنجاح")
            return True
            
        except Exception as e:
            error_msg = f"خطأ في التهيئة: {str(e)}"
            self.log_result(self.current_test, "❌ فشل", error_msg)
            print(f"❌ {error_msg}")
            traceback.print_exc()
            return False
        
    def test_data_loading(self):
        """اختبار تحميل البيانات"""
        self.current_test = "تحميل البيانات"
        self.status_var.set(f"🔄 جاري اختبار: {self.current_test}")
        self.root.update()
        
        try:
            if not self.leave_dept:
                self.log_result(self.current_test, "❌ فشل", "النظام غير مهيأ")
                return False
            
            print(f"\n🧪 اختبار: {self.current_test}")
            
            # فحص بيانات الموظفين
            if not self.leave_dept.employees_data:
                self.log_result(self.current_test, "⚠️ تحذير", "لا توجد بيانات موظفين")
                return False
            
            # فحص بيانات الأرصدة
            if not self.leave_dept.balance_data:
                self.log_result(self.current_test, "⚠️ تحذير", "لا توجد بيانات أرصدة")
                return False
            
            # فحص تطابق البيانات
            emp_ids = set(str(emp.get("الرقم الوظيفي", "")) for emp in self.leave_dept.employees_data)
            balance_ids = set(str(bal.get("الرقم الوظيفي", "")) for bal in self.leave_dept.balance_data)
            
            missing_balances = emp_ids - balance_ids
            extra_balances = balance_ids - emp_ids
            
            issues = []
            if missing_balances:
                issues.append(f"أرصدة مفقودة لـ {len(missing_balances)} موظف")
            if extra_balances:
                issues.append(f"أرصدة زائدة لـ {len(extra_balances)} موظف")
            
            if issues:
                self.log_result(self.current_test, "⚠️ تحذير", "; ".join(issues))
            else:
                self.log_result(self.current_test, "✅ نجح", "تم تحميل البيانات بنجاح")
            
            return True
            
        except Exception as e:
            error_msg = f"خطأ في تحميل البيانات: {str(e)}"
            self.log_result(self.current_test, "❌ فشل", error_msg)
            print(f"❌ {error_msg}")
            return False
    
    def test_user_interface(self):
        """اختبار واجهة المستخدم"""
        self.current_test = "واجهة المستخدم"
        self.status_var.set(f"🔄 جاري اختبار: {self.current_test}")
        self.root.update()
        
        try:
            if not self.leave_dept:
                self.log_result(self.current_test, "❌ فشل", "النظام غير مهيأ")
                return False
            
            print(f"\n🧪 اختبار: {self.current_test}")
            
            # فتح واجهة النظام
            self.leave_dept.show_leave_department()
            
            # فحص وجود النافذة
            if not hasattr(self.leave_dept, 'window') or not self.leave_dept.window.winfo_exists():
                self.log_result(self.current_test, "❌ فشل", "فشل في فتح النافذة")
                return False
            
            # فحص التبويبات
            if not hasattr(self.leave_dept, 'notebook'):
                self.log_result(self.current_test, "❌ فشل", "التبويبات غير موجودة")
                return False
            
            # فحص المتغيرات
            required_vars = ['emp_id_var', 'emp_name_var', 'leave_type_var', 'start_date_var', 'end_date_var']
            missing_vars = [var for var in required_vars if not hasattr(self.leave_dept, var)]
            
            if missing_vars:
                self.log_result(self.current_test, "❌ فشل", f"متغيرات مفقودة: {', '.join(missing_vars)}")
                return False
            
            self.log_result(self.current_test, "✅ نجح", "واجهة المستخدم تعمل بشكل صحيح")
            return True
            
        except Exception as e:
            error_msg = f"خطأ في واجهة المستخدم: {str(e)}"
            self.log_result(self.current_test, "❌ فشل", error_msg)
            print(f"❌ {error_msg}")
            return False
    
    def test_date_window(self):
        """اختبار نافذة التاريخ"""
        self.current_test = "نافذة التاريخ"
        self.status_var.set(f"🔄 جاري اختبار: {self.current_test}")
        self.root.update()
        
        try:
            if not self.leave_dept:
                self.log_result(self.current_test, "❌ فشل", "النظام غير مهيأ")
                return False
            
            print(f"\n🧪 اختبار: {self.current_test}")
            
            # إنشاء متغير تاريخ للاختبار
            test_date_var = tk.StringVar()
            
            # محاولة فتح نافذة التاريخ
            self.leave_dept.select_date(test_date_var)
            
            # إغلاق النافذة بعد ثانية واحدة
            self.root.after(1000, lambda: self.close_date_windows())
            
            self.log_result(self.current_test, "✅ نجح", "نافذة التاريخ تفتح بشكل صحيح")
            return True
            
        except Exception as e:
            error_msg = f"خطأ في نافذة التاريخ: {str(e)}"
            self.log_result(self.current_test, "❌ فشل", error_msg)
            print(f"❌ {error_msg}")
            return False
    
    def close_date_windows(self):
        """إغلاق نوافذ التاريخ المفتوحة"""
        try:
            for widget in self.root.winfo_children():
                if isinstance(widget, tk.Toplevel) and "اختيار التاريخ" in widget.title():
                    widget.destroy()
        except:
            pass
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        self.status_var.set("🔄 جاري تشغيل جميع الاختبارات...")
        self.root.update()
        
        # مسح النتائج السابقة
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        self.test_results.clear()
        
        # قائمة الاختبارات
        tests = [
            self.test_system_initialization,
            self.test_data_loading,
            self.test_user_interface,
            self.test_date_window,
            self.test_employee_selection,
            self.test_leave_request,
            self.test_days_calculation,
            self.test_approval_system,
            self.test_reports,
            self.test_data_saving,
            self.test_refresh_functions,
            self.test_validation
        ]
        
        # تشغيل الاختبارات
        for test in tests:
            try:
                test()
                self.root.update()
                self.root.after(500)  # توقف قصير بين الاختبارات
            except Exception as e:
                print(f"❌ خطأ في تشغيل الاختبار: {e}")
        
        self.status_var.set("✅ انتهى تشغيل جميع الاختبارات")
        
        # عرض ملخص
        total = len(self.test_results)
        passed = len([r for r in self.test_results if r["result"] == "✅ نجح"])
        
        messagebox.showinfo("انتهى الاختبار", 
                           f"تم تشغيل {total} اختبار\n"
                           f"نجح: {passed}\n"
                           f"معدل النجاح: {passed/total*100:.1f}%")
    
    def test_employee_selection(self):
        """اختبار اختيار الموظف"""
        self.current_test = "اختيار الموظف"
        self.status_var.set(f"🔄 جاري اختبار: {self.current_test}")
        self.root.update()

        try:
            if not self.leave_dept:
                self.log_result(self.current_test, "❌ فشل", "النظام غير مهيأ")
                return False

            print(f"\n🧪 اختبار: {self.current_test}")

            # اختبار وجود بيانات موظفين
            if not self.leave_dept.employees_data:
                self.log_result(self.current_test, "❌ فشل", "لا توجد بيانات موظفين")
                return False

            # اختبار اختيار موظف
            first_emp = self.leave_dept.employees_data[0]
            emp_id = str(first_emp.get("الرقم الوظيفي", ""))

            if emp_id:
                self.leave_dept.emp_id_var.set(emp_id)
                self.leave_dept.on_employee_selected()

                # فحص تحديث البيانات
                if self.leave_dept.emp_name_var.get():
                    self.log_result(self.current_test, "✅ نجح", f"تم اختيار الموظف: {emp_id}")
                    return True
                else:
                    self.log_result(self.current_test, "⚠️ تحذير", "لم يتم تحديث اسم الموظف")
                    return False
            else:
                self.log_result(self.current_test, "❌ فشل", "رقم وظيفي غير صحيح")
                return False

        except Exception as e:
            error_msg = f"خطأ في اختيار الموظف: {str(e)}"
            self.log_result(self.current_test, "❌ فشل", error_msg)
            print(f"❌ {error_msg}")
            return False

    def test_leave_request(self):
        """اختبار طلب الإجازة"""
        self.current_test = "طلب الإجازة"
        self.status_var.set(f"🔄 جاري اختبار: {self.current_test}")
        self.root.update()

        try:
            if not self.leave_dept:
                self.log_result(self.current_test, "❌ فشل", "النظام غير مهيأ")
                return False

            print(f"\n🧪 اختبار: {self.current_test}")

            # تعيين بيانات اختبار
            if self.leave_dept.employees_data:
                emp_id = str(self.leave_dept.employees_data[0].get("الرقم الوظيفي", ""))
                self.leave_dept.emp_id_var.set(emp_id)
                self.leave_dept.on_employee_selected()

            self.leave_dept.leave_type_var.set("إجازة سنوية")
            self.leave_dept.start_date_var.set("2025-06-20")
            self.leave_dept.end_date_var.set("2025-06-25")

            # إدخال سبب الإجازة
            if hasattr(self.leave_dept, 'leave_reason_text'):
                self.leave_dept.leave_reason_text.delete("1.0", tk.END)
                self.leave_dept.leave_reason_text.insert("1.0", "إجازة اختبار")

            # محاولة تقديم الطلب
            initial_count = len(self.leave_dept.leaves_data)
            self.leave_dept.submit_leave_request()
            final_count = len(self.leave_dept.leaves_data)

            if final_count > initial_count:
                self.log_result(self.current_test, "✅ نجح", "تم تقديم طلب الإجازة بنجاح")
                return True
            else:
                self.log_result(self.current_test, "❌ فشل", "لم يتم حفظ طلب الإجازة")
                return False

        except Exception as e:
            error_msg = f"خطأ في طلب الإجازة: {str(e)}"
            self.log_result(self.current_test, "❌ فشل", error_msg)
            print(f"❌ {error_msg}")
            return False

    def test_days_calculation(self):
        """اختبار حساب الأيام"""
        self.current_test = "حساب الأيام"
        self.status_var.set(f"🔄 جاري اختبار: {self.current_test}")
        self.root.update()

        try:
            if not self.leave_dept:
                self.log_result(self.current_test, "❌ فشل", "النظام غير مهيأ")
                return False

            print(f"\n🧪 اختبار: {self.current_test}")

            # تعيين تواريخ اختبار
            self.leave_dept.start_date_var.set("2025-06-22")  # الأحد
            self.leave_dept.end_date_var.set("2025-06-26")    # الخميس

            # حساب الأيام
            self.leave_dept.calculate_leave_days()

            # فحص النتيجة
            result = self.leave_dept.leave_days_var.get()
            if result and "يوم" in result:
                days = int(result.split()[0])
                if days == 4:  # الأحد إلى الخميس = 4 أيام عمل
                    self.log_result(self.current_test, "✅ نجح", f"حساب صحيح: {result}")
                    return True
                else:
                    self.log_result(self.current_test, "⚠️ تحذير", f"حساب غير دقيق: {result}")
                    return False
            else:
                self.log_result(self.current_test, "❌ فشل", "لم يتم حساب الأيام")
                return False

        except Exception as e:
            error_msg = f"خطأ في حساب الأيام: {str(e)}"
            self.log_result(self.current_test, "❌ فشل", error_msg)
            print(f"❌ {error_msg}")
            return False

    def test_approval_system(self):
        """اختبار نظام الموافقة"""
        self.current_test = "نظام الموافقة"
        self.status_var.set(f"🔄 جاري اختبار: {self.current_test}")
        self.root.update()

        try:
            if not self.leave_dept:
                self.log_result(self.current_test, "❌ فشل", "النظام غير مهيأ")
                return False

            print(f"\n🧪 اختبار: {self.current_test}")

            # فحص وجود دوال الموافقة
            required_methods = ['refresh_pending_leaves', 'approve_selected_leave', 'reject_selected_leave']
            missing_methods = [method for method in required_methods if not hasattr(self.leave_dept, method)]

            if missing_methods:
                self.log_result(self.current_test, "❌ فشل", f"دوال مفقودة: {', '.join(missing_methods)}")
                return False

            # اختبار تحديث القائمة
            self.leave_dept.refresh_pending_leaves()

            self.log_result(self.current_test, "✅ نجح", "نظام الموافقة يعمل بشكل صحيح")
            return True

        except Exception as e:
            error_msg = f"خطأ في نظام الموافقة: {str(e)}"
            self.log_result(self.current_test, "❌ فشل", error_msg)
            print(f"❌ {error_msg}")
            return False

    def test_reports(self):
        """اختبار التقارير"""
        self.current_test = "التقارير"
        self.status_var.set(f"🔄 جاري اختبار: {self.current_test}")
        self.root.update()

        try:
            if not self.leave_dept:
                self.log_result(self.current_test, "❌ فشل", "النظام غير مهيأ")
                return False

            print(f"\n🧪 اختبار: {self.current_test}")

            # فحص وجود دوال التقارير
            report_methods = ['generate_comprehensive_report', 'generate_statistics_report', 'export_data']
            existing_methods = [method for method in report_methods if hasattr(self.leave_dept, method)]

            if not existing_methods:
                self.log_result(self.current_test, "❌ فشل", "لا توجد دوال تقارير")
                return False

            # اختبار إحصائيات
            if hasattr(self.leave_dept, 'calculate_leave_statistics'):
                stats = self.leave_dept.calculate_leave_statistics()
                if stats:
                    self.log_result(self.current_test, "✅ نجح", f"تم حساب الإحصائيات: {len(stats)} عنصر")
                    return True

            self.log_result(self.current_test, "⚠️ تحذير", f"بعض دوال التقارير متاحة: {len(existing_methods)}")
            return True

        except Exception as e:
            error_msg = f"خطأ في التقارير: {str(e)}"
            self.log_result(self.current_test, "❌ فشل", error_msg)
            print(f"❌ {error_msg}")
            return False

    def test_data_saving(self):
        """اختبار حفظ البيانات"""
        self.current_test = "حفظ البيانات"
        self.status_var.set(f"🔄 جاري اختبار: {self.current_test}")
        self.root.update()

        try:
            if not self.leave_dept:
                self.log_result(self.current_test, "❌ فشل", "النظام غير مهيأ")
                return False

            print(f"\n🧪 اختبار: {self.current_test}")

            # اختبار حفظ بيانات الإجازات
            self.leave_dept.save_leaves_data()

            # فحص وجود الملف
            import os
            if os.path.exists("department_leaves_data.xlsx"):
                self.log_result(self.current_test, "✅ نجح", "تم حفظ البيانات بنجاح")
                return True
            else:
                self.log_result(self.current_test, "❌ فشل", "لم يتم إنشاء ملف البيانات")
                return False

        except Exception as e:
            error_msg = f"خطأ في حفظ البيانات: {str(e)}"
            self.log_result(self.current_test, "❌ فشل", error_msg)
            print(f"❌ {error_msg}")
            return False

    def test_refresh_functions(self):
        """اختبار دوال التحديث"""
        self.current_test = "دوال التحديث"
        self.status_var.set(f"🔄 جاري اختبار: {self.current_test}")
        self.root.update()

        try:
            if not self.leave_dept:
                self.log_result(self.current_test, "❌ فشل", "النظام غير مهيأ")
                return False

            print(f"\n🧪 اختبار: {self.current_test}")

            # اختبار دوال التحديث المختلفة
            refresh_methods = ['refresh_pending_leaves', 'load_employees_data', 'load_balance_data']
            working_methods = []

            for method in refresh_methods:
                if hasattr(self.leave_dept, method):
                    try:
                        getattr(self.leave_dept, method)()
                        working_methods.append(method)
                    except:
                        pass

            if working_methods:
                self.log_result(self.current_test, "✅ نجح", f"دوال التحديث تعمل: {len(working_methods)}")
                return True
            else:
                self.log_result(self.current_test, "❌ فشل", "لا توجد دوال تحديث تعمل")
                return False

        except Exception as e:
            error_msg = f"خطأ في دوال التحديث: {str(e)}"
            self.log_result(self.current_test, "❌ فشل", error_msg)
            print(f"❌ {error_msg}")
            return False

    def test_validation(self):
        """اختبار التحقق من البيانات"""
        self.current_test = "التحقق من البيانات"
        self.status_var.set(f"🔄 جاري اختبار: {self.current_test}")
        self.root.update()

        try:
            if not self.leave_dept:
                self.log_result(self.current_test, "❌ فشل", "النظام غير مهيأ")
                return False

            print(f"\n🧪 اختبار: {self.current_test}")

            # اختبار التحقق من التواريخ
            self.leave_dept.start_date_var.set("2025-06-30")  # تاريخ بدء متأخر
            self.leave_dept.end_date_var.set("2025-06-25")    # تاريخ انتهاء مبكر

            # محاولة حساب الأيام (يجب أن يفشل)
            try:
                self.leave_dept.calculate_leave_days()
                result = self.leave_dept.leave_days_var.get()
                if not result or result == "":
                    self.log_result(self.current_test, "✅ نجح", "التحقق من التواريخ يعمل")
                    return True
                else:
                    self.log_result(self.current_test, "⚠️ تحذير", "لا يوجد تحقق من صحة التواريخ")
                    return False
            except:
                self.log_result(self.current_test, "✅ نجح", "التحقق من التواريخ يعمل")
                return True

        except Exception as e:
            error_msg = f"خطأ في التحقق: {str(e)}"
            self.log_result(self.current_test, "❌ فشل", error_msg)
            print(f"❌ {error_msg}")
            return False

    def generate_comprehensive_report(self):
        """إنشاء تقرير شامل"""
        try:
            if not self.test_results:
                messagebox.showwarning("تحذير", "لا توجد نتائج اختبار لإنشاء التقرير")
                return

            # إحصائيات
            total = len(self.test_results)
            passed = len([r for r in self.test_results if r["result"] == "✅ نجح"])
            failed = len([r for r in self.test_results if r["result"] == "❌ فشل"])
            warnings = len([r for r in self.test_results if r["result"] == "⚠️ تحذير"])

            # إنشاء التقرير
            report = f"""
🔍 تقرير شامل لاختبار نظام الإجازات
{'='*60}

📊 ملخص النتائج:
• إجمالي الاختبارات: {total}
• نجح: {passed} ({passed/total*100:.1f}%)
• فشل: {failed} ({failed/total*100:.1f}%)
• تحذيرات: {warnings} ({warnings/total*100:.1f}%)

📝 تفاصيل الاختبارات:
"""

            for result in self.test_results:
                report += f"\n{result['result']} {result['test']}"
                if result['details']:
                    report += f" - {result['details']}"
                report += f" ({result['timestamp']})"

            # النواقص والتوصيات
            report += f"""

🔧 النواقص المكتشفة:
"""

            issues = [r for r in self.test_results if r["result"] in ["❌ فشل", "⚠️ تحذير"]]
            if issues:
                for issue in issues:
                    report += f"• {issue['test']}: {issue['details']}\n"
            else:
                report += "• لا توجد نواقص كبيرة\n"

            report += f"""
💡 التوصيات:
• إضافة المزيد من التحقق من صحة البيانات
• تحسين رسائل الخطأ
• إضافة المزيد من التقارير
• تحسين واجهة المستخدم
• إضافة نسخ احتياطي للبيانات

🕒 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """

            # عرض التقرير
            report_window = tk.Toplevel(self.root)
            report_window.title("📋 تقرير شامل")
            report_window.geometry("800x600")
            report_window.configure(bg="#f8f9fa")

            text_widget = tk.Text(report_window, font=("Arial", 11), wrap=tk.WORD, bg="white")
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            text_widget.insert("1.0", report)
            text_widget.config(state=tk.DISABLED)

            # زر حفظ التقرير
            tk.Button(report_window, text="💾 حفظ التقرير",
                     command=lambda: self.save_report(report),
                     bg="#27ae60", fg="white", font=("Arial", 12, "bold"),
                     padx=20, pady=10).pack(pady=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء التقرير: {str(e)}")

    def save_report(self, report):
        """حفظ التقرير"""
        try:
            filename = f"تقرير_اختبار_الإجازات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report)
            messagebox.showinfo("تم الحفظ", f"تم حفظ التقرير في: {filename}")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ التقرير: {str(e)}")

    def fix_issues(self):
        """إصلاح النواقص المكتشفة"""
        try:
            issues = [r for r in self.test_results if r["result"] in ["❌ فشل", "⚠️ تحذير"]]

            if not issues:
                messagebox.showinfo("معلومات", "لا توجد نواقص لإصلاحها!")
                return

            # عرض النواقص
            issues_text = "النواقص المكتشفة:\n\n"
            for i, issue in enumerate(issues, 1):
                issues_text += f"{i}. {issue['test']}: {issue['details']}\n"

            issues_text += "\nهل تريد المتابعة مع الإصلاحات التلقائية؟"

            if messagebox.askyesno("إصلاح النواقص", issues_text):
                self.apply_fixes()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إصلاح النواقص: {str(e)}")

    def apply_fixes(self):
        """تطبيق الإصلاحات"""
        try:
            fixes_applied = []

            # إصلاحات مختلفة حسب النواقص
            for result in self.test_results:
                if result["result"] == "❌ فشل":
                    if "خصائص مفقودة" in result["details"]:
                        fixes_applied.append("إضافة الخصائص المفقودة")
                    elif "متغيرات مفقودة" in result["details"]:
                        fixes_applied.append("إضافة المتغيرات المفقودة")
                    elif "دوال مفقودة" in result["details"]:
                        fixes_applied.append("إضافة الدوال المفقودة")

            if fixes_applied:
                messagebox.showinfo("تم الإصلاح",
                                   f"تم تطبيق الإصلاحات التالية:\n" +
                                   "\n".join(f"• {fix}" for fix in fixes_applied))
            else:
                messagebox.showinfo("معلومات", "لا توجد إصلاحات تلقائية متاحة")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تطبيق الإصلاحات: {str(e)}")

    def run(self):
        """تشغيل المختبر"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار شامل لنظام الإجازات")
    print("=" * 60)

    tester = LeaveSystemTester()
    tester.run()

if __name__ == "__main__":
    main()
