#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة رصيد الإجازات السنوي للموظفين
يحسب رصيد الإجازة بناءً على تاريخ أول مباشرة (شهر لكل سنة عمل)
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import os
import sys

# استيراد أنظمة التحسين
try:
    from data_validation import DataValidator, CalculationEngine
    from error_handler import error_handler, backup_manager
    from performance_optimizer import performance_monitor, memory_optimizer
    ENHANCED_FEATURES = True
    print("✅ تم تحميل أنظمة التحسين لنظام رصيد الإجازات")
except ImportError as e:
    print(f"⚠️ لم يتم تحميل أنظمة التحسين: {e}")
    ENHANCED_FEATURES = False

def get_resource_path(relative_path):
    """الحصول على المسار الصحيح للملفات"""
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

def is_running_as_executable():
    """فحص ما إذا كان التطبيق يعمل كملف تنفيذي"""
    return getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')

try:
    from openpyxl import load_workbook, Workbook
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    messagebox.showerror("خطأ", "مكتبة openpyxl غير مثبتة\nيرجى تثبيتها باستخدام: pip install openpyxl")

class LeaveBalanceSystem:
    """نظام إدارة رصيد الإجازات السنوي المحسن"""

    def __init__(self, root, auto_refresh=True):
        self.root = root
        self.root.title("نظام إدارة رصيد الإجازات السنوي")

        # تهيئة أنظمة التحسين
        if ENHANCED_FEATURES:
            self.validator = DataValidator()
            self.calculator = CalculationEngine()
            self.performance_enabled = True
            print("🔧 تم تفعيل أنظمة التحسين لرصيد الإجازات")
        else:
            self.validator = None
            self.calculator = None
            self.performance_enabled = False
        self.root.geometry("1400x800")
        self.root.configure(bg="#f0f0f0")
        
        # ملفات البيانات
        self.employees_data_file = get_resource_path("employees_data.xlsx") if is_running_as_executable() else "employees_data.xlsx"
        self.leaves_data_file = get_resource_path("leaves_data.xlsx") if is_running_as_executable() else "leaves_data.xlsx"
        self.balance_data_file = get_resource_path("leave_balance_data.xlsx") if is_running_as_executable() else "leave_balance_data.xlsx"
        
        # أسماء الأوراق
        self.employees_sheet_name = "الموظفين"
        self.leaves_sheet_name = "الإجازات"
        self.balance_sheet_name = "رصيد_الإجازات"
        
        # إنشاء ملف رصيد الإجازات إذا لم يكن موجوداً
        self.initialize_balance_file()
        
        # تحميل البيانات
        self.employees_data = self.load_employees_data()
        self.leaves_data = self.load_leaves_data()
        self.balance_data = self.load_balance_data()
        
        # إنشاء واجهة المستخدم
        self.create_ui()
        
        # تحديث البيانات عند التشغيل (حسب المعامل)
        if auto_refresh:
            self.refresh_all_balances(show_message=False)
    
    def initialize_balance_file(self):
        """إنشاء ملف Excel لرصيد الإجازات إذا لم يكن موجوداً"""
        if not OPENPYXL_AVAILABLE:
            return
            
        if not os.path.exists(self.balance_data_file):
            wb = Workbook()
            ws = wb.active
            ws.title = self.balance_sheet_name
            
            # عناوين الأعمدة
            headers = [
                "الرقم الوظيفي", "اسم الموظف", "الرقم المالي", "الرقم الوطني",
                "مكان العمل", "المؤهل والتخصص", "اسم المصرف", "رقم الحساب",
                "تاريخ أول مباشرة", "سنوات الخدمة", "الرصيد المحسوب تلقائياً",
                "الرصيد اليدوي المضاف", "إجمالي الرصيد", "الإجازات المأخوذة",
                "الرصيد المتبقي", "تاريخ آخر تحديث", "ملاحظات"
            ]
            
            ws.append(headers)
            wb.save(self.balance_data_file)
            print(f"✅ تم إنشاء ملف رصيد الإجازات: {self.balance_data_file}")
    
    def load_employees_data(self):
        """تحميل بيانات الموظفين من ملف Excel"""
        if not OPENPYXL_AVAILABLE:
            return []
            
        try:
            if not os.path.exists(self.employees_data_file):
                return []
                
            wb = load_workbook(self.employees_data_file)
            
            # البحث عن الورقة المناسبة
            if self.employees_sheet_name in wb.sheetnames:
                ws = wb[self.employees_sheet_name]
            elif "Employees" in wb.sheetnames:
                ws = wb["Employees"]
            else:
                ws = wb.active
            
            data = []
            headers = [cell.value for cell in ws[1]]
            
            for row in ws.iter_rows(min_row=2, values_only=True):
                if any(row):
                    emp_data = dict(zip(headers, row))
                    # التأكد من وجود الحقول المطلوبة
                    if emp_data.get("الرقم الوظيفي") and emp_data.get("الاسم العربي"):
                        data.append(emp_data)
            
            return data
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل بيانات الموظفين: {str(e)}")
            return []
    
    def load_leaves_data(self):
        """تحميل بيانات الإجازات من ملف Excel"""
        if not OPENPYXL_AVAILABLE:
            return []
            
        try:
            if not os.path.exists(self.leaves_data_file):
                return []
                
            wb = load_workbook(self.leaves_data_file)
            
            # البحث عن الورقة المناسبة
            if self.leaves_sheet_name in wb.sheetnames:
                ws = wb[self.leaves_sheet_name]
            elif "Leaves" in wb.sheetnames:
                ws = wb["Leaves"]
            else:
                ws = wb.active
            
            data = []
            headers = [cell.value for cell in ws[1]]
            
            for row in ws.iter_rows(min_row=2, values_only=True):
                if any(row):
                    leave_data = dict(zip(headers, row))
                    # فقط الإجازات الموافق عليها
                    if leave_data.get("الحالة") in ["موافق", "مفعل", "نشط"]:
                        data.append(leave_data)
            
            return data
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل بيانات الإجازات: {str(e)}")
            return []
    
    def load_balance_data(self):
        """تحميل بيانات رصيد الإجازات من ملف Excel"""
        if not OPENPYXL_AVAILABLE:
            return []
            
        try:
            if not os.path.exists(self.balance_data_file):
                return []
                
            wb = load_workbook(self.balance_data_file)
            ws = wb[self.balance_sheet_name]
            
            data = []
            headers = [cell.value for cell in ws[1]]
            
            for row in ws.iter_rows(min_row=2, values_only=True):
                if any(row):
                    data.append(dict(zip(headers, row)))
            
            return data
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل بيانات رصيد الإجازات: {str(e)}")
            return []
    
    def calculate_service_years(self, start_date_str):
        """حساب سنوات الخدمة من تاريخ أول مباشرة"""
        if not start_date_str:
            return 0
            
        try:
            # محاولة تحويل التاريخ بصيغ مختلفة
            start_date = None
            for date_format in ["%Y-%m-%d", "%d/%m/%Y", "%d-%m-%Y"]:
                try:
                    start_date = datetime.strptime(str(start_date_str), date_format)
                    break
                except:
                    continue
            
            if not start_date:
                return 0
            
            # حساب الفرق بالسنوات
            today = datetime.now()
            years = today.year - start_date.year
            
            # تعديل إذا لم يحن موعد الذكرى السنوية بعد
            if today.month < start_date.month or (today.month == start_date.month and today.day < start_date.day):
                years -= 1
            
            return max(0, years)  # لا يمكن أن تكون سالبة
            
        except Exception as e:
            print(f"خطأ في حساب سنوات الخدمة: {e}")
            return 0
    
    def calculate_automatic_balance(self, service_years, job_title=""):
        """حساب الرصيد التلقائي بناءً على المسمى الوظيفي مع التحسينات المتقدمة"""
        # استخدام محرك الحسابات المحسن إذا كان متاحاً
        if ENHANCED_FEATURES and hasattr(self, 'calculator') and self.calculator:
            balance = self.calculator.calculate_leave_balance(service_years, job_title)
            print(f"🔧 استخدام محرك الحسابات المحسن: {balance} يوم")
            return balance

        # الطريقة التقليدية
        job_title_lower = str(job_title).lower()

        # قائمة المسميات التي تستحق رصيد إجازات (موظفين فقط)
        employee_titles = [
            "موظف", "موظفة", "مدير", "مديرة", "رئيس", "رئيسة",
            "مساعد", "مساعدة", "سكرتير", "سكرتيرة", "محاسب", "محاسبة",
            "مراقب", "مراقبة", "مشرف", "مشرفة", "منسق", "منسقة",
            "أخصائي", "أخصائية", "فني", "فنية", "كاتب", "كاتبة"
        ]

        # قائمة المسميات التي لا تستحق رصيد إجازات (معلمين)
        teacher_titles = [
            "معلم", "معلمة", "مدرس", "مدرسة", "أستاذ", "أستاذة",
            "مدرب", "مدربة", "محاضر", "محاضرة"
        ]

        # فحص المسمى الوظيفي - أولوية للمعلمين
        is_teacher = any(title in job_title_lower for title in teacher_titles)
        is_employee = any(title in job_title_lower for title in employee_titles)

        print(f"🔍 فحص المسمى الوظيفي: '{job_title}'")
        print(f"   موظف: {is_employee}, معلم: {is_teacher}")

        # إذا كان معلم، لا يستحق رصيد (حتى لو كان يحتوي على كلمات موظف)
        if is_teacher:
            print(f"   ❌ معلم - لا يستحق رصيد إجازات")
            return 0
        elif is_employee:
            balance = service_years * 30  # شهر لكل سنة
            print(f"   ✅ موظف - يستحق {balance} يوم ({service_years} سنة × 30 يوم)")
            return balance
        else:
            # في حالة عدم وضوح المسمى، اعتبره موظف (الافتراضي)
            balance = service_years * 30
            print(f"   ⚠️ مسمى غير واضح - اعتبار كموظف: {balance} يوم")
            return balance
    
    def calculate_used_leaves(self, employee_id):
        """حساب الإجازات المأخوذة للموظف"""
        used_days = 0
        
        for leave in self.leaves_data:
            if str(leave.get("الرقم الوظيفي", "")) == str(employee_id):
                days = leave.get("عدد الأيام", 0)
                try:
                    used_days += int(days) if days else 0
                except:
                    pass
        
        return used_days

    def get_manual_balance(self, employee_id):
        """الحصول على الرصيد اليدوي المضاف للموظف"""
        for balance in self.balance_data:
            if str(balance.get("الرقم الوظيفي", "")) == str(employee_id):
                manual = balance.get("الرصيد اليدوي المضاف", 0)
                try:
                    return int(manual) if manual else 0
                except:
                    return 0
        return 0

    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg="#f0f0f0", padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان النظام
        title_frame = tk.Frame(main_frame, bg="#2c3e50", relief=tk.RAISED, bd=3)
        title_frame.pack(fill=tk.X, pady=(0, 20))

        title_label = tk.Label(title_frame, text="🏖️ نظام إدارة رصيد الإجازات السنوي",
                              font=("Arial", 20, "bold"), fg="white", bg="#2c3e50", pady=15)
        title_label.pack()

        # إطار الأزرار العلوية
        buttons_frame = tk.Frame(main_frame, bg="#f0f0f0")
        buttons_frame.pack(fill=tk.X, pady=(0, 15))

        # زر تحديث البيانات
        refresh_btn = tk.Button(buttons_frame, text="🔄 تحديث جميع الأرصدة",
                               command=self.refresh_all_balances,
                               font=("Arial", 12, "bold"), bg="#27ae60", fg="white",
                               relief=tk.RAISED, bd=2, padx=15, pady=8)
        refresh_btn.pack(side=tk.LEFT, padx=5)

        # زر إضافة رصيد يدوي
        manual_btn = tk.Button(buttons_frame, text="➕ إضافة رصيد يدوي",
                              command=self.open_manual_balance_window,
                              font=("Arial", 12, "bold"), bg="#3498db", fg="white",
                              relief=tk.RAISED, bd=2, padx=15, pady=8)
        manual_btn.pack(side=tk.LEFT, padx=5)

        # زر تصدير البيانات
        export_btn = tk.Button(buttons_frame, text="📊 تصدير البيانات",
                              command=self.export_balance_data,
                              font=("Arial", 12, "bold"), bg="#e67e22", fg="white",
                              relief=tk.RAISED, bd=2, padx=15, pady=8)
        export_btn.pack(side=tk.LEFT, padx=5)

        # زر الإغلاق
        close_btn = tk.Button(buttons_frame, text="❌ إغلاق",
                             command=self.close_window,
                             font=("Arial", 12, "bold"), bg="#e74c3c", fg="white",
                             relief=tk.RAISED, bd=2, padx=15, pady=8)
        close_btn.pack(side=tk.RIGHT, padx=5)

        # إطار البحث
        search_frame = tk.LabelFrame(main_frame, text="🔍 البحث والتصفية",
                                   font=("Arial", 12, "bold"), fg="#2c3e50",
                                   padx=10, pady=10, bg="#f8f9fa")
        search_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(search_frame, text="بحث حسب:", font=("Arial", 11), bg="#f8f9fa").grid(row=0, column=0, padx=5, sticky="e")

        self.search_by = ttk.Combobox(search_frame, values=[
            "اسم الموظف", "الرقم الوظيفي", "مكان العمل"
        ], state="readonly", width=15)
        self.search_by.grid(row=0, column=1, padx=5, sticky="w")
        self.search_by.current(0)

        self.search_entry = tk.Entry(search_frame, width=30, font=("Arial", 11))
        self.search_entry.grid(row=0, column=2, padx=5, sticky="w")

        search_btn = tk.Button(search_frame, text="بحث", command=self.search_employees,
                              font=("Arial", 10, "bold"), bg="#3498db", fg="white",
                              relief=tk.RAISED, bd=1, padx=10, pady=3)
        search_btn.grid(row=0, column=3, padx=5)

        reset_btn = tk.Button(search_frame, text="إعادة تعيين", command=self.reset_search,
                             font=("Arial", 10, "bold"), bg="#95a5a6", fg="white",
                             relief=tk.RAISED, bd=1, padx=10, pady=3)
        reset_btn.grid(row=0, column=4, padx=5)

        # إطار الجدول
        table_frame = tk.Frame(main_frame, bg="#f0f0f0")
        table_frame.pack(fill=tk.BOTH, expand=True)

        # شريط التمرير
        scroll_y = tk.Scrollbar(table_frame, orient=tk.VERTICAL)
        scroll_x = tk.Scrollbar(table_frame, orient=tk.HORIZONTAL)

        # إنشاء الجدول
        self.balance_table = ttk.Treeview(
            table_frame,
            yscrollcommand=scroll_y.set,
            xscrollcommand=scroll_x.set,
            selectmode="browse"
        )

        scroll_y.config(command=self.balance_table.yview)
        scroll_x.config(command=self.balance_table.xview)

        # تعريف الأعمدة
        self.balance_table["columns"] = (
            "الرقم الوظيفي", "اسم الموظف", "مكان العمل", "سنوات الخدمة",
            "الرصيد التلقائي", "الرصيد اليدوي", "إجمالي الرصيد",
            "الإجازات المأخوذة", "الرصيد المتبقي"
        )

        # تنسيق الأعمدة
        self.balance_table.column("#0", width=0, stretch=tk.NO)
        self.balance_table.column("الرقم الوظيفي", width=100, anchor="center")
        self.balance_table.column("اسم الموظف", width=150, anchor="center")
        self.balance_table.column("مكان العمل", width=120, anchor="center")
        self.balance_table.column("سنوات الخدمة", width=100, anchor="center")
        self.balance_table.column("الرصيد التلقائي", width=100, anchor="center")
        self.balance_table.column("الرصيد اليدوي", width=100, anchor="center")
        self.balance_table.column("إجمالي الرصيد", width=100, anchor="center")
        self.balance_table.column("الإجازات المأخوذة", width=120, anchor="center")
        self.balance_table.column("الرصيد المتبقي", width=100, anchor="center")

        # عناوين الأعمدة
        self.balance_table.heading("#0", text="", anchor="center")
        self.balance_table.heading("الرقم الوظيفي", text="الرقم الوظيفي", anchor="center")
        self.balance_table.heading("اسم الموظف", text="اسم الموظف", anchor="center")
        self.balance_table.heading("مكان العمل", text="مكان العمل", anchor="center")
        self.balance_table.heading("سنوات الخدمة", text="سنوات الخدمة", anchor="center")
        self.balance_table.heading("الرصيد التلقائي", text="الرصيد التلقائي", anchor="center")
        self.balance_table.heading("الرصيد اليدوي", text="الرصيد اليدوي", anchor="center")
        self.balance_table.heading("إجمالي الرصيد", text="إجمالي الرصيد", anchor="center")
        self.balance_table.heading("الإجازات المأخوذة", text="الإجازات المأخوذة", anchor="center")
        self.balance_table.heading("الرصيد المتبقي", text="الرصيد المتبقي", anchor="center")

        # وضع الجدول وشريط التمرير
        self.balance_table.grid(row=0, column=0, sticky="nsew")
        scroll_y.grid(row=0, column=1, sticky="ns")
        scroll_x.grid(row=1, column=0, sticky="ew")

        # جعل الجدول قابل للتوسع
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # ربط النقر المزدوج لعرض تفاصيل الموظف
        self.balance_table.bind("<Double-1>", self.show_employee_details)

    def refresh_all_balances(self, show_message=True):
        """تحديث جميع أرصدة الإجازات"""
        try:
            # إعادة تحميل البيانات
            self.employees_data = self.load_employees_data()
            self.leaves_data = self.load_leaves_data()
            self.balance_data = self.load_balance_data()

            # تحديث الجدول
            self.update_balance_table()

            # حفظ البيانات المحدثة
            self.save_balance_data()

            # إظهار الرسالة فقط عند الطلب (عند الضغط على الزر)
            if show_message:
                messagebox.showinfo("نجاح", "تم تحديث جميع الأرصدة بنجاح")

            print("✅ تم تحديث جميع الأرصدة بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث الأرصدة: {str(e)}")

    def update_balance_table(self, data=None):
        """تحديث جدول الأرصدة"""
        # مسح البيانات الحالية
        for item in self.balance_table.get_children():
            self.balance_table.delete(item)

        # استخدام البيانات المقدمة أو البيانات الكاملة
        display_data = data if data else self.employees_data

        # إضافة البيانات إلى الجدول
        for emp in display_data:
            emp_id = emp.get("الرقم الوظيفي", "")
            emp_name = emp.get("الاسم العربي", "")
            work_place = emp.get("مكان العمل الحالي", "")
            start_date = emp.get("تاريخ أول مباشرة", "")

            # حساب البيانات
            service_years = self.calculate_service_years(start_date)
            job_title = emp.get("المسمى الوظيفي", "") or emp.get("الوظيفة", "") or emp.get("المنصب", "")
            auto_balance = self.calculate_automatic_balance(service_years, job_title)
            manual_balance = self.get_manual_balance(emp_id)
            total_balance = auto_balance + manual_balance
            used_leaves = self.calculate_used_leaves(emp_id)
            remaining_balance = total_balance - used_leaves

            # إضافة الصف إلى الجدول
            self.balance_table.insert("", tk.END, values=(
                emp_id, emp_name, work_place, service_years,
                auto_balance, manual_balance, total_balance,
                used_leaves, remaining_balance
            ))

    def search_employees(self):
        """البحث في بيانات الموظفين"""
        search_criteria = self.search_by.get()
        search_term = self.search_entry.get().strip().lower()

        if not search_term:
            messagebox.showwarning("تحذير", "الرجاء إدخال مصطلح البحث")
            return

        filtered_data = []
        for emp in self.employees_data:
            if search_criteria == "اسم الموظف":
                if search_term in emp.get("الاسم العربي", "").lower():
                    filtered_data.append(emp)
            elif search_criteria == "الرقم الوظيفي":
                if search_term in str(emp.get("الرقم الوظيفي", "")).lower():
                    filtered_data.append(emp)
            elif search_criteria == "مكان العمل":
                if search_term in emp.get("مكان العمل الحالي", "").lower():
                    filtered_data.append(emp)

        if not filtered_data:
            messagebox.showinfo("نتائج البحث", "لا توجد نتائج مطابقة للبحث")
        else:
            self.update_balance_table(filtered_data)

    def reset_search(self):
        """إعادة تعيين البحث وعرض جميع الموظفين"""
        self.search_entry.delete(0, tk.END)
        self.update_balance_table()

    def open_manual_balance_window(self):
        """فتح نافذة إضافة الرصيد اليدوي"""
        selected_item = self.balance_table.focus()
        if not selected_item:
            messagebox.showwarning("تحذير", "الرجاء تحديد موظف لتعديل رصيده")
            return

        # الحصول على بيانات الموظف المحدد
        selected_values = self.balance_table.item(selected_item)["values"]
        emp_id = selected_values[0]
        emp_name = selected_values[1]
        current_manual = selected_values[5]

        # إنشاء نافذة جديدة بحجم أكبر
        manual_window = tk.Toplevel(self.root)
        manual_window.title(f"إدارة الرصيد اليدوي - {emp_name}")
        manual_window.geometry("700x600")
        manual_window.configure(bg="#f0f0f0")
        manual_window.resizable(True, True)
        manual_window.minsize(600, 500)

        # جعل النافذة في المقدمة
        manual_window.transient(self.root)
        manual_window.grab_set()

        # إطار رئيسي بحشو أكبر
        main_frame = tk.Frame(manual_window, bg="#f0f0f0", padx=30, pady=30)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان النافذة بخط أكبر
        title_label = tk.Label(main_frame, text="➕ إدارة الرصيد اليدوي",
                              font=("Arial", 20, "bold"), fg="#2c3e50", bg="#f0f0f0")
        title_label.pack(pady=(0, 25))

        # معلومات الموظف بحجم أكبر
        info_frame = tk.LabelFrame(main_frame, text="معلومات الموظف",
                                  font=("Arial", 14, "bold"), fg="#2c3e50",
                                  padx=20, pady=20, bg="#ffffff")
        info_frame.pack(fill=tk.X, pady=(0, 25))

        tk.Label(info_frame, text=f"الرقم الوظيفي: {emp_id}",
                font=("Arial", 13), bg="#ffffff").pack(anchor="w", pady=5)
        tk.Label(info_frame, text=f"اسم الموظف: {emp_name}",
                font=("Arial", 13), bg="#ffffff").pack(anchor="w", pady=5)
        tk.Label(info_frame, text=f"الرصيد اليدوي الحالي: {current_manual} يوم",
                font=("Arial", 13, "bold"), fg="#e74c3c", bg="#ffffff").pack(anchor="w", pady=5)

        # إطار إدخال الرصيد الجديد بحجم أكبر
        balance_frame = tk.LabelFrame(main_frame, text="الرصيد الجديد",
                                     font=("Arial", 14, "bold"), fg="#2c3e50",
                                     padx=20, pady=20, bg="#ffffff")
        balance_frame.pack(fill=tk.X, pady=(0, 25))

        tk.Label(balance_frame, text="الرصيد اليدوي الجديد (بالأيام):",
                font=("Arial", 13), bg="#ffffff").pack(anchor="w", pady=(0, 8))

        balance_entry = tk.Entry(balance_frame, font=("Arial", 14), width=25,
                                relief=tk.GROOVE, bd=2, justify='center')
        balance_entry.pack(pady=(0, 15), ipady=5)
        balance_entry.insert(0, str(current_manual))
        balance_entry.focus_set()

        tk.Label(balance_frame, text="ملاحظات (اختيارية):",
                font=("Arial", 13), bg="#ffffff").pack(anchor="w", pady=(15, 8))

        notes_text = tk.Text(balance_frame, height=6, width=50, font=("Arial", 12),
                            relief=tk.GROOVE, bd=2)
        notes_text.pack(pady=(0, 15))

        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg="#f0f0f0")
        buttons_frame.pack(fill=tk.X, pady=10)

        def save_manual_balance():
            """حفظ الرصيد اليدوي الجديد"""
            try:
                new_balance = balance_entry.get().strip()
                notes = notes_text.get("1.0", tk.END).strip()

                # التحقق من صحة الرقم
                try:
                    balance_value = int(new_balance) if new_balance else 0
                except ValueError:
                    messagebox.showerror("خطأ", "الرجاء إدخال رقم صحيح للرصيد")
                    return

                # تحديث البيانات
                self.update_manual_balance(emp_id, balance_value, notes)

                # حفظ البيانات فوراً
                save_success = self.save_balance_data()

                if save_success:
                    # تحديث الجدول
                    self.update_balance_table()

                    # إغلاق النافذة
                    manual_window.destroy()

                    messagebox.showinfo("نجاح", f"تم تحديث وحفظ الرصيد اليدوي للموظف {emp_name}\nالرصيد الجديد: {balance_value} يوم")
                else:
                    messagebox.showerror("خطأ", "تم تحديث البيانات ولكن فشل في حفظ الملف")

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ الرصيد: {str(e)}")

        # أزرار الحفظ والإلغاء بحجم أكبر
        save_btn = tk.Button(buttons_frame, text="💾 حفظ",
                            command=save_manual_balance,
                            font=("Arial", 14, "bold"), bg="#27ae60", fg="white",
                            relief=tk.RAISED, bd=3, padx=30, pady=12, width=12)
        save_btn.pack(side=tk.LEFT, padx=10)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء",
                              command=manual_window.destroy,
                              font=("Arial", 14, "bold"), bg="#e74c3c", fg="white",
                              relief=tk.RAISED, bd=3, padx=30, pady=12, width=12)
        cancel_btn.pack(side=tk.RIGHT, padx=10)

    def update_manual_balance(self, emp_id, balance_value, notes=""):
        """تحديث الرصيد اليدوي للموظف"""
        print(f"🔄 تحديث الرصيد اليدوي للموظف {emp_id}: {balance_value} يوم")

        # البحث عن الموظف في بيانات الرصيد
        found = False
        for balance in self.balance_data:
            if str(balance.get("الرقم الوظيفي", "")) == str(emp_id):
                old_balance = balance.get("الرصيد اليدوي المضاف", 0)
                balance["الرصيد اليدوي المضاف"] = balance_value
                balance["تاريخ آخر تحديث"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                if notes:
                    balance["ملاحظات"] = notes
                found = True
                print(f"✅ تم تحديث الرصيد من {old_balance} إلى {balance_value}")
                break

        # إذا لم يوجد، أضف سجل جديد
        if not found:
            print(f"📝 إنشاء سجل رصيد جديد للموظف {emp_id}")

            # البحث عن بيانات الموظف
            emp_data = None
            for emp in self.employees_data:
                if str(emp.get("الرقم الوظيفي", "")) == str(emp_id):
                    emp_data = emp
                    break

            if emp_data:
                service_years = self.calculate_service_years(emp_data.get("تاريخ أول مباشرة", ""))
                job_title = emp_data.get("المسمى الوظيفي", "") or emp_data.get("الوظيفة", "") or emp_data.get("المنصب", "")
                auto_balance = self.calculate_automatic_balance(service_years, job_title)
                used_leaves = self.calculate_used_leaves(emp_id)
                total_balance = auto_balance + balance_value
                remaining_balance = total_balance - used_leaves

                new_balance_record = {
                    "الرقم الوظيفي": emp_id,
                    "اسم الموظف": emp_data.get("الاسم العربي", ""),
                    "الرقم المالي": emp_data.get("الرقم المالي", ""),
                    "الرقم الوطني": emp_data.get("الرقم الوطني", ""),
                    "مكان العمل": emp_data.get("مكان العمل الحالي", ""),
                    "المؤهل والتخصص": f"{emp_data.get('المؤهل', '')} - {emp_data.get('التخصص', '')}",
                    "اسم المصرف": emp_data.get("اسم المصرف", ""),
                    "رقم الحساب": emp_data.get("رقم الحساب", ""),
                    "تاريخ أول مباشرة": emp_data.get("تاريخ أول مباشرة", ""),
                    "سنوات الخدمة": service_years,
                    "الرصيد المحسوب تلقائياً": auto_balance,
                    "الرصيد اليدوي المضاف": balance_value,
                    "إجمالي الرصيد": total_balance,
                    "الإجازات المأخوذة": used_leaves,
                    "الرصيد المتبقي": remaining_balance,
                    "تاريخ آخر تحديث": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "ملاحظات": notes
                }

                self.balance_data.append(new_balance_record)
                print(f"✅ تم إنشاء سجل جديد بالرصيد {balance_value}")
            else:
                print(f"❌ لم يتم العثور على بيانات الموظف {emp_id}")

    def save_balance_data(self):
        """حفظ بيانات الرصيد في ملف Excel"""
        if not OPENPYXL_AVAILABLE:
            print("❌ مكتبة openpyxl غير متاحة")
            return False

        try:
            print(f"💾 بدء حفظ بيانات الرصيد في {self.balance_data_file}")

            # إنشاء ملف جديد
            wb = Workbook()
            ws = wb.active
            ws.title = self.balance_sheet_name

            # العناوين
            headers = [
                "الرقم الوظيفي", "اسم الموظف", "الرقم المالي", "الرقم الوطني",
                "مكان العمل", "المؤهل والتخصص", "اسم المصرف", "رقم الحساب",
                "تاريخ أول مباشرة", "سنوات الخدمة", "الرصيد المحسوب تلقائياً",
                "الرصيد اليدوي المضاف", "إجمالي الرصيد", "الإجازات المأخوذة",
                "الرصيد المتبقي", "تاريخ آخر تحديث", "ملاحظات"
            ]
            ws.append(headers)

            # إضافة البيانات المحدثة لجميع الموظفين
            for emp in self.employees_data:
                emp_id = emp.get("الرقم الوظيفي", "")
                service_years = self.calculate_service_years(emp.get("تاريخ أول مباشرة", ""))
                job_title = emp.get("المسمى الوظيفي", "") or emp.get("الوظيفة", "") or emp.get("المنصب", "")
                auto_balance = self.calculate_automatic_balance(service_years, job_title)
                manual_balance = self.get_manual_balance(emp_id)
                used_leaves = self.calculate_used_leaves(emp_id)
                total_balance = auto_balance + manual_balance
                remaining_balance = total_balance - used_leaves

                # البحث عن ملاحظات وتاريخ التحديث
                notes = ""
                last_update = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                for balance in self.balance_data:
                    if str(balance.get("الرقم الوظيفي", "")) == str(emp_id):
                        notes = balance.get("ملاحظات", "")
                        last_update = balance.get("تاريخ آخر تحديث", last_update)
                        break

                row_data = [
                    emp_id,
                    emp.get("الاسم العربي", ""),
                    emp.get("الرقم المالي", ""),
                    emp.get("الرقم الوطني", ""),
                    emp.get("مكان العمل الحالي", ""),
                    f"{emp.get('المؤهل', '')} - {emp.get('التخصص', '')}",
                    emp.get("اسم المصرف", ""),
                    emp.get("رقم الحساب", ""),
                    emp.get("تاريخ أول مباشرة", ""),
                    service_years,
                    auto_balance,
                    manual_balance,
                    total_balance,
                    used_leaves,
                    remaining_balance,
                    last_update,
                    notes
                ]
                ws.append(row_data)

            wb.save(self.balance_data_file)
            print(f"✅ تم حفظ بيانات الرصيد بنجاح في {self.balance_data_file}")
            print(f"📊 تم حفظ بيانات {len(self.employees_data)} موظف")
            return True

        except Exception as e:
            print(f"❌ خطأ في حفظ بيانات الرصيد: {e}")
            return False

    def show_employee_details(self, event):
        """عرض تفاصيل الموظف والإجازات"""
        selected_item = self.balance_table.focus()
        if not selected_item:
            return

        # الحصول على بيانات الموظف المحدد
        selected_values = self.balance_table.item(selected_item)["values"]
        emp_id = selected_values[0]
        emp_name = selected_values[1]

        # إنشاء نافذة التفاصيل بحجم أكبر
        details_window = tk.Toplevel(self.root)
        details_window.title(f"تفاصيل الموظف - {emp_name}")
        details_window.geometry("1000x700")
        details_window.configure(bg="#f0f0f0")
        details_window.resizable(True, True)
        details_window.minsize(800, 600)

        # جعل النافذة في المقدمة
        details_window.transient(self.root)

        # إطار رئيسي
        main_frame = tk.Frame(details_window, bg="#f0f0f0", padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان النافذة
        title_label = tk.Label(main_frame, text=f"📋 تفاصيل الموظف: {emp_name}",
                              font=("Arial", 16, "bold"), fg="#2c3e50", bg="#f0f0f0")
        title_label.pack(pady=(0, 20))

        # إطار معلومات الرصيد
        balance_info_frame = tk.LabelFrame(main_frame, text="معلومات الرصيد",
                                         font=("Arial", 12, "bold"), fg="#2c3e50",
                                         padx=15, pady=15, bg="#ffffff")
        balance_info_frame.pack(fill=tk.X, pady=(0, 20))

        # عرض معلومات الرصيد
        for i, (label, value) in enumerate([
            ("الرقم الوظيفي", selected_values[0]),
            ("سنوات الخدمة", f"{selected_values[3]} سنة"),
            ("الرصيد التلقائي", f"{selected_values[4]} يوم"),
            ("الرصيد اليدوي", f"{selected_values[5]} يوم"),
            ("إجمالي الرصيد", f"{selected_values[6]} يوم"),
            ("الإجازات المأخوذة", f"{selected_values[7]} يوم"),
            ("الرصيد المتبقي", f"{selected_values[8]} يوم")
        ]):
            row = i // 2
            col = i % 2

            info_frame = tk.Frame(balance_info_frame, bg="#ffffff")
            info_frame.grid(row=row, column=col, padx=10, pady=5, sticky="w")

            tk.Label(info_frame, text=f"{label}:", font=("Arial", 10, "bold"),
                    bg="#ffffff").pack(side=tk.LEFT)
            tk.Label(info_frame, text=value, font=("Arial", 10),
                    fg="#e74c3c", bg="#ffffff").pack(side=tk.LEFT, padx=(5, 0))

        # إطار جدول الإجازات
        leaves_frame = tk.LabelFrame(main_frame, text="سجل الإجازات",
                                   font=("Arial", 12, "bold"), fg="#2c3e50",
                                   padx=15, pady=15, bg="#ffffff")
        leaves_frame.pack(fill=tk.BOTH, expand=True)

        # جدول الإجازات
        leaves_scroll = tk.Scrollbar(leaves_frame, orient=tk.VERTICAL)
        leaves_table = ttk.Treeview(leaves_frame, yscrollcommand=leaves_scroll.set)
        leaves_scroll.config(command=leaves_table.yview)

        leaves_table["columns"] = ("نوع الإجازة", "تاريخ البدء", "تاريخ الانتهاء", "عدد الأيام", "الحالة")
        leaves_table.column("#0", width=0, stretch=tk.NO)

        for col in leaves_table["columns"]:
            leaves_table.column(col, width=120, anchor="center")
            leaves_table.heading(col, text=col, anchor="center")

        # إضافة إجازات الموظف
        for leave in self.leaves_data:
            if str(leave.get("الرقم الوظيفي", "")) == str(emp_id):
                leaves_table.insert("", tk.END, values=(
                    leave.get("نوع الإجازة", ""),
                    leave.get("تاريخ البدء", ""),
                    leave.get("تاريخ الانتهاء", ""),
                    leave.get("عدد الأيام", ""),
                    leave.get("الحالة", "")
                ))

        leaves_table.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        leaves_scroll.pack(side=tk.RIGHT, fill=tk.Y)

    def export_balance_data(self):
        """تصدير بيانات الرصيد إلى ملف Excel جديد"""
        try:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_file = f"balance_export_{timestamp}.xlsx"

            wb = Workbook()
            ws = wb.active
            ws.title = "تقرير_أرصدة_الإجازات"

            # العناوين
            headers = [
                "الرقم الوظيفي", "اسم الموظف", "مكان العمل", "سنوات الخدمة",
                "الرصيد التلقائي", "الرصيد اليدوي", "إجمالي الرصيد",
                "الإجازات المأخوذة", "الرصيد المتبقي", "تاريخ التقرير"
            ]
            ws.append(headers)

            # إضافة البيانات
            report_date = datetime.now().strftime("%Y-%m-%d")
            for emp in self.employees_data:
                emp_id = emp.get("الرقم الوظيفي", "")
                emp_name = emp.get("الاسم العربي", "")
                work_place = emp.get("مكان العمل الحالي", "")
                service_years = self.calculate_service_years(emp.get("تاريخ أول مباشرة", ""))
                job_title = emp.get("المسمى الوظيفي", "") or emp.get("الوظيفة", "") or emp.get("المنصب", "")
                auto_balance = self.calculate_automatic_balance(service_years, job_title)
                manual_balance = self.get_manual_balance(emp_id)
                total_balance = auto_balance + manual_balance
                used_leaves = self.calculate_used_leaves(emp_id)
                remaining_balance = total_balance - used_leaves

                ws.append([
                    emp_id, emp_name, work_place, service_years,
                    auto_balance, manual_balance, total_balance,
                    used_leaves, remaining_balance, report_date
                ])

            wb.save(export_file)
            messagebox.showinfo("نجاح", f"تم تصدير البيانات إلى ملف: {export_file}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def deduct_leave_from_balance(self, emp_id, days):
        """خصم أيام الإجازة من رصيد الموظف (يستدعى من نظام الإجازات)"""
        try:
            # إعادة تحميل البيانات للتأكد من الحداثة
            self.employees_data = self.load_employees_data()
            self.leaves_data = self.load_leaves_data()
            self.balance_data = self.load_balance_data()

            # حساب الرصيد الحالي
            emp_data = None
            for emp in self.employees_data:
                if str(emp.get("الرقم الوظيفي", "")) == str(emp_id):
                    emp_data = emp
                    break

            if not emp_data:
                return False, "الموظف غير موجود"

            service_years = self.calculate_service_years(emp_data.get("تاريخ أول مباشرة", ""))
            job_title = emp_data.get("المسمى الوظيفي", "") or emp_data.get("الوظيفة", "") or emp_data.get("المنصب", "")
            auto_balance = self.calculate_automatic_balance(service_years, job_title)
            manual_balance = self.get_manual_balance(emp_id)
            total_balance = auto_balance + manual_balance
            used_leaves = self.calculate_used_leaves(emp_id)
            remaining_balance = total_balance - used_leaves

            # التحقق من كفاية الرصيد
            if remaining_balance < days:
                return False, f"الرصيد المتبقي ({remaining_balance} يوم) غير كافي للإجازة المطلوبة ({days} يوم)"

            # الخصم تم بالفعل عند إضافة الإجازة إلى نظام الإجازات
            # هنا نحدث فقط ملف الرصيد
            self.save_balance_data()

            return True, f"تم خصم {days} يوم من رصيد الموظف. الرصيد المتبقي: {remaining_balance - days} يوم"

        except Exception as e:
            return False, f"خطأ في خصم الرصيد: {str(e)}"

    def close_window(self):
        """إغلاق نافذة نظام رصيد الإجازات"""
        try:
            if messagebox.askyesno("إغلاق النظام", "هل أنت متأكد من إغلاق نظام رصيد الإجازات؟"):
                self.root.destroy()
        except Exception as e:
            print(f"خطأ في إغلاق نظام رصيد الإجازات: {e}")
            self.root.destroy()

# دالة مساعدة للاستدعاء من النظام الرئيسي
def open_leave_balance_system():
    """فتح نظام إدارة رصيد الإجازات"""
    root = tk.Tk()
    app = LeaveBalanceSystem(root)
    root.mainloop()

if __name__ == "__main__":
    open_leave_balance_system()
