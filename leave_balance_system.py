#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة رصيد الإجازات السنوي للموظفين
يحسب رصيد الإجازة بناءً على تاريخ أول مباشرة (شهر لكل سنة عمل)
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import os
import sys

# استيراد أنظمة التحسين
try:
    from data_validation import DataValidator, CalculationEngine
    from error_handler import error_handler, backup_manager
    from performance_optimizer import performance_monitor, memory_optimizer
    ENHANCED_FEATURES = True
    print("✅ تم تحميل أنظمة التحسين لنظام رصيد الإجازات")
except ImportError as e:
    print(f"⚠️ لم يتم تحميل أنظمة التحسين: {e}")
    ENHANCED_FEATURES = False

def get_resource_path(relative_path):
    """الحصول على المسار الصحيح للملفات"""
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

def is_running_as_executable():
    """فحص ما إذا كان التطبيق يعمل كملف تنفيذي"""
    return getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')

try:
    from openpyxl import load_workbook, Workbook
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    messagebox.showerror("خطأ", "مكتبة openpyxl غير مثبتة\nيرجى تثبيتها باستخدام: pip install openpyxl")

class LeaveBalanceSystem:
    """نظام إدارة رصيد الإجازات السنوي المحسن"""

    def __init__(self, root, auto_refresh=True):
        self.root = root
        self.root.title("نظام إدارة رصيد الإجازات السنوي")

        # تهيئة أنظمة التحسين
        if ENHANCED_FEATURES:
            self.validator = DataValidator()
            self.calculator = CalculationEngine()
            self.performance_enabled = True
            print("🔧 تم تفعيل أنظمة التحسين لرصيد الإجازات")
        else:
            self.validator = None
            self.calculator = None
            self.performance_enabled = False
        self.root.geometry("1400x800")
        self.root.configure(bg="#f0f0f0")
        
        # ملفات البيانات
        self.employees_data_file = get_resource_path("employees_data.xlsx") if is_running_as_executable() else "employees_data.xlsx"
        self.leaves_data_file = get_resource_path("leaves_data.xlsx") if is_running_as_executable() else "leaves_data.xlsx"
        self.balance_data_file = get_resource_path("leave_balance_data.xlsx") if is_running_as_executable() else "leave_balance_data.xlsx"
        
        # أسماء الأوراق
        self.employees_sheet_name = "الموظفين"
        self.leaves_sheet_name = "الإجازات"
        self.balance_sheet_name = "رصيد_الإجازات"
        
        # إنشاء ملف رصيد الإجازات إذا لم يكن موجوداً
        self.initialize_balance_file()
        
        # تحميل البيانات
        self.employees_data = self.load_employees_data()
        self.leaves_data = self.load_leaves_data()
        self.balance_data = self.load_balance_data()
        
        # إنشاء واجهة المستخدم
        self.create_ui()
        
        # تحديث البيانات عند التشغيل (حسب المعامل)
        if auto_refresh:
            self.refresh_all_balances(show_message=False)
    
    def initialize_balance_file(self):
        """إنشاء ملف Excel لرصيد الإجازات إذا لم يكن موجوداً"""
        if not OPENPYXL_AVAILABLE:
            return
            
        if not os.path.exists(self.balance_data_file):
            wb = Workbook()
            ws = wb.active
            ws.title = self.balance_sheet_name
            
            # عناوين الأعمدة
            headers = [
                "الرقم الوظيفي", "اسم الموظف", "الرقم المالي", "الرقم الوطني",
                "مكان العمل", "المؤهل والتخصص", "اسم المصرف", "رقم الحساب",
                "تاريخ أول مباشرة", "سنوات الخدمة", "الرصيد المحسوب تلقائياً",
                "الرصيد اليدوي المضاف", "تاريخ بداية الرصيد اليدوي", "تاريخ انتهاء الرصيد اليدوي",
                "إجمالي الرصيد", "الإجازات المأخوذة", "الرصيد المتبقي",
                "تاريخ آخر تحديث", "ملاحظات"
            ]
            
            ws.append(headers)
            wb.save(self.balance_data_file)
            print(f"✅ تم إنشاء ملف رصيد الإجازات: {self.balance_data_file}")
    
    def load_employees_data(self):
        """تحميل بيانات الموظفين من ملف Excel"""
        if not OPENPYXL_AVAILABLE:
            return []
            
        try:
            if not os.path.exists(self.employees_data_file):
                return []
                
            wb = load_workbook(self.employees_data_file)
            
            # البحث عن الورقة المناسبة
            if self.employees_sheet_name in wb.sheetnames:
                ws = wb[self.employees_sheet_name]
            elif "Employees" in wb.sheetnames:
                ws = wb["Employees"]
            else:
                ws = wb.active
            
            data = []
            headers = [cell.value for cell in ws[1]]
            
            for row in ws.iter_rows(min_row=2, values_only=True):
                if any(row):
                    emp_data = dict(zip(headers, row))
                    # التأكد من وجود الحقول المطلوبة
                    if emp_data.get("الرقم الوظيفي") and emp_data.get("الاسم العربي"):
                        data.append(emp_data)
            
            return data
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل بيانات الموظفين: {str(e)}")
            return []
    
    def load_leaves_data(self):
        """تحميل بيانات الإجازات من ملف Excel"""
        if not OPENPYXL_AVAILABLE:
            return []
            
        try:
            if not os.path.exists(self.leaves_data_file):
                return []
                
            wb = load_workbook(self.leaves_data_file)
            
            # البحث عن الورقة المناسبة
            if self.leaves_sheet_name in wb.sheetnames:
                ws = wb[self.leaves_sheet_name]
            elif "Leaves" in wb.sheetnames:
                ws = wb["Leaves"]
            else:
                ws = wb.active
            
            data = []
            headers = [cell.value for cell in ws[1]]
            
            for row in ws.iter_rows(min_row=2, values_only=True):
                if any(row):
                    leave_data = dict(zip(headers, row))
                    # فقط الإجازات الموافق عليها
                    if leave_data.get("الحالة") in ["موافق", "مفعل", "نشط"]:
                        data.append(leave_data)
            
            return data
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل بيانات الإجازات: {str(e)}")
            return []
    
    def load_balance_data(self):
        """تحميل بيانات رصيد الإجازات من ملف Excel"""
        if not OPENPYXL_AVAILABLE:
            return []
            
        try:
            if not os.path.exists(self.balance_data_file):
                return []
                
            wb = load_workbook(self.balance_data_file)
            ws = wb[self.balance_sheet_name]
            
            data = []
            headers = [cell.value for cell in ws[1]]
            
            for row in ws.iter_rows(min_row=2, values_only=True):
                if any(row):
                    data.append(dict(zip(headers, row)))
            
            return data
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل بيانات رصيد الإجازات: {str(e)}")
            return []
    
    def calculate_service_years(self, start_date_str):
        """حساب سنوات الخدمة من تاريخ أول مباشرة"""
        if not start_date_str:
            return 0
            
        try:
            # محاولة تحويل التاريخ بصيغ مختلفة
            start_date = None
            for date_format in ["%Y-%m-%d", "%d/%m/%Y", "%d-%m-%Y"]:
                try:
                    start_date = datetime.strptime(str(start_date_str), date_format)
                    break
                except:
                    continue
            
            if not start_date:
                return 0
            
            # حساب الفرق بالسنوات
            today = datetime.now()
            years = today.year - start_date.year
            
            # تعديل إذا لم يحن موعد الذكرى السنوية بعد
            if today.month < start_date.month or (today.month == start_date.month and today.day < start_date.day):
                years -= 1
            
            return max(0, years)  # لا يمكن أن تكون سالبة
            
        except Exception as e:
            print(f"خطأ في حساب سنوات الخدمة: {e}")
            return 0
    
    def calculate_automatic_balance(self, service_years, job_title=""):
        """حساب الرصيد التلقائي بناءً على المسمى الوظيفي مع التحسينات المتقدمة"""
        # استخدام محرك الحسابات المحسن إذا كان متاحاً
        if ENHANCED_FEATURES and hasattr(self, 'calculator') and self.calculator:
            balance = self.calculator.calculate_leave_balance(service_years, job_title)
            print(f"🔧 استخدام محرك الحسابات المحسن: {balance} يوم")
            return balance

        # الطريقة التقليدية
        job_title_lower = str(job_title).lower()

        # قائمة المسميات التي تستحق رصيد إجازات (موظفين فقط)
        employee_titles = [
            "موظف", "موظفة", "مدير", "مديرة", "رئيس", "رئيسة",
            "مساعد", "مساعدة", "سكرتير", "سكرتيرة", "محاسب", "محاسبة",
            "مراقب", "مراقبة", "مشرف", "مشرفة", "منسق", "منسقة",
            "أخصائي", "أخصائية", "فني", "فنية", "كاتب", "كاتبة"
        ]

        # قائمة المسميات التي لا تستحق رصيد إجازات (معلمين)
        teacher_titles = [
            "معلم", "معلمة", "مدرس", "مدرسة", "أستاذ", "أستاذة",
            "مدرب", "مدربة", "محاضر", "محاضرة"
        ]

        # فحص المسمى الوظيفي - أولوية للمعلمين
        is_teacher = any(title in job_title_lower for title in teacher_titles)
        is_employee = any(title in job_title_lower for title in employee_titles)

        print(f"🔍 فحص المسمى الوظيفي: '{job_title}'")
        print(f"   موظف: {is_employee}, معلم: {is_teacher}")

        # إذا كان معلم، لا يستحق رصيد (حتى لو كان يحتوي على كلمات موظف)
        if is_teacher:
            print(f"   ❌ معلم - لا يستحق رصيد إجازات")
            return 0
        elif is_employee:
            balance = service_years * 30  # شهر لكل سنة
            print(f"   ✅ موظف - يستحق {balance} يوم ({service_years} سنة × 30 يوم)")
            return balance
        else:
            # في حالة عدم وضوح المسمى، اعتبره موظف (الافتراضي)
            balance = service_years * 30
            print(f"   ⚠️ مسمى غير واضح - اعتبار كموظف: {balance} يوم")
            return balance
    
    def calculate_used_leaves(self, employee_id):
        """حساب الإجازات المأخوذة للموظف"""
        used_days = 0
        
        for leave in self.leaves_data:
            if str(leave.get("الرقم الوظيفي", "")) == str(employee_id):
                days = leave.get("عدد الأيام", 0)
                try:
                    used_days += int(days) if days else 0
                except:
                    pass
        
        return used_days

    def get_manual_balance(self, employee_id):
        """الحصول على الرصيد اليدوي المضاف للموظف"""
        for balance in self.balance_data:
            if str(balance.get("الرقم الوظيفي", "")) == str(employee_id):
                manual = balance.get("الرصيد اليدوي المضاف", 0)
                try:
                    return int(manual) if manual else 0
                except:
                    return 0
        return 0

    def get_manual_balance_dates(self, employee_id):
        """الحصول على تواريخ بداية ونهاية الرصيد اليدوي للموظف"""
        for balance in self.balance_data:
            if str(balance.get("الرقم الوظيفي", "")) == str(employee_id):
                start_date = balance.get("تاريخ بداية الرصيد اليدوي", "")
                end_date = balance.get("تاريخ انتهاء الرصيد اليدوي", "")
                return start_date, end_date
        return "", ""

    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg="#f0f0f0", padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان النظام
        title_frame = tk.Frame(main_frame, bg="#2c3e50", relief=tk.RAISED, bd=3)
        title_frame.pack(fill=tk.X, pady=(0, 20))

        title_label = tk.Label(title_frame, text="🏖️ نظام إدارة رصيد الإجازات السنوي",
                              font=("Arial", 20, "bold"), fg="white", bg="#2c3e50", pady=15)
        title_label.pack()

        # إطار الأزرار العلوية
        buttons_frame = tk.Frame(main_frame, bg="#f0f0f0")
        buttons_frame.pack(fill=tk.X, pady=(0, 15))

        # زر تحديث البيانات
        refresh_btn = tk.Button(buttons_frame, text="🔄 تحديث جميع الأرصدة",
                               command=self.refresh_all_balances,
                               font=("Arial", 12, "bold"), bg="#27ae60", fg="white",
                               relief=tk.RAISED, bd=2, padx=15, pady=8)
        refresh_btn.pack(side=tk.LEFT, padx=5)

        # زر إضافة رصيد يدوي
        manual_btn = tk.Button(buttons_frame, text="➕ إضافة رصيد يدوي",
                              command=self.open_manual_balance_window,
                              font=("Arial", 12, "bold"), bg="#3498db", fg="white",
                              relief=tk.RAISED, bd=2, padx=15, pady=8)
        manual_btn.pack(side=tk.LEFT, padx=5)

        # زر تعديل البيانات
        edit_btn = tk.Button(buttons_frame, text="✏️ تعديل البيانات",
                            command=self.edit_selected_employee,
                            font=("Arial", 12, "bold"), bg="#9b59b6", fg="white",
                            relief=tk.RAISED, bd=2, padx=15, pady=8)
        edit_btn.pack(side=tk.LEFT, padx=5)

        # زر تحديث البيانات
        update_btn = tk.Button(buttons_frame, text="🔄 تحديث البيانات",
                              command=self.update_selected_employee,
                              font=("Arial", 12, "bold"), bg="#f39c12", fg="white",
                              relief=tk.RAISED, bd=2, padx=15, pady=8)
        update_btn.pack(side=tk.LEFT, padx=5)

        # زر تصدير البيانات
        export_btn = tk.Button(buttons_frame, text="📊 تصدير البيانات",
                              command=self.export_balance_data_external,
                              font=("Arial", 12, "bold"), bg="#e67e22", fg="white",
                              relief=tk.RAISED, bd=2, padx=15, pady=8)
        export_btn.pack(side=tk.LEFT, padx=5)

        # زر التنبيهات
        alerts_btn = tk.Button(buttons_frame, text="⚠️ التنبيهات",
                              command=self.show_balance_alerts,
                              font=("Arial", 12, "bold"), bg="#e74c3c", fg="white",
                              relief=tk.RAISED, bd=2, padx=15, pady=8)
        alerts_btn.pack(side=tk.LEFT, padx=5)

        # زر الإغلاق
        close_btn = tk.Button(buttons_frame, text="❌ إغلاق",
                             command=self.close_window,
                             font=("Arial", 12, "bold"), bg="#e74c3c", fg="white",
                             relief=tk.RAISED, bd=2, padx=15, pady=8)
        close_btn.pack(side=tk.RIGHT, padx=5)

        # إطار البحث
        search_frame = tk.LabelFrame(main_frame, text="🔍 البحث والتصفية",
                                   font=("Arial", 12, "bold"), fg="#2c3e50",
                                   padx=10, pady=10, bg="#f8f9fa")
        search_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(search_frame, text="بحث حسب:", font=("Arial", 11), bg="#f8f9fa").grid(row=0, column=0, padx=5, sticky="e")

        self.search_by = ttk.Combobox(search_frame, values=[
            "اسم الموظف", "الرقم الوظيفي", "مكان العمل"
        ], state="readonly", width=15)
        self.search_by.grid(row=0, column=1, padx=5, sticky="w")
        self.search_by.current(0)

        self.search_entry = tk.Entry(search_frame, width=30, font=("Arial", 11))
        self.search_entry.grid(row=0, column=2, padx=5, sticky="w")

        search_btn = tk.Button(search_frame, text="بحث", command=self.search_employees,
                              font=("Arial", 10, "bold"), bg="#3498db", fg="white",
                              relief=tk.RAISED, bd=1, padx=10, pady=3)
        search_btn.grid(row=0, column=3, padx=5)

        reset_btn = tk.Button(search_frame, text="إعادة تعيين", command=self.reset_search,
                             font=("Arial", 10, "bold"), bg="#95a5a6", fg="white",
                             relief=tk.RAISED, bd=1, padx=10, pady=3)
        reset_btn.grid(row=0, column=4, padx=5)

        # إطار الجدول
        table_frame = tk.Frame(main_frame, bg="#f0f0f0")
        table_frame.pack(fill=tk.BOTH, expand=True)

        # شريط التمرير
        scroll_y = tk.Scrollbar(table_frame, orient=tk.VERTICAL)
        scroll_x = tk.Scrollbar(table_frame, orient=tk.HORIZONTAL)

        # إنشاء الجدول
        self.balance_table = ttk.Treeview(
            table_frame,
            yscrollcommand=scroll_y.set,
            xscrollcommand=scroll_x.set,
            selectmode="browse"
        )

        scroll_y.config(command=self.balance_table.yview)
        scroll_x.config(command=self.balance_table.xview)

        # تعريف الأعمدة
        self.balance_table["columns"] = (
            "الرقم الوظيفي", "اسم الموظف", "مكان العمل", "سنوات الخدمة",
            "الرصيد التلقائي", "الرصيد اليدوي", "تاريخ البداية", "تاريخ الانتهاء",
            "إجمالي الرصيد", "الإجازات المأخوذة", "الرصيد المتبقي"
        )

        # تنسيق الأعمدة
        self.balance_table.column("#0", width=0, stretch=tk.NO)
        self.balance_table.column("الرقم الوظيفي", width=100, anchor="center")
        self.balance_table.column("اسم الموظف", width=150, anchor="center")
        self.balance_table.column("مكان العمل", width=120, anchor="center")
        self.balance_table.column("سنوات الخدمة", width=100, anchor="center")
        self.balance_table.column("الرصيد التلقائي", width=100, anchor="center")
        self.balance_table.column("الرصيد اليدوي", width=100, anchor="center")
        self.balance_table.column("تاريخ البداية", width=110, anchor="center")
        self.balance_table.column("تاريخ الانتهاء", width=110, anchor="center")
        self.balance_table.column("إجمالي الرصيد", width=100, anchor="center")
        self.balance_table.column("الإجازات المأخوذة", width=120, anchor="center")
        self.balance_table.column("الرصيد المتبقي", width=100, anchor="center")

        # عناوين الأعمدة
        self.balance_table.heading("#0", text="", anchor="center")
        self.balance_table.heading("الرقم الوظيفي", text="الرقم الوظيفي", anchor="center")
        self.balance_table.heading("اسم الموظف", text="اسم الموظف", anchor="center")
        self.balance_table.heading("مكان العمل", text="مكان العمل", anchor="center")
        self.balance_table.heading("سنوات الخدمة", text="سنوات الخدمة", anchor="center")
        self.balance_table.heading("الرصيد التلقائي", text="الرصيد التلقائي", anchor="center")
        self.balance_table.heading("الرصيد اليدوي", text="الرصيد اليدوي", anchor="center")
        self.balance_table.heading("تاريخ البداية", text="تاريخ البداية", anchor="center")
        self.balance_table.heading("تاريخ الانتهاء", text="تاريخ الانتهاء", anchor="center")
        self.balance_table.heading("إجمالي الرصيد", text="إجمالي الرصيد", anchor="center")
        self.balance_table.heading("الإجازات المأخوذة", text="الإجازات المأخوذة", anchor="center")
        self.balance_table.heading("الرصيد المتبقي", text="الرصيد المتبقي", anchor="center")

        # وضع الجدول وشريط التمرير
        self.balance_table.grid(row=0, column=0, sticky="nsew")
        scroll_y.grid(row=0, column=1, sticky="ns")
        scroll_x.grid(row=1, column=0, sticky="ew")

        # جعل الجدول قابل للتوسع
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # ربط النقر المزدوج لعرض تفاصيل الموظف
        self.balance_table.bind("<Double-1>", self.show_employee_details)

    def refresh_all_balances(self, show_message=True):
        """تحديث جميع أرصدة الإجازات"""
        try:
            # إعادة تحميل البيانات
            self.employees_data = self.load_employees_data()
            self.leaves_data = self.load_leaves_data()
            self.balance_data = self.load_balance_data()

            # تحديث الجدول
            self.update_balance_table()

            # حفظ البيانات المحدثة
            self.save_balance_data()

            # إظهار الرسالة فقط عند الطلب (عند الضغط على الزر)
            if show_message:
                messagebox.showinfo("نجاح", "تم تحديث جميع الأرصدة بنجاح")

            print("✅ تم تحديث جميع الأرصدة بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث الأرصدة: {str(e)}")

    def update_balance_table(self, data=None):
        """تحديث جدول الأرصدة"""
        # مسح البيانات الحالية
        for item in self.balance_table.get_children():
            self.balance_table.delete(item)

        # استخدام البيانات المقدمة أو البيانات الكاملة
        display_data = data if data else self.employees_data

        # إضافة البيانات إلى الجدول
        for emp in display_data:
            emp_id = emp.get("الرقم الوظيفي", "")
            emp_name = emp.get("الاسم العربي", "")
            work_place = emp.get("مكان العمل الحالي", "")
            start_date = emp.get("تاريخ أول مباشرة", "")

            # حساب البيانات
            service_years = self.calculate_service_years(start_date)
            job_title = emp.get("المسمى الوظيفي", "") or emp.get("الوظيفة", "") or emp.get("المنصب", "")
            auto_balance = self.calculate_automatic_balance(service_years, job_title)
            manual_balance = self.get_manual_balance(emp_id)

            # الحصول على تواريخ الرصيد اليدوي
            start_date_manual, end_date_manual = self.get_manual_balance_dates(emp_id)

            total_balance = auto_balance + manual_balance
            used_leaves = self.calculate_used_leaves(emp_id)
            remaining_balance = total_balance - used_leaves

            # إضافة الصف إلى الجدول
            self.balance_table.insert("", tk.END, values=(
                emp_id, emp_name, work_place, service_years,
                auto_balance, manual_balance, start_date_manual, end_date_manual,
                total_balance, used_leaves, remaining_balance
            ))

    def search_employees(self):
        """البحث في بيانات الموظفين"""
        search_criteria = self.search_by.get()
        search_term = self.search_entry.get().strip().lower()

        if not search_term:
            messagebox.showwarning("تحذير", "الرجاء إدخال مصطلح البحث")
            return

        filtered_data = []
        for emp in self.employees_data:
            if search_criteria == "اسم الموظف":
                if search_term in emp.get("الاسم العربي", "").lower():
                    filtered_data.append(emp)
            elif search_criteria == "الرقم الوظيفي":
                if search_term in str(emp.get("الرقم الوظيفي", "")).lower():
                    filtered_data.append(emp)
            elif search_criteria == "مكان العمل":
                if search_term in emp.get("مكان العمل الحالي", "").lower():
                    filtered_data.append(emp)

        if not filtered_data:
            messagebox.showinfo("نتائج البحث", "لا توجد نتائج مطابقة للبحث")
        else:
            self.update_balance_table(filtered_data)

    def reset_search(self):
        """إعادة تعيين البحث وعرض جميع الموظفين"""
        self.search_entry.delete(0, tk.END)
        self.update_balance_table()

    def open_manual_balance_window(self):
        """فتح نافذة إضافة الرصيد اليدوي"""
        selected_item = self.balance_table.focus()
        if not selected_item:
            messagebox.showwarning("تحذير", "الرجاء تحديد موظف لتعديل رصيده")
            return

        # الحصول على بيانات الموظف المحدد
        selected_values = self.balance_table.item(selected_item)["values"]
        emp_id = selected_values[0]
        emp_name = selected_values[1]
        current_manual = selected_values[5]

        # إنشاء نافذة جديدة بحجم أكبر
        manual_window = tk.Toplevel(self.root)
        manual_window.title(f"إدارة الرصيد اليدوي - {emp_name}")
        manual_window.geometry("700x600")
        manual_window.configure(bg="#f0f0f0")
        manual_window.resizable(True, True)
        manual_window.minsize(600, 500)

        # جعل النافذة في المقدمة
        manual_window.transient(self.root)
        manual_window.grab_set()

        # إطار رئيسي بحشو أكبر
        main_frame = tk.Frame(manual_window, bg="#f0f0f0", padx=30, pady=30)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان النافذة بخط أكبر
        title_label = tk.Label(main_frame, text="➕ إدارة الرصيد اليدوي",
                              font=("Arial", 20, "bold"), fg="#2c3e50", bg="#f0f0f0")
        title_label.pack(pady=(0, 25))

        # معلومات الموظف بحجم أكبر
        info_frame = tk.LabelFrame(main_frame, text="معلومات الموظف",
                                  font=("Arial", 14, "bold"), fg="#2c3e50",
                                  padx=20, pady=20, bg="#ffffff")
        info_frame.pack(fill=tk.X, pady=(0, 25))

        tk.Label(info_frame, text=f"الرقم الوظيفي: {emp_id}",
                font=("Arial", 13), bg="#ffffff").pack(anchor="w", pady=5)
        tk.Label(info_frame, text=f"اسم الموظف: {emp_name}",
                font=("Arial", 13), bg="#ffffff").pack(anchor="w", pady=5)
        tk.Label(info_frame, text=f"الرصيد اليدوي الحالي: {current_manual} يوم",
                font=("Arial", 13, "bold"), fg="#e74c3c", bg="#ffffff").pack(anchor="w", pady=5)

        # إطار إدخال الرصيد الجديد بحجم أكبر
        balance_frame = tk.LabelFrame(main_frame, text="الرصيد الجديد",
                                     font=("Arial", 14, "bold"), fg="#2c3e50",
                                     padx=20, pady=20, bg="#ffffff")
        balance_frame.pack(fill=tk.X, pady=(0, 25))

        tk.Label(balance_frame, text="الرصيد اليدوي الجديد (بالأيام):",
                font=("Arial", 13), bg="#ffffff").pack(anchor="w", pady=(0, 8))

        balance_entry = tk.Entry(balance_frame, font=("Arial", 14), width=25,
                                relief=tk.GROOVE, bd=2, justify='center')
        balance_entry.pack(pady=(0, 15), ipady=5)
        balance_entry.insert(0, str(current_manual))
        balance_entry.focus_set()

        # إطار التواريخ
        dates_frame = tk.Frame(balance_frame, bg="#ffffff")
        dates_frame.pack(fill=tk.X, pady=(10, 15))

        # تاريخ البداية
        start_frame = tk.Frame(dates_frame, bg="#ffffff")
        start_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        tk.Label(start_frame, text="تاريخ بداية الرصيد:",
                font=("Arial", 13), bg="#ffffff").pack(anchor="w")

        start_date_entry = tk.Entry(start_frame, font=("Arial", 12), width=15,
                                   relief=tk.GROOVE, bd=2, justify='center')
        start_date_entry.pack(pady=(5, 0))
        start_date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))

        # تاريخ الانتهاء
        end_frame = tk.Frame(dates_frame, bg="#ffffff")
        end_frame.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))

        tk.Label(end_frame, text="تاريخ انتهاء الرصيد:",
                font=("Arial", 13), bg="#ffffff").pack(anchor="w")

        end_date_entry = tk.Entry(end_frame, font=("Arial", 12), width=15,
                                 relief=tk.GROOVE, bd=2, justify='center')
        end_date_entry.pack(pady=(5, 0))
        # تاريخ انتهاء افتراضي بعد سنة
        from datetime import timedelta
        default_end = datetime.now() + timedelta(days=365)
        end_date_entry.insert(0, default_end.strftime("%Y-%m-%d"))

        # ملاحظة التنسيق
        format_label = tk.Label(balance_frame, text="تنسيق التاريخ: YYYY-MM-DD (مثال: 2024-12-31)",
                               font=("Arial", 10), fg="#7f8c8d", bg="#ffffff")
        format_label.pack(pady=(5, 10))

        tk.Label(balance_frame, text="ملاحظات (اختيارية):",
                font=("Arial", 13), bg="#ffffff").pack(anchor="w", pady=(15, 8))

        notes_text = tk.Text(balance_frame, height=6, width=50, font=("Arial", 12),
                            relief=tk.GROOVE, bd=2)
        notes_text.pack(pady=(0, 15))

        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg="#f0f0f0")
        buttons_frame.pack(fill=tk.X, pady=10)

        def save_manual_balance():
            """حفظ الرصيد اليدوي الجديد"""
            try:
                new_balance = balance_entry.get().strip()
                start_date = start_date_entry.get().strip()
                end_date = end_date_entry.get().strip()
                notes = notes_text.get("1.0", tk.END).strip()

                # التحقق من صحة الرقم
                try:
                    balance_value = int(new_balance) if new_balance else 0
                except ValueError:
                    messagebox.showerror("خطأ", "الرجاء إدخال رقم صحيح للرصيد")
                    return

                # التحقق من صحة التواريخ
                try:
                    if start_date:
                        datetime.strptime(start_date, "%Y-%m-%d")
                    if end_date:
                        datetime.strptime(end_date, "%Y-%m-%d")

                    # التحقق من أن تاريخ الانتهاء بعد تاريخ البداية
                    if start_date and end_date:
                        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
                        if end_dt <= start_dt:
                            messagebox.showerror("خطأ", "تاريخ الانتهاء يجب أن يكون بعد تاريخ البداية")
                            return

                except ValueError:
                    messagebox.showerror("خطأ", "تنسيق التاريخ غير صحيح. استخدم YYYY-MM-DD")
                    return

                # تحديث البيانات
                self.update_manual_balance_with_dates(emp_id, balance_value, start_date, end_date, notes)

                # حفظ البيانات فوراً
                save_success = self.save_balance_data()

                if save_success:
                    # تحديث الجدول
                    self.update_balance_table()

                    # إغلاق النافذة
                    manual_window.destroy()

                    messagebox.showinfo("نجاح", f"تم تحديث وحفظ الرصيد اليدوي للموظف {emp_name}\nالرصيد الجديد: {balance_value} يوم\nمن {start_date} إلى {end_date}")
                else:
                    messagebox.showerror("خطأ", "تم تحديث البيانات ولكن فشل في حفظ الملف")

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ الرصيد: {str(e)}")

        # أزرار الحفظ والإلغاء بحجم أكبر
        save_btn = tk.Button(buttons_frame, text="💾 حفظ",
                            command=save_manual_balance,
                            font=("Arial", 14, "bold"), bg="#27ae60", fg="white",
                            relief=tk.RAISED, bd=3, padx=30, pady=12, width=12)
        save_btn.pack(side=tk.LEFT, padx=10)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء",
                              command=manual_window.destroy,
                              font=("Arial", 14, "bold"), bg="#e74c3c", fg="white",
                              relief=tk.RAISED, bd=3, padx=30, pady=12, width=12)
        cancel_btn.pack(side=tk.RIGHT, padx=10)

    def edit_selected_employee(self):
        """تعديل بيانات الموظف المحدد"""
        selected_item = self.balance_table.focus()
        if not selected_item:
            messagebox.showwarning("تحذير", "الرجاء تحديد موظف لتعديل بياناته")
            return

        # الحصول على بيانات الموظف المحدد
        selected_values = self.balance_table.item(selected_item)["values"]
        emp_id = selected_values[0]
        emp_name = selected_values[1]

        # البحث عن بيانات الموظف الكاملة
        emp_data = None
        for emp in self.employees_data:
            if str(emp.get("الرقم الوظيفي", "")) == str(emp_id):
                emp_data = emp
                break

        if not emp_data:
            messagebox.showerror("خطأ", "لم يتم العثور على بيانات الموظف")
            return

        # إنشاء نافذة التعديل
        edit_window = tk.Toplevel(self.root)
        edit_window.title(f"تعديل بيانات الموظف - {emp_name}")
        edit_window.geometry("800x700")
        edit_window.configure(bg="#f0f0f0")
        edit_window.resizable(True, True)
        edit_window.minsize(700, 600)

        # جعل النافذة في المقدمة
        edit_window.transient(self.root)
        edit_window.grab_set()

        # إطار رئيسي
        main_frame = tk.Frame(edit_window, bg="#f0f0f0", padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان النافذة
        title_label = tk.Label(main_frame, text="✏️ تعديل بيانات الموظف",
                              font=("Arial", 18, "bold"), fg="#2c3e50", bg="#f0f0f0")
        title_label.pack(pady=(0, 20))

        # إطار البيانات الأساسية
        basic_frame = tk.LabelFrame(main_frame, text="البيانات الأساسية",
                                   font=("Arial", 14, "bold"), fg="#2c3e50",
                                   padx=20, pady=20, bg="#ffffff")
        basic_frame.pack(fill=tk.X, pady=(0, 15))

        # حقول البيانات
        fields = {}

        # الصف الأول
        row1_frame = tk.Frame(basic_frame, bg="#ffffff")
        row1_frame.pack(fill=tk.X, pady=5)

        tk.Label(row1_frame, text="الرقم الوظيفي:", font=("Arial", 12), bg="#ffffff").pack(side=tk.LEFT)
        fields["الرقم الوظيفي"] = tk.Entry(row1_frame, font=("Arial", 12), width=20, state="readonly")
        fields["الرقم الوظيفي"].pack(side=tk.LEFT, padx=(10, 20))
        fields["الرقم الوظيفي"].insert(0, emp_data.get("الرقم الوظيفي", ""))

        tk.Label(row1_frame, text="الاسم العربي:", font=("Arial", 12), bg="#ffffff").pack(side=tk.LEFT)
        fields["الاسم العربي"] = tk.Entry(row1_frame, font=("Arial", 12), width=25)
        fields["الاسم العربي"].pack(side=tk.LEFT, padx=(10, 0))
        fields["الاسم العربي"].insert(0, emp_data.get("الاسم العربي", ""))

        # الصف الثاني
        row2_frame = tk.Frame(basic_frame, bg="#ffffff")
        row2_frame.pack(fill=tk.X, pady=5)

        tk.Label(row2_frame, text="مكان العمل:", font=("Arial", 12), bg="#ffffff").pack(side=tk.LEFT)
        fields["مكان العمل الحالي"] = tk.Entry(row2_frame, font=("Arial", 12), width=20)
        fields["مكان العمل الحالي"].pack(side=tk.LEFT, padx=(10, 20))
        fields["مكان العمل الحالي"].insert(0, emp_data.get("مكان العمل الحالي", ""))

        tk.Label(row2_frame, text="المسمى الوظيفي:", font=("Arial", 12), bg="#ffffff").pack(side=tk.LEFT)
        fields["المسمى الوظيفي"] = tk.Entry(row2_frame, font=("Arial", 12), width=25)
        fields["المسمى الوظيفي"].pack(side=tk.LEFT, padx=(10, 0))
        fields["المسمى الوظيفي"].insert(0, emp_data.get("المسمى الوظيفي", ""))

        # إطار الرصيد اليدوي
        balance_frame = tk.LabelFrame(main_frame, text="الرصيد اليدوي",
                                     font=("Arial", 14, "bold"), fg="#2c3e50",
                                     padx=20, pady=20, bg="#ffffff")
        balance_frame.pack(fill=tk.X, pady=(0, 15))

        # الرصيد الحالي
        current_manual = self.get_manual_balance(emp_id)
        current_start, current_end = self.get_manual_balance_dates(emp_id)

        balance_info_frame = tk.Frame(balance_frame, bg="#ffffff")
        balance_info_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(balance_info_frame, text=f"الرصيد اليدوي الحالي: {current_manual} يوم",
                font=("Arial", 12, "bold"), fg="#e74c3c", bg="#ffffff").pack(anchor="w")

        if current_start and current_end:
            tk.Label(balance_info_frame, text=f"الفترة الحالية: من {current_start} إلى {current_end}",
                    font=("Arial", 11), fg="#7f8c8d", bg="#ffffff").pack(anchor="w")

        # حقول الرصيد الجديد
        balance_input_frame = tk.Frame(balance_frame, bg="#ffffff")
        balance_input_frame.pack(fill=tk.X)

        tk.Label(balance_input_frame, text="رصيد جديد:", font=("Arial", 12), bg="#ffffff").pack(side=tk.LEFT)
        fields["رصيد_يدوي"] = tk.Entry(balance_input_frame, font=("Arial", 12), width=10)
        fields["رصيد_يدوي"].pack(side=tk.LEFT, padx=(10, 20))
        fields["رصيد_يدوي"].insert(0, str(current_manual))

        tk.Label(balance_input_frame, text="من:", font=("Arial", 12), bg="#ffffff").pack(side=tk.LEFT)
        fields["تاريخ_البداية"] = tk.Entry(balance_input_frame, font=("Arial", 12), width=12)
        fields["تاريخ_البداية"].pack(side=tk.LEFT, padx=(5, 10))
        fields["تاريخ_البداية"].insert(0, current_start or datetime.now().strftime("%Y-%m-%d"))

        tk.Label(balance_input_frame, text="إلى:", font=("Arial", 12), bg="#ffffff").pack(side=tk.LEFT)
        fields["تاريخ_الانتهاء"] = tk.Entry(balance_input_frame, font=("Arial", 12), width=12)
        fields["تاريخ_الانتهاء"].pack(side=tk.LEFT, padx=(5, 0))
        from datetime import timedelta
        default_end = datetime.now() + timedelta(days=365)
        fields["تاريخ_الانتهاء"].insert(0, current_end or default_end.strftime("%Y-%m-%d"))

        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg="#f0f0f0")
        buttons_frame.pack(fill=tk.X, pady=20)

        def save_changes():
            """حفظ التغييرات"""
            try:
                # تحديث البيانات الأساسية
                for field_name, entry in fields.items():
                    if field_name not in ["رصيد_يدوي", "تاريخ_البداية", "تاريخ_الانتهاء"]:
                        emp_data[field_name] = entry.get().strip()

                # تحديث الرصيد اليدوي
                new_balance = int(fields["رصيد_يدوي"].get().strip() or "0")
                start_date = fields["تاريخ_البداية"].get().strip()
                end_date = fields["تاريخ_الانتهاء"].get().strip()

                # التحقق من التواريخ
                if start_date and end_date:
                    start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                    end_dt = datetime.strptime(end_date, "%Y-%m-%d")
                    if end_dt <= start_dt:
                        messagebox.showerror("خطأ", "تاريخ الانتهاء يجب أن يكون بعد تاريخ البداية")
                        return

                # حفظ التغييرات
                self.update_manual_balance_with_dates(emp_id, new_balance, start_date, end_date, "تم التعديل")
                self.save_balance_data()
                self.update_balance_table()

                edit_window.destroy()
                messagebox.showinfo("نجاح", f"تم حفظ التغييرات للموظف {emp_name}")

            except ValueError as e:
                messagebox.showerror("خطأ", "تنسيق التاريخ غير صحيح. استخدم YYYY-MM-DD")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ التغييرات: {str(e)}")

        # أزرار الحفظ والإلغاء
        save_btn = tk.Button(buttons_frame, text="💾 حفظ التغييرات",
                            command=save_changes,
                            font=("Arial", 12, "bold"), bg="#27ae60", fg="white",
                            relief=tk.RAISED, bd=3, padx=20, pady=10)
        save_btn.pack(side=tk.LEFT, padx=10)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء",
                              command=edit_window.destroy,
                              font=("Arial", 12, "bold"), bg="#e74c3c", fg="white",
                              relief=tk.RAISED, bd=3, padx=20, pady=10)
        cancel_btn.pack(side=tk.RIGHT, padx=10)

    def update_selected_employee(self):
        """تحديث بيانات الموظف المحدد"""
        selected_item = self.balance_table.focus()
        if not selected_item:
            messagebox.showwarning("تحذير", "الرجاء تحديد موظف لتحديث بياناته")
            return

        # الحصول على بيانات الموظف المحدد
        selected_values = self.balance_table.item(selected_item)["values"]
        emp_id = selected_values[0]
        emp_name = selected_values[1]

        # تأكيد التحديث
        if messagebox.askyesno("تأكيد التحديث", f"هل تريد تحديث بيانات الموظف {emp_name}؟"):
            try:
                # إعادة تحميل البيانات
                self.employees_data = self.load_employees_data()
                self.leaves_data = self.load_leaves_data()
                self.balance_data = self.load_balance_data()

                # تحديث الجدول
                self.update_balance_table()

                messagebox.showinfo("نجاح", f"تم تحديث بيانات الموظف {emp_name}")

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث البيانات: {str(e)}")

    def update_manual_balance(self, emp_id, balance_value, notes=""):
        """تحديث الرصيد اليدوي للموظف (للتوافق مع النسخة القديمة)"""
        self.update_manual_balance_with_dates(emp_id, balance_value, "", "", notes)

    def update_manual_balance_with_dates(self, emp_id, balance_value, start_date="", end_date="", notes=""):
        """تحديث الرصيد اليدوي للموظف مع التواريخ"""
        print(f"🔄 تحديث الرصيد اليدوي للموظف {emp_id}: {balance_value} يوم")
        if start_date and end_date:
            print(f"📅 فترة الرصيد: من {start_date} إلى {end_date}")

        # البحث عن الموظف في بيانات الرصيد
        found = False
        for balance in self.balance_data:
            if str(balance.get("الرقم الوظيفي", "")) == str(emp_id):
                old_balance = balance.get("الرصيد اليدوي المضاف", 0)
                balance["الرصيد اليدوي المضاف"] = balance_value
                balance["تاريخ بداية الرصيد اليدوي"] = start_date
                balance["تاريخ انتهاء الرصيد اليدوي"] = end_date
                balance["تاريخ آخر تحديث"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                if notes:
                    balance["ملاحظات"] = notes
                found = True
                print(f"✅ تم تحديث الرصيد من {old_balance} إلى {balance_value}")
                break

        # إذا لم يوجد، أضف سجل جديد
        if not found:
            print(f"📝 إنشاء سجل رصيد جديد للموظف {emp_id}")

            # البحث عن بيانات الموظف
            emp_data = None
            for emp in self.employees_data:
                if str(emp.get("الرقم الوظيفي", "")) == str(emp_id):
                    emp_data = emp
                    break

            if emp_data:
                service_years = self.calculate_service_years(emp_data.get("تاريخ أول مباشرة", ""))
                job_title = emp_data.get("المسمى الوظيفي", "") or emp_data.get("الوظيفة", "") or emp_data.get("المنصب", "")
                auto_balance = self.calculate_automatic_balance(service_years, job_title)
                used_leaves = self.calculate_used_leaves(emp_id)
                total_balance = auto_balance + balance_value
                remaining_balance = total_balance - used_leaves

                new_balance_record = {
                    "الرقم الوظيفي": emp_id,
                    "اسم الموظف": emp_data.get("الاسم العربي", ""),
                    "الرقم المالي": emp_data.get("الرقم المالي", ""),
                    "الرقم الوطني": emp_data.get("الرقم الوطني", ""),
                    "مكان العمل": emp_data.get("مكان العمل الحالي", ""),
                    "المؤهل والتخصص": f"{emp_data.get('المؤهل', '')} - {emp_data.get('التخصص', '')}",
                    "اسم المصرف": emp_data.get("اسم المصرف", ""),
                    "رقم الحساب": emp_data.get("رقم الحساب", ""),
                    "تاريخ أول مباشرة": emp_data.get("تاريخ أول مباشرة", ""),
                    "سنوات الخدمة": service_years,
                    "الرصيد المحسوب تلقائياً": auto_balance,
                    "الرصيد اليدوي المضاف": balance_value,
                    "تاريخ بداية الرصيد اليدوي": start_date,
                    "تاريخ انتهاء الرصيد اليدوي": end_date,
                    "إجمالي الرصيد": total_balance,
                    "الإجازات المأخوذة": used_leaves,
                    "الرصيد المتبقي": remaining_balance,
                    "تاريخ آخر تحديث": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "ملاحظات": notes
                }

                self.balance_data.append(new_balance_record)
                print(f"✅ تم إنشاء سجل جديد بالرصيد {balance_value}")
            else:
                print(f"❌ لم يتم العثور على بيانات الموظف {emp_id}")

    def save_balance_data(self):
        """حفظ بيانات الرصيد في ملف Excel"""
        if not OPENPYXL_AVAILABLE:
            print("❌ مكتبة openpyxl غير متاحة")
            return False

        try:
            print(f"💾 بدء حفظ بيانات الرصيد في {self.balance_data_file}")

            # إنشاء ملف جديد
            wb = Workbook()
            ws = wb.active
            ws.title = self.balance_sheet_name

            # العناوين
            headers = [
                "الرقم الوظيفي", "اسم الموظف", "الرقم المالي", "الرقم الوطني",
                "مكان العمل", "المؤهل والتخصص", "اسم المصرف", "رقم الحساب",
                "تاريخ أول مباشرة", "سنوات الخدمة", "الرصيد المحسوب تلقائياً",
                "الرصيد اليدوي المضاف", "إجمالي الرصيد", "الإجازات المأخوذة",
                "الرصيد المتبقي", "تاريخ آخر تحديث", "ملاحظات"
            ]
            ws.append(headers)

            # إضافة البيانات المحدثة لجميع الموظفين
            for emp in self.employees_data:
                emp_id = emp.get("الرقم الوظيفي", "")
                service_years = self.calculate_service_years(emp.get("تاريخ أول مباشرة", ""))
                job_title = emp.get("المسمى الوظيفي", "") or emp.get("الوظيفة", "") or emp.get("المنصب", "")
                auto_balance = self.calculate_automatic_balance(service_years, job_title)
                manual_balance = self.get_manual_balance(emp_id)
                used_leaves = self.calculate_used_leaves(emp_id)
                total_balance = auto_balance + manual_balance
                remaining_balance = total_balance - used_leaves

                # البحث عن ملاحظات وتاريخ التحديث
                notes = ""
                last_update = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                for balance in self.balance_data:
                    if str(balance.get("الرقم الوظيفي", "")) == str(emp_id):
                        notes = balance.get("ملاحظات", "")
                        last_update = balance.get("تاريخ آخر تحديث", last_update)
                        break

                row_data = [
                    emp_id,
                    emp.get("الاسم العربي", ""),
                    emp.get("الرقم المالي", ""),
                    emp.get("الرقم الوطني", ""),
                    emp.get("مكان العمل الحالي", ""),
                    f"{emp.get('المؤهل', '')} - {emp.get('التخصص', '')}",
                    emp.get("اسم المصرف", ""),
                    emp.get("رقم الحساب", ""),
                    emp.get("تاريخ أول مباشرة", ""),
                    service_years,
                    auto_balance,
                    manual_balance,
                    total_balance,
                    used_leaves,
                    remaining_balance,
                    last_update,
                    notes
                ]
                ws.append(row_data)

            wb.save(self.balance_data_file)
            print(f"✅ تم حفظ بيانات الرصيد بنجاح في {self.balance_data_file}")
            print(f"📊 تم حفظ بيانات {len(self.employees_data)} موظف")
            return True

        except Exception as e:
            print(f"❌ خطأ في حفظ بيانات الرصيد: {e}")
            return False

    def show_employee_details(self, event):
        """عرض تفاصيل الموظف والإجازات"""
        selected_item = self.balance_table.focus()
        if not selected_item:
            return

        # الحصول على بيانات الموظف المحدد
        selected_values = self.balance_table.item(selected_item)["values"]
        emp_id = selected_values[0]
        emp_name = selected_values[1]

        # إنشاء نافذة التفاصيل بحجم أكبر
        details_window = tk.Toplevel(self.root)
        details_window.title(f"تفاصيل الموظف - {emp_name}")
        details_window.geometry("1000x700")
        details_window.configure(bg="#f0f0f0")
        details_window.resizable(True, True)
        details_window.minsize(800, 600)

        # جعل النافذة في المقدمة
        details_window.transient(self.root)

        # إطار رئيسي
        main_frame = tk.Frame(details_window, bg="#f0f0f0", padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان النافذة
        title_label = tk.Label(main_frame, text=f"📋 تفاصيل الموظف: {emp_name}",
                              font=("Arial", 16, "bold"), fg="#2c3e50", bg="#f0f0f0")
        title_label.pack(pady=(0, 20))

        # إطار معلومات الرصيد
        balance_info_frame = tk.LabelFrame(main_frame, text="معلومات الرصيد",
                                         font=("Arial", 12, "bold"), fg="#2c3e50",
                                         padx=15, pady=15, bg="#ffffff")
        balance_info_frame.pack(fill=tk.X, pady=(0, 20))

        # عرض معلومات الرصيد
        for i, (label, value) in enumerate([
            ("الرقم الوظيفي", selected_values[0]),
            ("سنوات الخدمة", f"{selected_values[3]} سنة"),
            ("الرصيد التلقائي", f"{selected_values[4]} يوم"),
            ("الرصيد اليدوي", f"{selected_values[5]} يوم"),
            ("إجمالي الرصيد", f"{selected_values[6]} يوم"),
            ("الإجازات المأخوذة", f"{selected_values[7]} يوم"),
            ("الرصيد المتبقي", f"{selected_values[8]} يوم")
        ]):
            row = i // 2
            col = i % 2

            info_frame = tk.Frame(balance_info_frame, bg="#ffffff")
            info_frame.grid(row=row, column=col, padx=10, pady=5, sticky="w")

            tk.Label(info_frame, text=f"{label}:", font=("Arial", 10, "bold"),
                    bg="#ffffff").pack(side=tk.LEFT)
            tk.Label(info_frame, text=value, font=("Arial", 10),
                    fg="#e74c3c", bg="#ffffff").pack(side=tk.LEFT, padx=(5, 0))

        # إطار جدول الإجازات
        leaves_frame = tk.LabelFrame(main_frame, text="سجل الإجازات",
                                   font=("Arial", 12, "bold"), fg="#2c3e50",
                                   padx=15, pady=15, bg="#ffffff")
        leaves_frame.pack(fill=tk.BOTH, expand=True)

        # جدول الإجازات
        leaves_scroll = tk.Scrollbar(leaves_frame, orient=tk.VERTICAL)
        leaves_table = ttk.Treeview(leaves_frame, yscrollcommand=leaves_scroll.set)
        leaves_scroll.config(command=leaves_table.yview)

        leaves_table["columns"] = ("نوع الإجازة", "تاريخ البدء", "تاريخ الانتهاء", "عدد الأيام", "الحالة")
        leaves_table.column("#0", width=0, stretch=tk.NO)

        for col in leaves_table["columns"]:
            leaves_table.column(col, width=120, anchor="center")
            leaves_table.heading(col, text=col, anchor="center")

        # إضافة إجازات الموظف
        for leave in self.leaves_data:
            if str(leave.get("الرقم الوظيفي", "")) == str(emp_id):
                leaves_table.insert("", tk.END, values=(
                    leave.get("نوع الإجازة", ""),
                    leave.get("تاريخ البدء", ""),
                    leave.get("تاريخ الانتهاء", ""),
                    leave.get("عدد الأيام", ""),
                    leave.get("الحالة", "")
                ))

        leaves_table.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        leaves_scroll.pack(side=tk.RIGHT, fill=tk.Y)

    def export_balance_data(self):
        """تصدير بيانات الرصيد إلى ملف Excel جديد (للتوافق مع النسخة القديمة)"""
        self.export_balance_data_external()

    def export_balance_data_external(self):
        """تصدير بيانات الرصيد إلى ملف Excel خارج المنظومة"""
        try:
            from tkinter import filedialog
            from datetime import datetime

            # اختيار مكان الحفظ
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"تقرير_أرصدة_الإجازات_{timestamp}.xlsx"

            export_file = filedialog.asksaveasfilename(
                title="اختر مكان حفظ تقرير أرصدة الإجازات",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialfilename=default_filename
            )

            if not export_file:
                return  # المستخدم ألغى العملية

            wb = Workbook()
            ws = wb.active
            ws.title = "تقرير_أرصدة_الإجازات"

            # العناوين المحسنة مع التواريخ
            headers = [
                "الرقم الوظيفي", "اسم الموظف", "مكان العمل", "سنوات الخدمة",
                "الرصيد التلقائي", "الرصيد اليدوي", "تاريخ بداية الرصيد اليدوي",
                "تاريخ انتهاء الرصيد اليدوي", "إجمالي الرصيد", "الإجازات المأخوذة",
                "الرصيد المتبقي", "حالة الرصيد اليدوي", "تاريخ التقرير"
            ]
            ws.append(headers)

            # إضافة البيانات
            report_date = datetime.now().strftime("%Y-%m-%d")
            current_date = datetime.now().date()

            for emp in self.employees_data:
                emp_id = emp.get("الرقم الوظيفي", "")
                emp_name = emp.get("الاسم العربي", "")
                work_place = emp.get("مكان العمل الحالي", "")
                service_years = self.calculate_service_years(emp.get("تاريخ أول مباشرة", ""))
                job_title = emp.get("المسمى الوظيفي", "") or emp.get("الوظيفة", "") or emp.get("المنصب", "")
                auto_balance = self.calculate_automatic_balance(service_years, job_title)
                manual_balance = self.get_manual_balance(emp_id)

                # الحصول على تواريخ الرصيد اليدوي
                start_date, end_date = self.get_manual_balance_dates(emp_id)

                # تحديد حالة الرصيد اليدوي
                balance_status = "لا يوجد"
                if manual_balance > 0:
                    if start_date and end_date:
                        try:
                            end_dt = datetime.strptime(end_date, "%Y-%m-%d").date()
                            days_remaining = (end_dt - current_date).days

                            if days_remaining < 0:
                                balance_status = "منتهي"
                            elif days_remaining <= 30:
                                balance_status = f"ينتهي خلال {days_remaining} يوم"
                            else:
                                balance_status = "نشط"
                        except:
                            balance_status = "نشط"
                    else:
                        balance_status = "نشط (بدون تاريخ)"

                total_balance = auto_balance + manual_balance
                used_leaves = self.calculate_used_leaves(emp_id)
                remaining_balance = total_balance - used_leaves

                ws.append([
                    emp_id, emp_name, work_place, service_years,
                    auto_balance, manual_balance, start_date, end_date,
                    total_balance, used_leaves, remaining_balance,
                    balance_status, report_date
                ])

            # تنسيق الجدول
            from openpyxl.styles import Font, PatternFill, Alignment

            # تنسيق العناوين
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

            for cell in ws[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal="center", vertical="center")

            # ضبط عرض الأعمدة
            column_widths = [12, 20, 15, 12, 12, 12, 18, 18, 12, 15, 12, 20, 15]
            for i, width in enumerate(column_widths, 1):
                ws.column_dimensions[ws.cell(row=1, column=i).column_letter].width = width

            wb.save(export_file)

            # عرض رسالة نجاح مع معلومات إضافية
            total_employees = len(self.employees_data)
            employees_with_manual = sum(1 for emp in self.employees_data if self.get_manual_balance(emp.get("الرقم الوظيفي", "")) > 0)

            success_message = f"""تم تصدير البيانات بنجاح!

📁 الملف: {export_file}
👥 إجمالي الموظفين: {total_employees}
💰 موظفين برصيد يدوي: {employees_with_manual}
📅 تاريخ التقرير: {report_date}

✅ تم حفظ التقرير خارج ملفات المنظومة"""

            messagebox.showinfo("نجاح التصدير", success_message)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def check_manual_balance_alerts(self):
        """فحص تنبيهات انتهاء الرصيد اليدوي"""
        try:
            from datetime import datetime, timedelta

            current_date = datetime.now().date()
            alert_period = current_date + timedelta(days=30)  # تنبيه قبل 30 يوم

            expiring_balances = []
            expired_balances = []

            for emp in self.employees_data:
                emp_id = emp.get("الرقم الوظيفي", "")
                emp_name = emp.get("الاسم العربي", "")
                manual_balance = self.get_manual_balance(emp_id)

                if manual_balance > 0:
                    start_date, end_date = self.get_manual_balance_dates(emp_id)

                    if end_date:
                        try:
                            end_dt = datetime.strptime(end_date, "%Y-%m-%d").date()

                            if end_dt < current_date:
                                # رصيد منتهي
                                days_expired = (current_date - end_dt).days
                                expired_balances.append({
                                    "emp_id": emp_id,
                                    "emp_name": emp_name,
                                    "balance": manual_balance,
                                    "end_date": end_date,
                                    "days_expired": days_expired
                                })
                            elif end_dt <= alert_period:
                                # رصيد سينتهي قريباً
                                days_remaining = (end_dt - current_date).days
                                expiring_balances.append({
                                    "emp_id": emp_id,
                                    "emp_name": emp_name,
                                    "balance": manual_balance,
                                    "end_date": end_date,
                                    "days_remaining": days_remaining
                                })
                        except ValueError:
                            continue

            return expiring_balances, expired_balances

        except Exception as e:
            print(f"خطأ في فحص تنبيهات الرصيد اليدوي: {e}")
            return [], []

    def show_balance_alerts(self):
        """عرض تنبيهات الرصيد اليدوي"""
        expiring, expired = self.check_manual_balance_alerts()

        if not expiring and not expired:
            messagebox.showinfo("تنبيهات الرصيد", "✅ لا توجد تنبيهات للرصيد اليدوي")
            return

        # إنشاء نافذة التنبيهات
        alerts_window = tk.Toplevel(self.root)
        alerts_window.title("تنبيهات الرصيد اليدوي")
        alerts_window.geometry("800x600")
        alerts_window.configure(bg="#f0f0f0")
        alerts_window.resizable(True, True)

        # جعل النافذة في المقدمة
        alerts_window.transient(self.root)

        # إطار رئيسي
        main_frame = tk.Frame(alerts_window, bg="#f0f0f0", padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان
        title_label = tk.Label(main_frame, text="⚠️ تنبيهات الرصيد اليدوي",
                              font=("Arial", 16, "bold"), fg="#e74c3c", bg="#f0f0f0")
        title_label.pack(pady=(0, 20))

        # إطار الرصيد المنتهي
        if expired:
            expired_frame = tk.LabelFrame(main_frame, text=f"❌ رصيد منتهي ({len(expired)} موظف)",
                                         font=("Arial", 12, "bold"), fg="#e74c3c",
                                         padx=15, pady=15, bg="#ffffff")
            expired_frame.pack(fill=tk.X, pady=(0, 15))

            for balance in expired[:5]:  # عرض أول 5 فقط
                info_text = f"• {balance['emp_name']} ({balance['emp_id']}) - {balance['balance']} يوم - انتهى منذ {balance['days_expired']} يوم"
                tk.Label(expired_frame, text=info_text, font=("Arial", 10),
                        fg="#e74c3c", bg="#ffffff", anchor="w").pack(fill=tk.X, pady=2)

        # إطار الرصيد المنتهي قريباً
        if expiring:
            expiring_frame = tk.LabelFrame(main_frame, text=f"⚠️ رصيد ينتهي قريباً ({len(expiring)} موظف)",
                                          font=("Arial", 12, "bold"), fg="#f39c12",
                                          padx=15, pady=15, bg="#ffffff")
            expiring_frame.pack(fill=tk.X, pady=(0, 15))

            for balance in expiring[:5]:  # عرض أول 5 فقط
                info_text = f"• {balance['emp_name']} ({balance['emp_id']}) - {balance['balance']} يوم - ينتهي خلال {balance['days_remaining']} يوم"
                tk.Label(expiring_frame, text=info_text, font=("Arial", 10),
                        fg="#f39c12", bg="#ffffff", anchor="w").pack(fill=tk.X, pady=2)

        # زر إغلاق
        tk.Button(main_frame, text="❌ إغلاق", command=alerts_window.destroy,
                 font=("Arial", 12, "bold"), bg="#95a5a6", fg="white",
                 relief=tk.RAISED, bd=3, padx=20, pady=10).pack(pady=20)

    def deduct_leave_from_balance(self, emp_id, days):
        """خصم أيام الإجازة من رصيد الموظف (يستدعى من نظام الإجازات)"""
        try:
            # إعادة تحميل البيانات للتأكد من الحداثة
            self.employees_data = self.load_employees_data()
            self.leaves_data = self.load_leaves_data()
            self.balance_data = self.load_balance_data()

            # حساب الرصيد الحالي
            emp_data = None
            for emp in self.employees_data:
                if str(emp.get("الرقم الوظيفي", "")) == str(emp_id):
                    emp_data = emp
                    break

            if not emp_data:
                return False, "الموظف غير موجود"

            service_years = self.calculate_service_years(emp_data.get("تاريخ أول مباشرة", ""))
            job_title = emp_data.get("المسمى الوظيفي", "") or emp_data.get("الوظيفة", "") or emp_data.get("المنصب", "")
            auto_balance = self.calculate_automatic_balance(service_years, job_title)
            manual_balance = self.get_manual_balance(emp_id)
            total_balance = auto_balance + manual_balance
            used_leaves = self.calculate_used_leaves(emp_id)
            remaining_balance = total_balance - used_leaves

            # التحقق من كفاية الرصيد
            if remaining_balance < days:
                return False, f"الرصيد المتبقي ({remaining_balance} يوم) غير كافي للإجازة المطلوبة ({days} يوم)"

            # الخصم تم بالفعل عند إضافة الإجازة إلى نظام الإجازات
            # هنا نحدث فقط ملف الرصيد
            self.save_balance_data()

            return True, f"تم خصم {days} يوم من رصيد الموظف. الرصيد المتبقي: {remaining_balance - days} يوم"

        except Exception as e:
            return False, f"خطأ في خصم الرصيد: {str(e)}"

    def close_window(self):
        """إغلاق نافذة نظام رصيد الإجازات"""
        try:
            if messagebox.askyesno("إغلاق النظام", "هل أنت متأكد من إغلاق نظام رصيد الإجازات؟"):
                self.root.destroy()
        except Exception as e:
            print(f"خطأ في إغلاق نظام رصيد الإجازات: {e}")
            self.root.destroy()

# دالة مساعدة للاستدعاء من النظام الرئيسي
def open_leave_balance_system():
    """فتح نظام إدارة رصيد الإجازات"""
    root = tk.Tk()
    app = LeaveBalanceSystem(root)
    root.mainloop()

if __name__ == "__main__":
    open_leave_balance_system()
