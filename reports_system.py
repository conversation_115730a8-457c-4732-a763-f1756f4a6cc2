import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
from datetime import datetime
from reference_data import get_bank_names, get_nationalities, get_qualifications, get_work_places

try:
    from openpyxl import load_workbook
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    messagebox.showerror("خطأ", "مكتبة openpyxl غير مثبتة\nيرجى تثبيتها باستخدام: pip install openpyxl")



try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("تحذير: مكتبة pandas غير مثبتة - سيتم استخدام معالجة بيانات أساسية")

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    print("تحذير: مكتبة python-docx غير مثبتة - لن تتمكن من إنشاء تقارير Word")

class ReportsSystem:
    def __init__(self, root, employees_data_file="employees_data.xlsx", leaves_data_file="leaves_data.xlsx"):
        self.root = root
        self.root.title("نظام التقارير والإحصائيات")
        self.root.geometry("1200x800")
        
        # ملفات البيانات
        self.employees_data_file = employees_data_file
        self.leaves_data_file = leaves_data_file
        
        # تحميل البيانات
        self.employees_data = self.load_employees_data()
        self.leaves_data = self.load_leaves_data()

        # تحويل البيانات إلى DataFrame لسهولة التحليل
        if PANDAS_AVAILABLE:
            self.employees_df = pd.DataFrame(self.employees_data)
            self.leaves_df = pd.DataFrame(self.leaves_data)
        else:
            # استخدام معالجة بيانات أساسية
            self.employees_df = self.employees_data
            self.leaves_df = self.leaves_data
        
        # إنشاء واجهة المستخدم
        self.create_ui()
    
    def load_employees_data(self):
        """تحميل بيانات الموظفين من ملف Excel"""
        try:
            wb = load_workbook(self.employees_data_file)
            # محاولة العثور على الورقة بأسماء مختلفة
            if "الموظفين" in wb.sheetnames:
                ws = wb["الموظفين"]
            elif "Employees" in wb.sheetnames:
                ws = wb["Employees"]
            else:
                ws = wb.active
            
            data = []
            headers = [cell.value for cell in ws[1]]
            
            for row in ws.iter_rows(min_row=2, values_only=True):
                if any(row):
                    data.append(dict(zip(headers, row)))
            
            return data
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل بيانات الموظفين: {str(e)}")
            return []
    
    def load_leaves_data(self):
        """تحميل بيانات الإجازات من ملف Excel"""
        try:
            wb = load_workbook(self.leaves_data_file)
            # محاولة العثور على الورقة بأسماء مختلفة
            if "الإجازات" in wb.sheetnames:
                ws = wb["الإجازات"]
            elif "Leaves" in wb.sheetnames:
                ws = wb["Leaves"]
            else:
                ws = wb.active
            
            data = []
            headers = [cell.value for cell in ws[1]]
            
            for row in ws.iter_rows(min_row=2, values_only=True):
                if any(row):
                    data.append(dict(zip(headers, row)))
            
            return data
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل بيانات الإجازات: {str(e)}")
            return []

    def get_filter_options(self):
        """الحصول على خيارات التصفية من البيانات الفعلية"""
        filter_options = ["الكل"]

        if self.employees_data:
            # فحص الأعمدة المتاحة في البيانات
            sample_employee = self.employees_data[0]
            available_columns = list(sample_employee.keys())

            # إضافة خيارات التصفية المتاحة
            if "الجنسية" in available_columns:
                filter_options.append("الجنسية")
            if "مكان العمل الحالي" in available_columns:
                filter_options.append("مكان العمل")
            if "المسمى الوظيفي" in available_columns:
                filter_options.append("المسمى الوظيفي")
            if "الدرجة الحالية" in available_columns:
                filter_options.append("الدرجة")
            if "المؤهل" in available_columns:
                filter_options.append("المؤهل")
            if "الحالة الاجتماعية" in available_columns:
                filter_options.append("الحالة الاجتماعية")

        return filter_options

    def get_unique_values_for_column(self, column_name):
        """الحصول على القيم الفريدة لعمود معين"""
        unique_values = set()

        for employee in self.employees_data:
            value = employee.get(column_name)
            if value and str(value).strip():
                unique_values.add(str(value).strip())

        return sorted(list(unique_values))
    
    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        # إطار رئيسي
        main_frame = tk.Frame(self.root, padx=10, pady=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # إطار التحكم
        control_frame = tk.LabelFrame(main_frame, text="إعدادات التقرير", padx=5, pady=5)
        control_frame.pack(fill=tk.X, pady=5)
        
        # نوع التقرير
        tk.Label(control_frame, text="نوع التقرير:").grid(row=0, column=0, padx=5, sticky="e")
        
        self.report_type = ttk.Combobox(
            control_frame,
            values=[
                "الموظفين"
            ],
            state="readonly",
            width=40
        )
        self.report_type.grid(row=0, column=1, padx=5, sticky="w")
        self.report_type.current(0)
        self.report_type.bind("<<ComboboxSelected>>", self.update_report)
        
        # خيارات التصفية - تحديث القيم من بيانات Excel الفعلية
        tk.Label(control_frame, text="تصفية حسب:").grid(row=1, column=0, padx=5, sticky="e")

        # الحصول على قيم التصفية من البيانات الفعلية
        filter_options = self.get_filter_options()

        self.filter_by = ttk.Combobox(
            control_frame,
            values=filter_options,
            state="readonly",
            width=15
        )
        self.filter_by.grid(row=1, column=1, padx=5, sticky="w")
        self.filter_by.current(0)

        self.filter_value = ttk.Combobox(control_frame, state="readonly", width=20)
        self.filter_value.grid(row=1, column=2, padx=5, sticky="w")

        self.filter_by.bind("<<ComboboxSelected>>", self.update_filter_values)
        
        # زر تطبيق التقرير
        apply_btn = tk.Button(control_frame, text="تطبيق", command=self.update_report)
        apply_btn.grid(row=1, column=3, padx=5)
        
        # إطار عرض التقرير
        report_frame = tk.LabelFrame(main_frame, text="عرض التقرير", padx=5, pady=5)
        report_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # إنشاء عناصر عرض التقرير
        self.create_report_display(report_frame)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=5)

        # زر التصدير الخارجي
        export_btn = tk.Button(buttons_frame, text="📊 تصدير خارج النظام",
                              command=self.export_report_external,
                              bg="#27ae60", fg="white", font=("Arial", 10, "bold"))
        export_btn.pack(side=tk.LEFT, padx=5)

        print_btn = tk.Button(buttons_frame, text="طباعة التقرير", command=self.print_report)
        print_btn.pack(side=tk.RIGHT, padx=5)
    
    def create_report_display(self, parent):
        """إنشاء عناصر عرض التقرير (جدول البيانات)"""
        # إطار لعرض الجدول
        table_frame = tk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # شريط التمرير للجدول
        scroll_y = tk.Scrollbar(table_frame, orient=tk.VERTICAL)
        scroll_x = tk.Scrollbar(table_frame, orient=tk.HORIZONTAL)
        
        # إنشاء الجدول
        self.report_table = ttk.Treeview(
            table_frame, 
            yscrollcommand=scroll_y.set, 
            xscrollcommand=scroll_x.set,
            selectmode="browse"
        )
        
        scroll_y.config(command=self.report_table.yview)
        scroll_x.config(command=self.report_table.xview)
        
        # وضع الجدول وشريط التمرير في الواجهة
        self.report_table.grid(row=0, column=0, sticky="nsew")
        scroll_y.grid(row=0, column=1, sticky="ns")
        scroll_x.grid(row=1, column=0, sticky="ew")
        
        # جعل الجدول قابل للتوسع
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
    
    def update_filter_values(self, event=None):
        """تحديث قيم التصفية بناءً على نوع التصفية المحدد"""
        filter_type = self.filter_by.get()
        self.filter_value["values"] = []

        if filter_type == "الكل":
            return

        # تحديد اسم العمود المقابل
        column_mapping = {
            "الجنسية": "الجنسية",
            "مكان العمل": "مكان العمل الحالي",
            "المسمى الوظيفي": "المسمى الوظيفي",
            "الدرجة": "الدرجة الحالية",
            "المؤهل": "المؤهل",
            "الحالة الاجتماعية": "الحالة الاجتماعية"
        }

        column_name = column_mapping.get(filter_type)
        if column_name:
            if PANDAS_AVAILABLE and hasattr(self, 'employees_df'):
                # استخدام pandas إذا كان متاحاً
                unique_values = self.employees_df[column_name].dropna().unique()
                self.filter_value["values"] = sorted([str(v) for v in unique_values])
            else:
                # استخدام الطريقة الأساسية
                self.filter_value["values"] = self.get_unique_values_for_column(column_name)

        if self.filter_value["values"]:
            self.filter_value.current(0)
    
    def update_report(self, event=None):
        """تحديث التقرير بناءً على الإعدادات المحددة"""
        report_type = self.report_type.get()
        filter_type = self.filter_by.get()
        filter_value = self.filter_value.get() if filter_type != "الكل" else None

        # تطبيق التصفية إذا كانت محددة
        if PANDAS_AVAILABLE:
            employees_df = self.employees_df.copy()

            if filter_value:
                # تحديد اسم العمود المقابل
                column_mapping = {
                    "الجنسية": "الجنسية",
                    "مكان العمل": "مكان العمل الحالي",
                    "المسمى الوظيفي": "المسمى الوظيفي",
                    "الدرجة": "الدرجة الحالية",
                    "المؤهل": "المؤهل",
                    "الحالة الاجتماعية": "الحالة الاجتماعية"
                }

                column_name = column_mapping.get(filter_type)
                if column_name and column_name in employees_df.columns:
                    employees_df = employees_df[employees_df[column_name] == filter_value]
        else:
            # معالجة بيانات أساسية بدون pandas
            employees_data = self.employees_data.copy()

            if filter_value:
                column_mapping = {
                    "الجنسية": "الجنسية",
                    "مكان العمل": "مكان العمل الحالي",
                    "المسمى الوظيفي": "المسمى الوظيفي",
                    "الدرجة": "الدرجة الحالية",
                    "المؤهل": "المؤهل",
                    "الحالة الاجتماعية": "الحالة الاجتماعية"
                }

                column_name = column_mapping.get(filter_type)
                if column_name:
                    employees_data = [emp for emp in employees_data if emp.get(column_name) == filter_value]

            # تحويل إلى DataFrame للمعالجة الموحدة
            employees_df = pd.DataFrame(employees_data) if PANDAS_AVAILABLE else employees_data

        # إنشاء تقرير الموظفين
        if report_type == "الموظفين":
            self.generate_employees_report(employees_df)
    
    def generate_employees_report(self, df):
        """تقرير الموظفين مع إحصائيات حسب التصفية"""
        if PANDAS_AVAILABLE:
            if df.empty:
                messagebox.showwarning("تحذير", "لا توجد بيانات لعرضها بعد التصفية")
                return

            # تحضير بيانات الموظفين للعرض
            display_columns = [
                "الاسم العربي", "الرقم الوظيفي", "الجنسية",
                "مكان العمل الحالي", "المسمى الوظيفي", "الدرجة الحالية",
                "تاريخ التعيين", "المؤهل"
            ]

            # التأكد من وجود الأعمدة في البيانات
            available_columns = [col for col in display_columns if col in df.columns]
            employees_data = df[available_columns].copy()

            # عرض البيانات في الجدول
            self.display_data_in_table(employees_data)
        else:
            # معالجة بيانات أساسية
            if not df:
                messagebox.showwarning("تحذير", "لا توجد بيانات لعرضها بعد التصفية")
                return

            # تحضير بيانات الموظفين للعرض
            display_columns = [
                "الاسم العربي", "الرقم الوظيفي", "الجنسية",
                "مكان العمل الحالي", "المسمى الوظيفي", "الدرجة الحالية",
                "تاريخ التعيين", "المؤهل"
            ]

            # إنشاء بيانات منظمة للعرض
            organized_data = []
            for employee in df:
                row_data = {}
                for col in display_columns:
                    if col in employee:
                        row_data[col] = employee[col]
                organized_data.append(row_data)

            # عرض البيانات في الجدول
            self.display_basic_data_in_table(organized_data, display_columns)


    
    def display_data_in_table(self, data):
        """عرض البيانات في الجدول (مع pandas)"""
        # مسح الجدول الحالي
        for item in self.report_table.get_children():
            self.report_table.delete(item)

        # تحديد أعمدة الجدول
        columns = list(data.columns)
        self.report_table["columns"] = columns

        # تنسيق الأعمدة
        self.report_table.column("#0", width=0, stretch=tk.NO)
        for col in columns:
            self.report_table.column(col, width=150, anchor="center")
            self.report_table.heading(col, text=col, anchor="center")

        # إضافة البيانات إلى الجدول
        for _, row in data.iterrows():
            self.report_table.insert("", tk.END, values=list(row))

    def display_basic_data_in_table(self, data, columns):
        """عرض البيانات في الجدول (بدون pandas)"""
        # مسح الجدول الحالي
        for item in self.report_table.get_children():
            self.report_table.delete(item)

        # تحديد أعمدة الجدول
        self.report_table["columns"] = columns

        # تنسيق الأعمدة
        self.report_table.column("#0", width=0, stretch=tk.NO)
        for col in columns:
            self.report_table.column(col, width=150, anchor="center")
            self.report_table.heading(col, text=col, anchor="center")

        # إضافة البيانات إلى الجدول
        for row_data in data:
            values = [str(row_data.get(col, "")) for col in columns]
            self.report_table.insert("", tk.END, values=values)

    def export_report_external(self):
        """تصدير التقرير إلى ملف Excel خارج النظام"""
        try:
            # التحقق من وجود بيانات للتصدير
            if not self.report_table.get_children():
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return

            # إعداد اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_type = self.report_type.get()
            filter_type = self.filter_by.get()
            filter_value = self.filter_value.get()

            # تحديد اسم الملف حسب التصفية
            if filter_type != "الكل" and filter_value:
                filename = f"تقرير_{report_type}_{filter_type}_{filter_value}_{timestamp}.xlsx"
            else:
                filename = f"تقرير_{report_type}_شامل_{timestamp}.xlsx"

            # اختيار مكان الحفظ خارج النظام
            export_file = filedialog.asksaveasfilename(
                title="اختر مكان حفظ التقرير (خارج النظام)",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialfile=filename,
                initialdir=os.path.expanduser("~/Desktop")  # البدء من سطح المكتب
            )

            if not export_file:
                return  # المستخدم ألغى العملية

            # التأكد من أن المسار خارج مجلد النظام
            current_dir = os.path.dirname(os.path.abspath(__file__))
            if os.path.commonpath([export_file, current_dir]) == current_dir:
                # إذا كان المسار داخل مجلد النظام، اقترح مكان آخر
                suggested_path = os.path.join(os.path.expanduser("~/Desktop"), filename)
                if messagebox.askyesno("تحذير",
                    f"المسار المحدد داخل مجلد النظام.\nهل تريد الحفظ في سطح المكتب بدلاً من ذلك؟\n\n{suggested_path}"):
                    export_file = suggested_path
                else:
                    return

            # إنشاء ملف Excel محسن
            from openpyxl import Workbook
            wb = Workbook()
            ws = wb.active
            ws.title = f"تقرير_{report_type}"

            # الحصول على الأعمدة والبيانات من الجدول
            columns = self.report_table["columns"]

            # إضافة معلومات التقرير في الأعلى
            report_info = [
                ["تقرير النظام", ""],
                ["نوع التقرير", report_type],
                ["تاريخ التقرير", datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
                ["التصفية", f"{filter_type}: {filter_value}" if filter_type != "الكل" else "بدون تصفية"],
                ["عدد السجلات", str(len(self.report_table.get_children()))],
                ["", ""],  # سطر فارغ
            ]

            for info_row in report_info:
                ws.append(info_row)

            # إضافة العناوين
            ws.append(list(columns))

            # إضافة البيانات
            for item in self.report_table.get_children():
                values = self.report_table.item(item)["values"]
                ws.append(list(values))

            # تنسيق الجدول
            try:
                from openpyxl.styles import Font, PatternFill, Alignment

                # تنسيق معلومات التقرير
                for row in range(1, 6):
                    ws.cell(row=row, column=1).font = Font(bold=True)
                    ws.cell(row=row, column=1).fill = PatternFill(start_color="E3F2FD", end_color="E3F2FD", fill_type="solid")

                # تنسيق العناوين
                header_row = 7  # الصف الذي يحتوي على العناوين
                header_font = Font(bold=True, color="FFFFFF")
                header_fill = PatternFill(start_color="1976D2", end_color="1976D2", fill_type="solid")

                for col in range(1, len(columns) + 1):
                    cell = ws.cell(row=header_row, column=col)
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = Alignment(horizontal="center", vertical="center")

                # ضبط عرض الأعمدة
                for column in ws.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 30)
                    ws.column_dimensions[column_letter].width = adjusted_width

            except ImportError:
                print("   ⚠️ تنسيق الجدول غير متاح (openpyxl.styles)")

            # حفظ الملف
            wb.save(export_file)

            # عرض رسالة نجاح مفصلة
            file_size = os.path.getsize(export_file)
            total_records = len(self.report_table.get_children())

            success_message = f"""تم تصدير التقرير بنجاح!

📁 الملف: {os.path.basename(export_file)}
📂 المجلد: {os.path.dirname(export_file)}
📊 نوع التقرير: {report_type}
🔍 التصفية: {filter_type}: {filter_value if filter_type != 'الكل' else 'بدون تصفية'}
📋 عدد السجلات: {total_records}
📊 عدد الأعمدة: {len(columns)}
📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
💾 حجم الملف: {file_size:,} بايت

✅ تم حفظ التقرير خارج ملفات النظام"""

            messagebox.showinfo("نجح التصدير", success_message)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")
    

    
    def print_report(self):
        """طباعة التقرير الحالي"""
        # التحقق من وجود بيانات للطباعة
        if not self.report_table.get_children():
            messagebox.showwarning("تحذير", "لا توجد بيانات للطباعة")
            return

        # إنشاء نافذة خيارات الطباعة
        self.show_print_options()

    def show_print_options(self):
        """عرض خيارات طباعة التقرير"""
        print_window = tk.Toplevel(self.root)
        print_window.title("طباعة التقرير")
        print_window.geometry("600x500")
        print_window.grab_set()

        # إطار الخيارات
        options_frame = tk.LabelFrame(print_window, text="خيارات الطباعة", padx=10, pady=10)
        options_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # نوع التقرير
        report_type = self.report_type.get()
        tk.Label(options_frame, text=f"التقرير: {report_type}",
                font=("Arial", 12, "bold")).pack(anchor="w", pady=5)

        # نوع الملف
        tk.Label(options_frame, text="تنسيق الطباعة:", font=("Arial", 12)).pack(anchor="w", pady=5)

        self.print_format_var = tk.StringVar(master=print_window, value="word")

        formats = [
            ("Word", "word", "مستند Word للطباعة"),
            ("PDF", "pdf", "ملف PDF للطباعة"),
            ("كلاهما", "both", "إنشاء كلا التنسيقين")
        ]

        for text, value, desc in formats:
            frame = tk.Frame(options_frame)
            frame.pack(fill=tk.X, pady=2)

            rb = tk.Radiobutton(frame, text=text, variable=self.print_format_var, value=value)
            rb.pack(side=tk.LEFT)

            tk.Label(frame, text=f"- {desc}", font=("Arial", 9), fg="gray").pack(side=tk.LEFT, padx=(10, 0))

        # خيارات التقرير
        tk.Label(options_frame, text="خيارات التقرير:", font=("Arial", 12)).pack(anchor="w", pady=(20, 5))

        self.include_summary_var = tk.BooleanVar(master=print_window, value=True)
        tk.Checkbutton(options_frame, text="تضمين ملخص إحصائي",
                      variable=self.include_summary_var).pack(anchor="w")

        self.include_date_var = tk.BooleanVar(master=print_window, value=True)
        tk.Checkbutton(options_frame, text="تضمين تاريخ ووقت التقرير",
                      variable=self.include_date_var).pack(anchor="w")

        # خيارات الطباعة
        tk.Label(options_frame, text="خيارات الطباعة:", font=("Arial", 12)).pack(anchor="w", pady=(20, 5))

        self.auto_open_var = tk.BooleanVar(master=print_window, value=True)
        tk.Checkbutton(options_frame, text="فتح الملف بعد الإنشاء",
                      variable=self.auto_open_var).pack(anchor="w")

        self.auto_print_var = tk.BooleanVar(master=print_window, value=False)
        tk.Checkbutton(options_frame, text="طباعة مباشرة",
                      variable=self.auto_print_var).pack(anchor="w")

        # ملاحظة للمستخدم
        note_label = tk.Label(options_frame, text="ملاحظة: التقارير للعرض والطباعة فقط (لا يتم حفظها)",
                             font=("Arial", 9), fg="gray")
        note_label.pack(anchor="w", pady=(5, 0))

        # معاينة إحصائيات التقرير
        stats_frame = tk.LabelFrame(options_frame, text="إحصائيات التقرير", padx=5, pady=5)
        stats_frame.pack(fill=tk.X, pady=10)

        # حساب الإحصائيات
        total_records = len(self.report_table.get_children())
        columns_count = len(self.report_table["columns"])

        stats_text = f"عدد السجلات: {total_records}\nعدد الأعمدة: {columns_count}\nنوع التقرير: {report_type}"

        tk.Label(stats_frame, text=stats_text, font=("Arial", 10), justify=tk.LEFT).pack(anchor="w")

        # أزرار التحكم
        buttons_frame = tk.Frame(print_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        create_btn = tk.Button(buttons_frame, text="إنشاء وطباعة التقرير",
                              command=lambda: self.process_report_print(print_window),
                              bg="#2196F3", fg="white", font=("Arial", 11))
        create_btn.pack(side=tk.RIGHT, padx=5)

        cancel_btn = tk.Button(buttons_frame, text="إلغاء", command=print_window.destroy)
        cancel_btn.pack(side=tk.LEFT, padx=5)

    def process_report_print(self, window):
        """معالجة طباعة التقرير"""
        try:
            from printing_system import PrintingSystem

            printer = PrintingSystem()
            print_format = self.print_format_var.get()
            created_files = []

            # جمع بيانات التقرير
            report_data = []
            for item in self.report_table.get_children():
                values = self.report_table.item(item)["values"]
                columns = self.report_table["columns"]
                record = dict(zip(columns, values))
                report_data.append(record)

            # إنشاء الملفات حسب التنسيق المحدد
            if print_format in ["word", "both"]:
                word_file = self.create_word_report(report_data)
                if word_file:
                    created_files.append(("Word", word_file))

            if print_format in ["pdf", "both"]:
                pdf_file = self.create_pdf_report(report_data)
                if pdf_file:
                    created_files.append(("PDF", pdf_file))

            if not created_files:
                messagebox.showerror("خطأ", "فشل في إنشاء ملفات التقرير")
                return

            # معالجة الخيارات - عرض وطباعة فقط (بدون حفظ)
            for file_format, filepath in created_files:
                print(f"ℹ️ تم إنشاء تقرير {file_format} للعرض والطباعة: {os.path.basename(filepath)}")

                # فتح الملف للعرض
                if self.auto_open_var.get():
                    printer.open_file(filepath)

                # طباعة مباشرة إذا كان مطلوباً
                if self.auto_print_var.get():
                    printer.print_file(filepath)

            # حذف الملفات المؤقتة بعد المعالجة
            import threading
            def delayed_cleanup():
                import time
                time.sleep(3)  # انتظار لضمان انتهاء العمليات
                for file_format, filepath in created_files:
                    try:
                        if os.path.exists(filepath):
                            os.remove(filepath)
                            print(f"🗑️ تم حذف ملف التقرير المؤقت: {os.path.basename(filepath)}")
                    except:
                        pass
                # تنظيف مجلدات التقارير المؤقتة
                self.cleanup_temp_files()

            threading.Thread(target=delayed_cleanup, daemon=True).start()

            # إغلاق النافذة
            window.destroy()

            # رسالة نجاح
            files_info = ", ".join([f[0] for f in created_files])
            messagebox.showinfo("نجاح", f"تم إنشاء التقرير بنجاح: {files_info}")

        except ImportError:
            messagebox.showerror("خطأ", "نظام الطباعة غير متاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء طباعة التقرير: {str(e)}")

    def create_word_report(self, report_data):
        """إنشاء تقرير Word مؤقت"""
        try:
            if not DOCX_AVAILABLE:
                messagebox.showerror("خطأ", "مكتبة python-docx غير مثبتة\nيرجى تثبيتها باستخدام: pip install python-docx")
                return None

            import tempfile
            from datetime import datetime
            from docx import Document
            from docx.shared import Inches
            from docx.enum.text import WD_ALIGN_PARAGRAPH
            from docx.oxml.ns import qn
            from docx.oxml import OxmlElement

            # إنشاء ملف مؤقت
            temp_dir = tempfile.mkdtemp()
            filename = f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
            filepath = os.path.join(temp_dir, filename)

            # تسجيل المجلد المؤقت للحذف لاحقاً
            self.temp_dirs = getattr(self, 'temp_dirs', [])
            self.temp_dirs.append(temp_dir)

            # إنشاء مستند Word
            doc = Document()

            # إعداد اتجاه النص للعربية
            sections = doc.sections
            for section in sections:
                sectPr = section._sectPr
                bidi = OxmlElement('w:bidi')
                sectPr.append(bidi)

            # عنوان التقرير
            title = doc.add_heading('تقرير الموظفين', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # تاريخ التقرير
            date_para = doc.add_paragraph(f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M")}')
            date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

            if report_data:
                # إضافة جدول
                table = doc.add_table(rows=1, cols=len(report_data[0]))
                table.style = 'Table Grid'

                # إضافة العناوين
                headers = list(report_data[0].keys())
                hdr_cells = table.rows[0].cells
                for i, header in enumerate(headers):
                    hdr_cells[i].text = header
                    # تنسيق العناوين
                    for paragraph in hdr_cells[i].paragraphs:
                        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
                        run.bold = True

                # إضافة البيانات
                for record in report_data:
                    row_cells = table.add_row().cells
                    for i, value in enumerate(record.values()):
                        row_cells[i].text = str(value) if value is not None else ""
                        # توسيط النص
                        for paragraph in row_cells[i].paragraphs:
                            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

                # تعديل عرض الأعمدة
                for row in table.rows:
                    for cell in row.cells:
                        cell.width = Inches(1.2)

            else:
                doc.add_paragraph('لا توجد بيانات متاحة.')

            # إضافة فقرة ختامية
            doc.add_paragraph()
            footer = doc.add_paragraph('تم إنشاء هذا التقرير بواسطة نظام إدارة الموارد البشرية')
            footer.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # حفظ الملف
            doc.save(filepath)
            return filepath

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء تقرير Word: {str(e)}")
            return None

    def create_pdf_report(self, report_data):
        """إنشاء تقرير PDF مؤقت"""
        try:
            from printing_system import PrintingSystem

            printer = PrintingSystem()
            report_title = f"تقرير {self.report_type.get()}"

            # استخدام نظام الطباعة لإنشاء PDF
            pdf_file = printer.create_employees_report_pdf(report_data, report_title)

            if pdf_file:
                # تسجيل الملف للحذف لاحقاً
                self.temp_dirs = getattr(self, 'temp_dirs', [])
                temp_dir = os.path.dirname(pdf_file)
                if temp_dir not in self.temp_dirs:
                    self.temp_dirs.append(temp_dir)

            return pdf_file

        except ImportError:
            messagebox.showerror("خطأ", "نظام الطباعة غير متاح لإنشاء ملفات PDF")
            return None
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء تقرير PDF: {str(e)}")
            return None

    def cleanup_temp_files(self):
        """تنظيف جميع الملفات المؤقتة"""
        try:
            import shutil
            temp_dirs = getattr(self, 'temp_dirs', [])
            for temp_dir in temp_dirs:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir, ignore_errors=True)
                    print(f"🗑️ تم حذف المجلد المؤقت: {temp_dir}")
            self.temp_dirs = []
        except Exception as e:
            print(f"⚠️ تعذر حذف الملفات المؤقتة: {e}")

    def __del__(self):
        """تنظيف عند حذف الكائن"""
        try:
            self.cleanup_temp_files()
        except:
            pass

if __name__ == "__main__":
    root = tk.Tk()
    app = ReportsSystem(root)

    # تنظيف الملفات المؤقتة عند إغلاق النافذة
    def on_closing():
        app.cleanup_temp_files()
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()