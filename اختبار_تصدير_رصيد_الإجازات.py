#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تصدير نظام رصيد الإجازات السنوية
Test Leave Balance System Export
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
from datetime import datetime

def test_leave_balance_export():
    """اختبار تصدير نظام رصيد الإجازات"""
    print("🔧 اختبار تصدير نظام رصيد الإجازات السنوية")
    print("=" * 60)
    
    try:
        # استيراد نظام رصيد الإجازات
        print("📦 استيراد نظام رصيد الإجازات...")
        import leave_balance_system
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة الاختبار...")
        root = tk.Tk()
        root.title("اختبار تصدير رصيد الإجازات")
        root.geometry("1200x800")
        
        # إنشاء النظام
        print("🔧 إنشاء نظام رصيد الإجازات...")
        balance_system = leave_balance_system.LeaveBalanceSystem(root, auto_refresh=False)
        print("✅ تم إنشاء النظام بنجاح")
        
        # فحص التصدير
        print("\n🔍 فحص نظام التصدير:")
        
        # فحص وجود الدوال
        export_functions = [
            ("export_balance_data", "دالة التصدير الأساسية"),
            ("export_balance_data_external", "دالة التصدير الخارجي"),
        ]
        
        for func_name, description in export_functions:
            if hasattr(balance_system, func_name):
                print(f"   ✅ {description}: موجودة")
            else:
                print(f"   ❌ {description}: غير موجودة")
        
        # فحص البيانات
        print(f"\n📊 فحص البيانات:")
        print(f"   👥 عدد الموظفين: {len(balance_system.employees_data)}")
        print(f"   🏖️ عدد الإجازات: {len(balance_system.leaves_data)}")
        print(f"   💰 عدد أرصدة الإجازات: {len(balance_system.balance_data)}")
        
        # إنشاء واجهة تفاعلية للاختبار
        create_balance_export_test_interface(root, balance_system)
        
        print("\n🎉 انتهى الاختبار - الواجهة التفاعلية جاهزة")
        print("\n📋 تعليمات الاختبار:")
        print("   • اضغط على زر 'تصدير البيانات' في النظام")
        print("   • اختر مكان الحفظ خارج مجلد النظام")
        print("   • تأكد من نجاح التصدير")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def create_balance_export_test_interface(root, balance_system):
    """إنشاء واجهة تفاعلية لاختبار التصدير"""
    
    # إطار الاختبار
    test_frame = tk.Frame(root, bg="#e3f2fd")
    test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
    
    # عنوان
    title_label = tk.Label(test_frame, text="🔧 اختبار تصدير نظام رصيد الإجازات السنوية", 
                          bg="#e3f2fd", fg="#1565c0", font=("Arial", 14, "bold"))
    title_label.pack(pady=5)
    
    # معلومات النظام
    info_frame = tk.LabelFrame(test_frame, text="معلومات النظام", 
                              bg="#e3f2fd", fg="#1565c0", font=("Arial", 12, "bold"))
    info_frame.pack(fill=tk.X, pady=5)
    
    # إحصائيات
    stats_text = f"""
📊 إحصائيات النظام:
• عدد الموظفين: {len(balance_system.employees_data)}
• عدد الإجازات المسجلة: {len(balance_system.leaves_data)}
• عدد أرصدة الإجازات: {len(balance_system.balance_data)}

🔧 دوال التصدير المتوفرة:
• export_balance_data() - التصدير الأساسي
• export_balance_data_external() - التصدير الخارجي المحسن

📋 الأعمدة في التصدير:
• الرقم الوظيفي، اسم الموظف، مكان العمل
• سنوات الخدمة، الرصيد التلقائي، الرصيد اليدوي
• تاريخ بداية الرصيد اليدوي، تاريخ انتهاء الرصيد اليدوي
• إجمالي الرصيد، الإجازات المأخوذة، الرصيد المتبقي
• حالة الرصيد اليدوي، تاريخ التقرير
    """
    
    stats_label = tk.Label(info_frame, text=stats_text, bg="#e3f2fd", 
                          fg="#1565c0", font=("Arial", 10), justify=tk.LEFT)
    stats_label.pack(padx=10, pady=5)
    
    # متغير لعرض النتائج
    result_var = tk.StringVar()
    result_label = tk.Label(test_frame, textvariable=result_var, bg="#e3f2fd", 
                           fg="#1565c0", font=("Arial", 11, "bold"))
    result_label.pack(pady=5)
    
    # أزرار الاختبار
    buttons_frame = tk.Frame(test_frame, bg="#e3f2fd")
    buttons_frame.pack(pady=10)
    
    def test_export_function():
        """اختبار دالة التصدير"""
        try:
            print("\n📊 اختبار دالة التصدير...")
            
            # فحص وجود الدالة
            if hasattr(balance_system, 'export_balance_data_external'):
                result_var.set("✅ دالة التصدير الخارجي موجودة وجاهزة")
                print("   ✅ دالة التصدير الخارجي موجودة")
                
                # فحص كود الدالة
                import inspect
                source = inspect.getsource(balance_system.export_balance_data_external)
                
                checks = [
                    ("filedialog.asksaveasfilename", "نافذة اختيار مكان الحفظ"),
                    ("خارج النظام", "التحقق من الحفظ خارج النظام"),
                    ("openpyxl", "استخدام مكتبة Excel"),
                    ("تنسيق الجدول", "تنسيق الملف"),
                ]
                
                for check, description in checks:
                    if check in source:
                        print(f"     ✅ {description}: موجود")
                    else:
                        print(f"     ⚠️ {description}: غير موجود")
                
            else:
                result_var.set("❌ دالة التصدير الخارجي غير موجودة")
                print("   ❌ دالة التصدير الخارجي غير موجودة")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار دالة التصدير: {e}")
            result_var.set(f"❌ خطأ: {e}")
    
    def test_data_availability():
        """اختبار توفر البيانات"""
        try:
            print("\n📊 اختبار توفر البيانات...")
            
            # فحص البيانات
            employees_count = len(balance_system.employees_data)
            leaves_count = len(balance_system.leaves_data)
            balance_count = len(balance_system.balance_data)
            
            if employees_count > 0:
                print(f"   ✅ بيانات الموظفين: {employees_count} موظف")
                
                # فحص عينة من البيانات
                sample_emp = balance_system.employees_data[0]
                required_fields = ["الرقم الوظيفي", "الاسم العربي", "مكان العمل الحالي"]
                
                for field in required_fields:
                    if field in sample_emp:
                        print(f"     ✅ حقل '{field}': موجود")
                    else:
                        print(f"     ⚠️ حقل '{field}': غير موجود")
                
                result_var.set(f"✅ البيانات متوفرة: {employees_count} موظف")
            else:
                result_var.set("⚠️ لا توجد بيانات موظفين")
                print("   ⚠️ لا توجد بيانات موظفين")
            
            print(f"   📊 الإجازات: {leaves_count}")
            print(f"   💰 الأرصدة: {balance_count}")
            
        except Exception as e:
            print(f"   ❌ خطأ في فحص البيانات: {e}")
            result_var.set(f"❌ خطأ البيانات: {e}")
    
    def test_manual_export():
        """اختبار التصدير اليدوي"""
        try:
            print("\n🔄 اختبار التصدير اليدوي...")
            
            # محاولة استدعاء دالة التصدير
            if hasattr(balance_system, 'export_balance_data_external'):
                print("   🔄 استدعاء دالة التصدير...")
                
                # تحذير للمستخدم
                if messagebox.askyesno("تأكيد التصدير", 
                    "هل تريد تجربة التصدير الفعلي؟\nسيتم فتح نافذة اختيار مكان الحفظ."):
                    
                    balance_system.export_balance_data_external()
                    result_var.set("✅ تم تشغيل دالة التصدير")
                    print("   ✅ تم تشغيل دالة التصدير")
                else:
                    result_var.set("⏸️ تم إلغاء التصدير بواسطة المستخدم")
                    print("   ⏸️ تم إلغاء التصدير بواسطة المستخدم")
            else:
                result_var.set("❌ دالة التصدير غير متوفرة")
                print("   ❌ دالة التصدير غير متوفرة")
            
        except Exception as e:
            print(f"   ❌ خطأ في التصدير اليدوي: {e}")
            result_var.set(f"❌ خطأ التصدير: {e}")
    
    def test_openpyxl_availability():
        """اختبار توفر مكتبة openpyxl"""
        try:
            print("\n📦 اختبار توفر مكتبة openpyxl...")
            
            try:
                from openpyxl import Workbook
                from openpyxl.styles import Font, PatternFill, Alignment
                
                # إنشاء ملف تجريبي
                wb = Workbook()
                ws = wb.active
                ws.title = "اختبار"
                ws.append(["عمود 1", "عمود 2", "عمود 3"])
                ws.append(["بيانات 1", "بيانات 2", "بيانات 3"])
                
                # تنسيق تجريبي
                header_font = Font(bold=True)
                ws.cell(row=1, column=1).font = header_font
                
                result_var.set("✅ مكتبة openpyxl متوفرة وتعمل بشكل صحيح")
                print("   ✅ مكتبة openpyxl متوفرة وتعمل بشكل صحيح")
                
            except ImportError:
                result_var.set("❌ مكتبة openpyxl غير مثبتة")
                print("   ❌ مكتبة openpyxl غير مثبتة")
                print("   💡 لتثبيتها: pip install openpyxl")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار openpyxl: {e}")
            result_var.set(f"❌ خطأ openpyxl: {e}")
    
    def show_export_guide():
        """عرض دليل التصدير"""
        guide = """
📖 دليل تصدير أرصدة الإجازات:

🎯 الهدف:
   تصدير تقرير شامل لأرصدة الإجازات السنوية لجميع الموظفين

🔧 كيفية الاستخدام:
   1. اضغط على زر "📊 تصدير البيانات" في النظام
   2. اختر مكان الحفظ (خارج مجلد النظام)
   3. انتظر انتهاء عملية التصدير
   4. افتح الملف للمراجعة

📊 محتويات التقرير:
   • البيانات الأساسية للموظفين
   • سنوات الخدمة المحسوبة
   • الرصيد التلقائي (شهر لكل سنة خدمة)
   • الرصيد اليدوي المضاف
   • تواريخ بداية ونهاية الرصيد اليدوي
   • إجمالي الرصيد المتاح
   • الإجازات المأخوذة
   • الرصيد المتبقي
   • حالة الرصيد اليدوي (نشط/منتهي/ينتهي قريباً)

🎨 مميزات التقرير:
   • تنسيق احترافي مع ألوان
   • عناوين واضحة ومنسقة
   • ضبط عرض الأعمدة تلقائياً
   • معلومات إضافية (تاريخ التقرير، إحصائيات)

⚠️ ملاحظات مهمة:
   • يتم الحفظ خارج مجلد النظام فقط
   • يتم التحقق من صحة البيانات قبل التصدير
   • رسالة تأكيد مفصلة عند النجاح
   • معالجة الأخطاء مع رسائل واضحة

🛠️ متطلبات التشغيل:
   • مكتبة openpyxl مثبتة
   • بيانات موظفين متوفرة
   • مساحة كافية في مكان الحفظ
        """
        
        messagebox.showinfo("دليل تصدير أرصدة الإجازات", guide)
        result_var.set("📖 تم عرض دليل التصدير")
    
    # أزرار الاختبار
    tk.Label(buttons_frame, text="اختبارات التصدير:", bg="#e3f2fd", 
            font=("Arial", 12, "bold")).pack()
    
    buttons_row1 = tk.Frame(buttons_frame, bg="#e3f2fd")
    buttons_row1.pack(pady=5)
    
    tk.Button(buttons_row1, text="🔧 اختبار الدالة",
             command=test_export_function,
             bg="#2196f3", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row1, text="📊 اختبار البيانات",
             command=test_data_availability,
             bg="#4caf50", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row1, text="📦 اختبار openpyxl",
             command=test_openpyxl_availability,
             bg="#ff9800", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    buttons_row2 = tk.Frame(buttons_frame, bg="#e3f2fd")
    buttons_row2.pack(pady=5)
    
    tk.Button(buttons_row2, text="🔄 تجربة التصدير",
             command=test_manual_export,
             bg="#9c27b0", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row2, text="📖 دليل التصدير",
             command=show_export_guide,
             bg="#795548", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    # معلومات إضافية
    info_frame = tk.Frame(test_frame, bg="#e3f2fd")
    info_frame.pack(pady=10)
    
    info_text = """
💡 نصائح للاختبار:
• تأكد من وجود بيانات موظفين قبل التصدير
• اختر مكان حفظ خارج مجلد النظام
• راجع محتوى الملف المصدر للتأكد من صحة البيانات
• استخدم التنبيهات لمراقبة الأرصدة المنتهية
    """
    info_label = tk.Label(info_frame, text=info_text, bg="#e3f2fd", 
                         fg="#1565c0", font=("Arial", 10), justify=tk.LEFT)
    info_label.pack()
    
    # زر إغلاق
    tk.Button(test_frame, text="❌ إغلاق الاختبار", 
             command=root.destroy,
             bg="#616161", fg="white", 
             font=("Arial", 12, "bold")).pack(pady=15)

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار تصدير نظام رصيد الإجازات السنوية")
    print("=" * 60)
    
    test_leave_balance_export()

if __name__ == "__main__":
    main()
