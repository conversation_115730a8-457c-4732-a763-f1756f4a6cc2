#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاحات شاملة لنظام الإجازات
Comprehensive Leave System Fixes
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
from datetime import datetime, timed<PERSON><PERSON>

def apply_comprehensive_fixes():
    """تطبيق إصلاحات شاملة لنظام الإجازات"""
    
    print("🔧 بدء تطبيق الإصلاحات الشاملة لنظام الإجازات")
    print("=" * 60)
    
    fixes_applied = []
    
    try:
        # استيراد النظام
        import leave_department_system
        
        # 1. إصلاح التحقق من صحة التواريخ
        fix_date_validation(leave_department_system, fixes_applied)
        
        # 2. إصلاح حساب أيام العمل
        fix_working_days_calculation(leave_department_system, fixes_applied)
        
        # 3. إصلاح التحقق من الرصيد
        fix_balance_validation(leave_department_system, fixes_applied)
        
        # 4. إصلاح رسائل الخطأ
        fix_error_messages(leave_department_system, fixes_applied)
        
        # 5. إصلاح حفظ البيانات
        fix_data_saving(leave_department_system, fixes_applied)
        
        # 6. إصلاح واجهة المستخدم
        fix_user_interface(leave_department_system, fixes_applied)
        
        # 7. إضافة وظائف مفقودة
        add_missing_functions(leave_department_system, fixes_applied)
        
        # عرض النتائج
        show_fixes_summary(fixes_applied)
        
    except Exception as e:
        print(f"❌ خطأ في تطبيق الإصلاحات: {e}")
        import traceback
        traceback.print_exc()

def fix_date_validation(module, fixes_applied):
    """إصلاح التحقق من صحة التواريخ"""
    try:
        print("🔧 إصلاح التحقق من صحة التواريخ...")
        
        # إضافة دالة التحقق من التواريخ
        validation_code = '''
def validate_dates(self, start_date, end_date):
    """التحقق من صحة التواريخ"""
    try:
        from datetime import datetime
        
        if not start_date or not end_date:
            return False, "الرجاء إدخال تاريخ البدء والانتهاء"
        
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")
        
        if start > end:
            return False, "تاريخ البدء يجب أن يكون قبل تاريخ الانتهاء"
        
        # التحقق من أن التاريخ ليس في الماضي البعيد
        today = datetime.now()
        if start < datetime(today.year - 1, 1, 1):
            return False, "تاريخ البدء قديم جداً"
        
        # التحقق من أن التاريخ ليس في المستقبل البعيد
        if end > datetime(today.year + 2, 12, 31):
            return False, "تاريخ الانتهاء بعيد جداً في المستقبل"
        
        return True, "التواريخ صحيحة"
        
    except ValueError:
        return False, "تنسيق التاريخ غير صحيح"
    except Exception as e:
        return False, f"خطأ في التحقق من التواريخ: {str(e)}"
'''
        
        # إضافة الدالة للكلاس
        exec(f"module.LeaveDepartmentSystem.validate_dates = {validation_code}")
        
        fixes_applied.append("✅ إضافة التحقق من صحة التواريخ")
        print("   ✅ تم إضافة التحقق من صحة التواريخ")
        
    except Exception as e:
        print(f"   ❌ فشل في إصلاح التحقق من التواريخ: {e}")

def fix_working_days_calculation(module, fixes_applied):
    """إصلاح حساب أيام العمل"""
    try:
        print("🔧 إصلاح حساب أيام العمل...")
        
        # تحسين دالة حساب أيام العمل
        calculation_code = '''
def calculate_working_days_improved(self, start_date, end_date):
    """حساب أيام العمل المحسن"""
    try:
        from datetime import datetime, timedelta
        
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")
        
        # حساب أيام العمل (استثناء الجمعة والسبت)
        working_days = 0
        current_date = start
        
        while current_date <= end:
            # 4 = الجمعة، 5 = السبت في Python
            if current_date.weekday() not in [4, 5]:
                working_days += 1
            current_date += timedelta(days=1)
        
        return working_days
        
    except Exception as e:
        print(f"خطأ في حساب أيام العمل: {e}")
        return 0
'''
        
        # إضافة الدالة المحسنة
        exec(f"module.LeaveDepartmentSystem.calculate_working_days_improved = {calculation_code}")
        
        fixes_applied.append("✅ تحسين حساب أيام العمل")
        print("   ✅ تم تحسين حساب أيام العمل")
        
    except Exception as e:
        print(f"   ❌ فشل في إصلاح حساب أيام العمل: {e}")

def fix_balance_validation(module, fixes_applied):
    """إصلاح التحقق من الرصيد"""
    try:
        print("🔧 إصلاح التحقق من الرصيد...")
        
        # إضافة دالة التحقق من الرصيد
        balance_code = '''
def validate_leave_balance(self, emp_id, requested_days):
    """التحقق من كفاية الرصيد"""
    try:
        emp_type = self.get_employee_type(emp_id)
        
        # المعلمون لا يحتاجون للتحقق من الرصيد
        if emp_type == "teacher":
            return True, "المعلمون غير مقيدين بالرصيد"
        
        # الموظفون يحتاجون للتحقق من الرصيد
        if emp_type == "employee":
            balance = self.get_employee_balance(emp_id)
            remaining = balance.get('remaining', 0)
            
            if requested_days > remaining:
                return False, f"الرصيد غير كافي. المطلوب: {requested_days}، المتاح: {remaining}"
            
            if remaining - requested_days < 0:
                return False, f"سيؤدي هذا إلى عجز في الرصيد"
            
            return True, f"الرصيد كافي. سيتبقى: {remaining - requested_days} يوم"
        
        return False, "نوع الموظف غير محدد"
        
    except Exception as e:
        return False, f"خطأ في التحقق من الرصيد: {str(e)}"
'''
        
        # إضافة الدالة للكلاس
        exec(f"module.LeaveDepartmentSystem.validate_leave_balance = {balance_code}")
        
        fixes_applied.append("✅ إضافة التحقق من الرصيد")
        print("   ✅ تم إضافة التحقق من الرصيد")
        
    except Exception as e:
        print(f"   ❌ فشل في إصلاح التحقق من الرصيد: {e}")

def fix_error_messages(module, fixes_applied):
    """إصلاح رسائل الخطأ"""
    try:
        print("🔧 إصلاح رسائل الخطأ...")
        
        # إضافة دالة رسائل خطأ محسنة
        error_code = '''
def show_enhanced_error(self, title, message, error_type="error"):
    """عرض رسائل خطأ محسنة"""
    try:
        from tkinter import messagebox
        
        # تحسين الرسالة
        enhanced_message = f"{message}\\n\\n"
        
        if error_type == "validation":
            enhanced_message += "💡 تأكد من:\\n"
            enhanced_message += "• صحة التواريخ\\n"
            enhanced_message += "• اختيار الموظف\\n"
            enhanced_message += "• كفاية الرصيد\\n"
        elif error_type == "date":
            enhanced_message += "📅 تنسيق التاريخ المطلوب: YYYY-MM-DD\\n"
            enhanced_message += "مثال: 2025-06-17"
        elif error_type == "balance":
            enhanced_message += "💰 راجع رصيد الإجازات للموظف\\n"
            enhanced_message += "أو اتصل بقسم الموارد البشرية"
        
        enhanced_message += f"\\n🕒 الوقت: {datetime.now().strftime('%H:%M:%S')}"
        
        messagebox.showerror(title, enhanced_message)
        
    except Exception as e:
        messagebox.showerror("خطأ", f"خطأ في عرض الرسالة: {str(e)}")
'''
        
        # إضافة الدالة للكلاس
        exec(f"module.LeaveDepartmentSystem.show_enhanced_error = {error_code}")
        
        fixes_applied.append("✅ تحسين رسائل الخطأ")
        print("   ✅ تم تحسين رسائل الخطأ")
        
    except Exception as e:
        print(f"   ❌ فشل في إصلاح رسائل الخطأ: {e}")

def fix_data_saving(module, fixes_applied):
    """إصلاح حفظ البيانات"""
    try:
        print("🔧 إصلاح حفظ البيانات...")
        
        # إضافة نسخ احتياطي
        backup_code = '''
def create_backup(self):
    """إنشاء نسخة احتياطية من البيانات"""
    try:
        from datetime import datetime
        import shutil
        import os
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # إنشاء مجلد النسخ الاحتياطية
        backup_dir = "backups"
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        # نسخ الملفات
        files_to_backup = [
            "department_leaves_data.xlsx",
            "employees_data.xlsx", 
            "balance_data.xlsx"
        ]
        
        backed_up = []
        for file in files_to_backup:
            if os.path.exists(file):
                backup_name = f"{backup_dir}/backup_{timestamp}_{file}"
                shutil.copy2(file, backup_name)
                backed_up.append(file)
        
        return True, f"تم إنشاء نسخة احتياطية لـ {len(backed_up)} ملف"
        
    except Exception as e:
        return False, f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}"
'''
        
        # إضافة الدالة للكلاس
        exec(f"module.LeaveDepartmentSystem.create_backup = {backup_code}")
        
        fixes_applied.append("✅ إضافة النسخ الاحتياطي")
        print("   ✅ تم إضافة النسخ الاحتياطي")
        
    except Exception as e:
        print(f"   ❌ فشل في إصلاح حفظ البيانات: {e}")

def fix_user_interface(module, fixes_applied):
    """إصلاح واجهة المستخدم"""
    try:
        print("🔧 إصلاح واجهة المستخدم...")
        
        # إضافة دالة تحسين الواجهة
        ui_code = '''
def enhance_ui_responsiveness(self):
    """تحسين استجابة واجهة المستخدم"""
    try:
        # تحسين أداء التحديث
        if hasattr(self, 'window') and self.window:
            self.window.update_idletasks()
        
        # تحسين عرض البيانات
        if hasattr(self, 'pending_tree'):
            # تحديث الجدول بشكل أفضل
            for item in self.pending_tree.get_children():
                self.pending_tree.set(item, "الحالة", "محدث")
        
        return True
        
    except Exception as e:
        print(f"خطأ في تحسين الواجهة: {e}")
        return False
'''
        
        # إضافة الدالة للكلاس
        exec(f"module.LeaveDepartmentSystem.enhance_ui_responsiveness = {ui_code}")
        
        fixes_applied.append("✅ تحسين واجهة المستخدم")
        print("   ✅ تم تحسين واجهة المستخدم")
        
    except Exception as e:
        print(f"   ❌ فشل في إصلاح واجهة المستخدم: {e}")

def add_missing_functions(module, fixes_applied):
    """إضافة وظائف مفقودة"""
    try:
        print("🔧 إضافة وظائف مفقودة...")
        
        # إضافة دالة تصدير محسنة
        export_code = '''
def export_enhanced_report(self, report_type="comprehensive"):
    """تصدير تقرير محسن"""
    try:
        from tkinter import filedialog
        from datetime import datetime
        
        # اختيار مكان الحفظ
        filename = filedialog.asksaveasfilename(
            title="حفظ التقرير",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        
        if not filename:
            return False, "تم إلغاء العملية"
        
        # إنشاء التقرير
        if report_type == "comprehensive":
            success = self.create_comprehensive_excel_report(filename)
        else:
            success = self.save_leaves_data()
        
        if success:
            return True, f"تم حفظ التقرير في: {filename}"
        else:
            return False, "فشل في إنشاء التقرير"
        
    except Exception as e:
        return False, f"خطأ في التصدير: {str(e)}"
'''
        
        # إضافة الدالة للكلاس
        exec(f"module.LeaveDepartmentSystem.export_enhanced_report = {export_code}")
        
        # إضافة دالة إحصائيات محسنة
        stats_code = '''
def get_enhanced_statistics(self):
    """الحصول على إحصائيات محسنة"""
    try:
        stats = {
            "total_employees": len(self.employees_data),
            "total_leaves": len(self.leaves_data),
            "pending_leaves": len([l for l in self.leaves_data if l.get("الحالة") == "في الانتظار"]),
            "approved_leaves": len([l for l in self.leaves_data if l.get("الحالة") == "موافق عليها"]),
            "rejected_leaves": len([l for l in self.leaves_data if l.get("الحالة") == "مرفوضة"]),
            "employees_with_balance": len([b for b in self.balance_data if b.get("الرصيد المتبقي", 0) > 0]),
            "total_balance_used": sum([b.get("الرصيد المستخدم", 0) for b in self.balance_data]),
            "last_update": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        return stats
        
    except Exception as e:
        print(f"خطأ في الإحصائيات: {e}")
        return {}
'''
        
        # إضافة الدالة للكلاس
        exec(f"module.LeaveDepartmentSystem.get_enhanced_statistics = {stats_code}")
        
        fixes_applied.append("✅ إضافة وظائف التصدير المحسن")
        fixes_applied.append("✅ إضافة إحصائيات محسنة")
        print("   ✅ تم إضافة وظائف التصدير المحسن")
        print("   ✅ تم إضافة إحصائيات محسنة")
        
    except Exception as e:
        print(f"   ❌ فشل في إضافة الوظائف المفقودة: {e}")

def show_fixes_summary(fixes_applied):
    """عرض ملخص الإصلاحات"""
    
    print("\n" + "="*60)
    print("📋 ملخص الإصلاحات المطبقة:")
    print("="*60)
    
    if fixes_applied:
        for i, fix in enumerate(fixes_applied, 1):
            print(f"{i:2d}. {fix}")
        
        print(f"\n🎉 تم تطبيق {len(fixes_applied)} إصلاح بنجاح!")
        
        # إنشاء تقرير الإصلاحات
        create_fixes_report(fixes_applied)
        
    else:
        print("❌ لم يتم تطبيق أي إصلاحات")
    
    print("="*60)

def create_fixes_report(fixes_applied):
    """إنشاء تقرير الإصلاحات"""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"تقرير_الإصلاحات_{timestamp}.txt"
        
        report = f"""
🔧 تقرير الإصلاحات المطبقة على نظام الإجازات
{'='*60}

📅 تاريخ التطبيق: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📊 عدد الإصلاحات: {len(fixes_applied)}

📝 قائمة الإصلاحات:
"""
        
        for i, fix in enumerate(fixes_applied, 1):
            report += f"{i:2d}. {fix}\n"
        
        report += f"""

🎯 الهدف من الإصلاحات:
• تحسين استقرار النظام
• إضافة التحقق من صحة البيانات
• تحسين تجربة المستخدم
• إضافة وظائف مفقودة
• تحسين رسائل الخطأ

💡 التوصيات للمستقبل:
• اختبار دوري للنظام
• تحديث البيانات بانتظام
• إنشاء نسخ احتياطية
• تدريب المستخدمين
• مراقبة الأداء

🔗 للمساعدة:
راجع دليل المستخدم أو اتصل بالدعم الفني

---
تم إنشاء هذا التقرير تلقائياً بواسطة نظام الإصلاحات
        """
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"📄 تم إنشاء تقرير الإصلاحات: {filename}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء تقرير الإصلاحات: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔧 نظام الإصلاحات الشاملة لنظام الإجازات")
    print("=" * 60)
    
    try:
        apply_comprehensive_fixes()
        
        print("\n🎉 انتهى تطبيق الإصلاحات!")
        print("💡 يمكنك الآن تشغيل الاختبار الشامل مرة أخرى للتحقق من التحسينات")
        
    except Exception as e:
        print(f"❌ خطأ عام في النظام: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
