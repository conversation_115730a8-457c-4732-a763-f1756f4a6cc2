#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
البيانات المرجعية للقوائم المنسدلة في جميع وحدات النظام
"""

# 1. أسماء المصارف
BANK_NAMES = [
    "الجمهورية وكاله قصر بن غشير",
    "شمال افريقيا وكاله سيدي السائح",
    "الجمهوريه فرع سوق الخميس",
    "شمال افريقيا وكالة اسبيعه",
    "التجارى اسبيعه",
    "الجمهوريه فرع قصر بن غشير",
    "شمال افريقيا فرع قصر بن غشير",
    "الجمهوريه فرع طرابلس المقريف",
    "الوحدة فرع طريق المطار",
    "الجمهورية سوق الاحد",
    "الوحده السوانــى",
    "شمال افريقيا وادي الربيع",
    "الوحده الرئيســى",
    "السراي للتجارة والاستثمار فرع ميزران",
    "الجمهوريه فرع الرجبان",
    "الصحاري وكاله النهر الصناعي",
    "الوحدة يفــرن",
    "الجمهورية جنوب طرابلس",
    "الجمهورية فرع الباب الجديد",
    "التجارى تاورغــاء",
    "الجمهورية جامعة طرابلس",
    "الجمهورية سوق الجمعه",
    "الجمهورية وكالة المهاري",
    "شمال افريقيا وكاله سوق الخميس",
    "الجمهورية شارع الصريم",
    "الجمهورية عين زاره",
    "التجارى المدينه",
    "الوحده المغاربه",
    "الجمهورية زليتـــن",
    "الجمهورية غرب طرابلس",
    "الجمهوريه نالوت",
    "التجارى السوانى",
    "الجمهورية وكاله اسبيعه",
    "شمال افريقيا ترهونة",
    "الوحده البلديـــه",
    "الصحارى العمامـرة",
    "شمال افريقيا مسلاته",
    "الجمهورية نسمة",
    "الجمهورية تاجوراء",
    "شمال افريقيا فرع جنزور",
    "التجارى الرئيسى",
    "الجمهورية الكريمية",
    "الجمهوريه ام الارانب",
    "الوحدة الجـــادة",
    "شمال افريقيا تاورغاء",
    "التجارى الظهرة",
    "الجمهوريه فرع المغاربه",
    "شمال افريقيا سوق الجمعه",
    "الجمهوريه الاصابعه",
    "الوحده سوق الثلاثاء",
    "الصحاري بن عاشور",
    "الوحدة المعمورة",
    "الصحاري مسلاتـه",
    "التجارى سوق الثلاثاء",
    "الصحاري باب المدينه",
    "الجمهوريه جادو",
    "اليقين فرع البرج",
    "الجمهورية فرع ميزران",
    "الجمهورية العجيلات",
    "التجاري المطــار",
    "الجمهوريه فرع راس حسن",
    "الجمهورية الظهره",
    "الجمهوريه فرع القادسيه",
    "الجمهوريه حي دمشق",
    "الجمهورية غريان",
    "الوحده سبها",
    "الصحارى مصراتـة",
    "التجارى غريان",
    "الواحه وكالة سوق الجمعه",
    "الجمهوريه وكالة برج طرابلس",
    "الوحدة العجيـلات",
    "الاسلامي مصراتة",
    "الجمهورية فرع زويلة",
    "شمال افريقيا وكاله صلاح الدين",
    "الجمهوريه هون",
    "الوحده حى الاندلس",
    "التجارى ترهونـــة",
    "الجمهورية الداوون",
    "شمال افريقيا فرع تاجوراء",
    "التجارى فرع الاستقلال",
    "الجمهوريه الميدان مصراته",
    "الصحارى العربـان",
    "الصحارى الرشيـد",
    "الجمهورية الرئيسي",
    "الجمهوريه فرع طرابلس الميدان",
    "الجمهوريه تنيناي",
    "الجمهورية حى الاندلس",
    "الوحده زليتن",
    "الجمهورية القواسم",
    "الجمهوريه وكالة ابي الاشهر",
    "الصحارى السوانـى",
    "الجمهورية فرع تندميرة",
    "التجارى مصراتة",
    "الصحارى الرئيسى",
    "الجمهورية السوانى",
    "الصحاري الزاويـة",
    "الجمهوريه وكالة الشاحنات",
    "التجارى ككلــه",
    "المتحد للتجارة فرع العجيلات",
    "اليقين",
    "النوران فر المشتل",
    "الواحة الرئيسى",
    "الجمهوريه فرع الرشيــد",
    "مصرف النوران وكالة المدار",
    "الجمهورية العلوص",
    "التجارى العزيزيه",
    "الجمهوريه فرع جنزور الشرقي",
    "الوحدة الحرابـة",
    "الجمهورية فرع وادي كعام",
    "الصحاري وكالة عين زارة",
    "شمال افريقيا فرع الخمـس",
    "المتحد للتجارة والاستثمار العزيزية",
    "المتحد للتجارة فرع الجديدة",
    "التجارى المينـاء",
    "شمال افريقيا فرع ابي سليم",
    "الجمهوريه فرع / طريق المطار",
    "الجمهوريه فرع سوق الثلاثـاء",
    "الجمهوريه فرع القرضابيه",
    "الوحده مسلاته",
    "التجارى بني وليد",
    "الجمهوريه وكالة قرجي",
    "الجمهوريه الخضراء وكالة ترغلات ترهونه",
    "الجمهوريه وكالة انجيلة",
    "الوحده الخمس",
    "الوحدة مصراتــه",
    "الجمهوريه الحوامد",
    "الوحدة ميدان الجزائر",
    "شمال افريقيا سبها",
    "مصرف السراي للتجارة والاستثمار",
    "الجمهورية بني وليد",
    "شمال افريقيا الرئيسي",
    "الجمهورية المطار",
    "الواحة مصراته للخدمات الاسلامية",
    "شمال افريقيا سوف الجين",
    "الصحاري شهداء كعام",
    "التجارى تاجوراء",
    "الجمهورية الخمس",
    "الجمهورية ترهونة",
    "الامان والتجاره حي الاندلس",
    "الوفاء الفلاح",
    "التجارة والتنمية سوق الجمعة",
    "المتحد للتجارة والاستثمار السواني",
    "شمال افريقيا وكالة ماجر",
    "الوحدة الريايينة",
    "الوحده فرع الاستقلاال",
    "الجمهوريه وكالة فشلوم",
    "الجمهورية فرع مصراتة احمد الشريف",
    "الجمهورية القريات",
    "الواحة فرع ترهونة",
    "التجاري مستشفى شارع الزاويه",
    "الاسلامي الليبي ابومليانة",
    "الوحدة الهيـــرة"
]

# 2. الجنسيات
NATIONALITIES = [
    "ليبي",
    "ليبية", 
    "فلسطيني",
    "فلسطينية",
    "أخرى"
]

# 3. المؤهلات العلمية
QUALIFICATIONS = [
    "بكالوريوس",
    "ثانوية تخصصية",
    "ليسانس",
    "دبلوم عالي",
    "دبلوم متقدم",
    "دبلوم متوسط",
    "اجازة التدريس الخاصة",
    "دورة تدريبية",
    "العامة",
    "اجازة قران",
    "ثانوية علوم أساسية",
    "ابتدائي",
    "عامة",
    "الخاصة",
    "ثانوية عامة",
    "تعليم اساسي",
    "شهادة اعدادية",
    "ثانوية فنية",
    "دورة حاسوب",
    "لايوجد",
    "بدون مؤهل",
    "اعدادي",
    "دورة معلمين",
    "حفظ القرآن",
    "عام",
    "دبلوم متوسط مختبرات",
    "شهاده اعداديه",
    "د.تمريض",
    "د.عام",
    "معلمة فصل",
    "ماجستير"
]

# 4. أماكن العمل
WORK_PLACES = [
    "أبو شيبة",
    "ابن رشد",
    "الاجواد",
    "السائح الأساسي",
    "السائح الثانوية",
    "المبروك اونيس",
    "المجاهدين",
    "المدينة الجديدة",
    "المعرفة",
    "النهضة",
    "الوفاق",
    "أبي عائشة",
    "بئرذياب مشتركة",
    "جعفر بن ابي طالب",
    "دايون المراقبة",
    "راية الاسلام",
    "شهداء الوطن",
    "طلائع الحرية",
    "فوناس",
    "منصور عون",
    "نجوم الوحدة مشتركة",
    "وادي الربيع للتعليم الاساسي",
    "وادي الربيع الشمالية للتعليم الاساسي",
    "وادي الربيع للتعليم الثانوي"
]

def get_bank_names():
    """الحصول على قائمة أسماء المصارف"""
    return BANK_NAMES.copy()

def get_nationalities():
    """الحصول على قائمة الجنسيات"""
    return NATIONALITIES.copy()

def get_qualifications():
    """الحصول على قائمة المؤهلات العلمية"""
    return QUALIFICATIONS.copy()

def get_work_places():
    """الحصول على قائمة أماكن العمل"""
    return WORK_PLACES.copy()

def get_all_reference_data():
    """الحصول على جميع البيانات المرجعية"""
    return {
        "bank_names": get_bank_names(),
        "nationalities": get_nationalities(),
        "qualifications": get_qualifications(),
        "work_places": get_work_places()
    }

if __name__ == "__main__":
    # اختبار البيانات المرجعية
    print("🏦 أسماء المصارف:", len(get_bank_names()))
    print("🌍 الجنسيات:", len(get_nationalities()))
    print("🎓 المؤهلات:", len(get_qualifications()))
    print("🏢 أماكن العمل:", len(get_work_places()))
