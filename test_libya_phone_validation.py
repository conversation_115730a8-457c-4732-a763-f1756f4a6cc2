#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التحقق من أرقام الهاتف الليبية
Test Libya Phone Number Validation
"""

import sys
import datetime

def test_libya_phone_validation():
    """اختبار التحقق من أرقام الهاتف الليبية"""
    print("🇱🇾 اختبار التحقق من أرقام الهاتف الليبية")
    print("=" * 60)
    
    try:
        from data_validation import DataValidator
        validator = DataValidator()
        
        # حالات اختبار أرقام الهاتف الليبية
        test_cases = [
            # أرقام الجوال الليبية الصحيحة (10 أرقام)
            ("0911234567", True, "جوال ليبي محلي (091xxxxxxx)"),
            ("0921234567", True, "جوال ليبي محلي (092xxxxxxx)"),
            ("0941234567", True, "جوال ليبي محلي (094xxxxxxx)"),
            ("0951234567", True, "جوال ليبي محلي (095xxxxxxx)"),
            
            # أرقام الجوال الليبية الدولية (13 رقم)
            ("2180911234567", True, "جوال ليبي دولي (218091xxxxxxx)"),
            ("2180921234567", True, "جوال ليبي دولي (218092xxxxxxx)"),
            ("2180941234567", True, "جوال ليبي دولي (218094xxxxxxx)"),
            ("2180951234567", True, "جوال ليبي دولي (218095xxxxxxx)"),
            
            # أرقام مع رموز
            ("+2180911234567", True, "جوال ليبي مع + (+218091xxxxxxx)"),
            ("218-************", True, "جوال ليبي مع شرطات"),
            ("218 091 123 4567", True, "جوال ليبي مع مسافات"),
            ("(218) 091 123 4567", True, "جوال ليبي مع أقواس"),
            
            # أرقام الهاتف الثابت الليبية
            ("021123456", True, "هاتف ثابت طرابلس (021xxxxxx)"),
            ("0211234567", True, "هاتف ثابت طرابلس (021xxxxxxx)"),
            ("061123456", True, "هاتف ثابت بنغازي (061xxxxxx)"),
            ("0611234567", True, "هاتف ثابت بنغازي (061xxxxxxx)"),
            ("051123456", True, "هاتف ثابت مصراتة (051xxxxxx)"),
            ("023123456", True, "هاتف ثابت الزاوية (023xxxxxx)"),
            ("024123456", True, "هاتف ثابت صبراتة (024xxxxxx)"),
            ("025123456", True, "هاتف ثابت زليتن (025xxxxxx)"),
            
            # أرقام الهاتف الثابت الدولية
            ("218021123456", True, "هاتف ثابت دولي طرابلس"),
            ("218061123456", True, "هاتف ثابت دولي بنغازي"),
            ("218051123456", True, "هاتف ثابت دولي مصراتة"),
            
            # أرقام خاطئة - طول غير صحيح
            ("09112345", False, "رقم جوال قصير (8 أرقام)"),
            ("091123456789", False, "رقم جوال طويل (12 رقم محلي)"),
            ("21809112345", False, "رقم دولي قصير (11 رقم)"),
            ("21809112345678", False, "رقم دولي طويل (14 رقم)"),
            
            # أرقام خاطئة - بداية غير صحيحة
            ("0811234567", False, "رقم يبدأ بـ 081 (غير مستخدم للجوال)"),
            ("0931234567", False, "رقم يبدأ بـ 093 (غير مستخدم)"),
            ("0961234567", False, "رقم يبدأ بـ 096 (غير مستخدم)"),
            ("2180811234567", False, "رقم دولي يبدأ بـ 218081 (غير صحيح)"),
            
            # أرقام تحتوي على أحرف
            ("091123456a", False, "رقم يحتوي على حرف"),
            ("09l1234567", False, "رقم يحتوي على حرف l بدلاً من 1"),
            ("218-091-123-456a", False, "رقم دولي يحتوي على حرف"),
            
            # حالات خاصة
            ("", True, "رقم فارغ (تحذير)"),
            ("   ", True, "مسافات فقط (تحذير)"),
            ("123", False, "رقم قصير جداً"),
            ("002180911234567", False, "رقم مع 00 إضافي"),
            
            # أرقام دولية أخرى (غير ليبية)
            ("966501234567", False, "رقم سعودي (966)"),
            ("971501234567", False, "رقم إماراتي (971)"),
            ("20101234567", False, "رقم مصري (20)"),
            ("212661234567", False, "رقم مغربي (212)"),
            ("216123456789", False, "رقم تونسي (216)"),
            ("213661234567", False, "رقم جزائري (213)"),
        ]
        
        passed = 0
        failed = 0
        total = len(test_cases)
        
        print(f"🔍 تشغيل {total} اختبار لأرقام الهاتف الليبية...")
        print()
        
        for i, (phone, expected, description) in enumerate(test_cases, 1):
            validator.clear_messages()
            result = validator.validate_phone(phone)
            
            if result == expected:
                status = "✅" if expected else "✅"
                print(f"{status} اختبار {i:2d}: {description}")
                if phone and len(phone.strip()) > 0:
                    print(f"    الرقم: {phone}")
                passed += 1
            else:
                print(f"❌ اختبار {i:2d}: {description}")
                print(f"    الرقم: {phone}")
                print(f"    المتوقع: {expected}, الفعلي: {result}")
                messages = validator.get_all_messages()
                if messages:
                    for msg in messages:
                        print(f"    رسالة: {msg}")
                failed += 1
            
            # إضافة خط فاصل بين المجموعات
            if i in [4, 8, 12, 20, 23, 27, 31, 34, 38, 42]:
                print()
        
        print()
        print("=" * 60)
        print(f"📊 نتائج الاختبار:")
        print(f"   إجمالي الاختبارات: {total}")
        print(f"   نجح: {passed}")
        print(f"   فشل: {failed}")
        print(f"   معدل النجاح: {(passed/total)*100:.1f}%")
        
        if failed == 0:
            print("🎉 جميع الاختبارات نجحت! التحقق من أرقام الهاتف الليبية يعمل بشكل مثالي")
        else:
            print(f"⚠️ {failed} اختبار فشل - يحتاج مراجعة")
        
        return passed, failed, total
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد نظام التحقق: {e}")
        return 0, 1, 1
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبار: {e}")
        return 0, 1, 1

def show_libya_phone_format_examples():
    """عرض أمثلة على التنسيقات المقبولة للأرقام الليبية"""
    print("\n📋 التنسيقات المقبولة لأرقام الهاتف الليبية:")
    print("=" * 60)
    
    examples = [
        ("أرقام الجوال المحلية", [
            "0911234567 (شبكة المدار الجديد)",
            "0921234567 (شبكة ليبيانا)",
            "0941234567 (شبكة المدار الجديد)",
            "0951234567 (شبكة ليبيانا)"
        ]),
        ("أرقام الجوال الدولية", [
            "2180911234567", "+2180911234567", "218-************"
        ]),
        ("أرقام الهاتف الثابت - المدن الرئيسية", [
            "021123456 (طرابلس)", "061123456 (بنغازي)", "051123456 (مصراتة)"
        ]),
        ("أرقام الهاتف الثابت - مدن أخرى", [
            "023123456 (الزاوية)", "024123456 (صبراتة)", "025123456 (زليتن)"
        ]),
        ("أرقام الهاتف الثابت الدولية", [
            "218021123456", "+218021123456"
        ])
    ]
    
    for category, phones in examples:
        print(f"\n📱 {category}:")
        for phone in phones:
            print(f"   ✅ {phone}")
    
    print(f"\n❌ التنسيقات المرفوضة:")
    rejected = [
        "0811234567 (ليس رقم جوال ليبي)",
        "09112345 (قصير)",
        "091123456789 (طويل)",
        "966501234567 (سعودي)",
        "091123456a (يحتوي على أحرف)"
    ]
    
    for phone in rejected:
        print(f"   ❌ {phone}")

def show_libya_area_codes():
    """عرض أكواد المناطق الليبية"""
    print(f"\n🏙️ أكواد المناطق الليبية:")
    print("=" * 60)
    
    area_codes = {
        "المدن الرئيسية": {
            "021": "طرابلس",
            "061": "بنغازي", 
            "051": "مصراتة"
        },
        "المدن الغربية": {
            "023": "الزاوية",
            "024": "صبراتة",
            "025": "زليتن",
            "027": "غريان"
        },
        "المدن الشرقية": {
            "062": "المرج",
            "063": "طبرق",
            "064": "درنة",
            "065": "البيضاء"
        },
        "المدن الجنوبية": {
            "071": "سبها",
            "072": "مرزق",
            "073": "أوباري"
        }
    }
    
    for region, codes in area_codes.items():
        print(f"\n📍 {region}:")
        for code, city in codes.items():
            print(f"   {code}: {city}")

def main():
    """تشغيل جميع الاختبارات"""
    print("🇱🇾 اختبار التحقق من أرقام الهاتف الليبية")
    print(f"📅 التاريخ: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 إصدار Python: {sys.version}")
    print()
    
    # اختبار التحقق من أرقام الهاتف
    passed, failed, total = test_libya_phone_validation()
    
    # عرض أمثلة التنسيقات
    show_libya_phone_format_examples()
    
    # عرض أكواد المناطق
    show_libya_area_codes()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("📋 الملخص النهائي:")
    print(f"   🧪 اختبارات التحقق: {passed}/{total} نجح")
    
    if failed == 0:
        print("\n🎉 تم تحديث النظام لدعم أرقام الهاتف الليبية بنجاح!")
        print("✅ النظام يدعم الآن:")
        print("   • أرقام الجوال الليبية (091, 092, 094, 095)")
        print("   • أرقام الجوال الدولية (+218091xxxxxxx)")
        print("   • أرقام الهاتف الثابت الليبية")
        print("   • التنسيقات المختلفة (مع رموز ومسافات)")
        print("   • رسائل خطأ واضحة ومفصلة")
    else:
        print("\n⚠️ يحتاج النظام إلى مراجعة إضافية")
    
    print("\n🏁 انتهى الاختبار")

if __name__ == "__main__":
    main()
