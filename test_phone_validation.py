#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التحقق من أرقام الهاتف السعودية
Test Saudi Phone Number Validation
"""

import sys
import datetime

def test_phone_validation():
    """اختبار التحقق من أرقام الهاتف"""
    print("📱 اختبار التحقق من أرقام الهاتف             # أرقام الهاتف الثابت الليبية")
    print("=" * 60)
    
    try:
        from data_validation import DataValidator
        validator = DataValidator()
        
        # حالات اختبار أرقام الهاتف
        test_cases = [
            # أرقام الجوال             # أرقام الهاتف الثابت الليبية الصحيحة
            ("0501234567", True, "جوال سعودي محلي (05xxxxxxxx)"),
            ("0551234567", True, "جوال سعودي محلي (05xxxxxxxx)"),
            ("0591234567", True, "جوال سعودي محلي (05xxxxxxxx)"),
            
            # أرقام الجوال الدولية الصحيحة
            ("966501234567", True, "جوال             # أرقام الهاتف الثابت الليبية دولي (966xxxxxxxxx)"),
            ("966551234567", True, "جوال             # أرقام الهاتف الثابت الليبية دولي (966xxxxxxxxx)"),
            ("966591234567", True, "جوال             # أرقام الهاتف الثابت الليبية دولي (966xxxxxxxxx)"),
            
            # أرقام مع رموز
            ("+966501234567", True, "جوال سعودي مع + (+966xxxxxxxxx)"),
            ("966-50-123-4567", True, "جوال سعودي مع شرطات"),
            ("966 50 123 4567", True, "جوال سعودي مع مسافات"),
            ("(966) 50 123 4567", True, "جوال سعودي مع أقواس"),
            
            # أرقام الهاتف الثابت الليبية
            ("011234567", True, "هاتف ثابت الرياض (01xxxxxxx)"),
            ("021234567", True, "هاتف ثابت مكة (02xxxxxxx)"),
            ("031234567", True, "هاتف ثابت المدينة (03xxxxxxx)"),
            ("041234567", True, "هاتف ثابت الدمام (04xxxxxxx)"),
            ("061234567", True, "هاتف ثابت بريدة (06xxxxxxx)"),
            ("071234567", True, "هاتف ثابت حائل (07xxxxxxx)"),
            
            # أرقام الهاتف الثابت الدولية
            ("966011234567", True, "هاتف ثابت دولي الرياض"),
            ("966021234567", True, "هاتف ثابت دولي مكة"),
            
            # أرقام خاطئة - طول غير صحيح
            ("05012345", False, "رقم قصير (8 أرقام)"),
            ("050123456789", False, "رقم طويل (12 رقم محلي)"),
            ("96650123456", False, "رقم دولي قصير (11 رقم)"),
            ("9665012345678", False, "رقم دولي طويل (13 رقم)"),
            
            # أرقام خاطئة - بداية غير صحيحة
            ("0401234567", False, "رقم يبدأ بـ 04 (ليس جوال)"),
            ("0801234567", False, "رقم يبدأ بـ 08 (غير مستخدم)"),
            ("0901234567", False, "رقم يبدأ بـ 09 (غير مستخدم)"),
            ("966401234567", False, "رقم دولي يبدأ بـ 9664 (ليس جوال)"),
            
            # أرقام تحتوي على أحرف
            ("050123456a", False, "رقم يحتوي على حرف"),
            ("05o1234567", False, "رقم يحتوي على حرف o بدلاً من 0"),
            ("966-50-123-456a", False, "رقم دولي يحتوي على حرف"),
            
            # حالات خاصة
            ("", True, "رقم فارغ (تحذير)"),
            ("   ", True, "مسافات فقط (تحذير)"),
            ("123", False, "رقم قصير جداً"),
            ("00966501234567", False, "رقم مع 00 إضافي"),
            
            # أرقام دولية أخرى (غير سعودية)
            ("971501234567", False, "رقم إماراتي (971)"),
            ("974501234567", False, "رقم قطري (974)"),
            ("965501234567", False, "رقم كويتي (965)"),
        ]
        
        passed = 0
        failed = 0
        total = len(test_cases)
        
        print(f"🔍 تشغيل {total} اختبار لأرقام الهاتف...")
        print()
        
        for i, (phone, expected, description) in enumerate(test_cases, 1):
            validator.clear_messages()
            result = validator.validate_phone(phone)
            
            if result == expected:
                status = "✅" if expected else "✅"
                print(f"{status} اختبار {i:2d}: {description}")
                if phone and len(phone.strip()) > 0:
                    print(f"    الرقم: {phone}")
                passed += 1
            else:
                print(f"❌ اختبار {i:2d}: {description}")
                print(f"    الرقم: {phone}")
                print(f"    المتوقع: {expected}, الفعلي: {result}")
                messages = validator.get_all_messages()
                if messages:
                    for msg in messages:
                        print(f"    رسالة: {msg}")
                failed += 1
            
            # إضافة خط فاصل بين المجموعات
            if i in [3, 6, 12, 18, 22, 26, 29, 33, 37]:
                print()
        
        print()
        print("=" * 60)
        print(f"📊 نتائج الاختبار:")
        print(f"   إجمالي الاختبارات: {total}")
        print(f"   نجح: {passed}")
        print(f"   فشل: {failed}")
        print(f"   معدل النجاح: {(passed/total)*100:.1f}%")
        
        if failed == 0:
            print("🎉 جميع الاختبارات نجحت! التحقق من أرقام الهاتف يعمل بشكل مثالي")
        else:
            print(f"⚠️ {failed} اختبار فشل - يحتاج مراجعة")
        
        return passed, failed, total
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد نظام التحقق: {e}")
        return 0, 1, 1
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبار: {e}")
        return 0, 1, 1

def show_phone_format_examples():
    """عرض أمثلة على التنسيقات المقبولة"""
    print("\n📋 التنسيقات المقبولة لأرقام الهاتف السعودية:")
    print("=" * 60)
    
    examples = [
        ("أرقام الجوال المحلية", [
            "0501234567", "0551234567", "0591234567"
        ]),
        ("أرقام الجوال الدولية", [
            "966501234567", "+966501234567", "966-50-123-4567"
        ]),
        ("أرقام الهاتف الثابت", [
            "011234567 (الرياض)", "021234567 (مكة)", "031234567 (المدينة)"
        ]),
        ("أرقام الهاتف الثابت الدولية", [
            "966011234567", "+966011234567"
        ])
    ]
    
    for category, phones in examples:
        print(f"\n📱 {category}:")
        for phone in phones:
            print(f"   ✅ {phone}")
    
    print(f"\n❌ التنسيقات المرفوضة:")
    rejected = [
        "0401234567 (ليس رقم جوال)",
        "050123456 (قصير)",
        "050123456789 (طويل)",
        "971501234567 (ليس سعودي)",
        "050123456a (يحتوي على أحرف)"
    ]
    
    for phone in rejected:
        print(f"   ❌ {phone}")

def main():
    """تشغيل جميع الاختبارات"""
    print("📱 اختبار التحقق من أرقام الهاتف السعودية")
    print(f"📅 التاريخ: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 إصدار Python: {sys.version}")
    print()
    
    # اختبار التحقق من أرقام الهاتف
    passed, failed, total = test_phone_validation()
    
    # عرض أمثلة التنسيقات
    show_phone_format_examples()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("📋 الملخص النهائي:")
    print(f"   🧪 اختبارات التحقق: {passed}/{total} نجح")
    
    if failed == 0:
        print("\n🎉 تم إصلاح التحقق من أرقام الهاتف بنجاح!")
        print("✅ النظام يدعم الآن:")
        print("   • أرقام الجوال السعودية (05xxxxxxxx)")
        print("   • أرقام الجوال الدولية (+966xxxxxxxxx)")
        print("   • أرقام الهاتف الثابت السعودية")
        print("   • التنسيقات المختلفة (مع رموز ومسافات)")
        print("   • رسائل خطأ واضحة ومفصلة")
    else:
        print("\n⚠️ يحتاج النظام إلى مراجعة إضافية")
    
    print("\n🏁 انتهى الاختبار")

if __name__ == "__main__":
    main()
