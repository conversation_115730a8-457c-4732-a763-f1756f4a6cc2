#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أدوات مساعدة لنظام القوالب
تتيح تهيئة القوالب بدون إنشاء نوافذ
"""

import os
from datetime import datetime

def initialize_templates_directory():
    """تهيئة مجلد القوالب بدون إنشاء نوافذ"""
    try:
        templates_dir = "correspondence_templates"
        
        # إنشاء مجلد القوالب إذا لم يكن موجوداً
        if not os.path.exists(templates_dir):
            os.makedirs(templates_dir)
            print(f"📂 تم إنشاء مجلد القوالب: {templates_dir}")
        
        # فحص وجود قوالب أساسية
        basic_templates = [
            "قالب_افتراضي.docx",
            "قالب_نموذج_مباشرة.docx",
            "مباشرة.docx"
        ]
        
        existing_templates = []
        if os.path.exists(templates_dir):
            for file in os.listdir(templates_dir):
                if file.endswith('.docx'):
                    existing_templates.append(file)
        
        if existing_templates:
            print(f"✅ تم العثور على {len(existing_templates)} قالب موجود")
        else:
            print("📋 لا توجد قوالب - يمكن إنشاؤها من مدير القوالب")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تهيئة مجلد القوالب: {e}")
        return False

def check_template_system():
    """فحص حالة نظام القوالب"""
    try:
        # فحص وجود مجلد القوالب
        templates_dir = "correspondence_templates"
        if not os.path.exists(templates_dir):
            return {
                "status": "missing_directory",
                "message": "مجلد القوالب غير موجود",
                "templates_count": 0
            }
        
        # عد القوالب الموجودة
        templates_count = 0
        templates_list = []
        
        for file in os.listdir(templates_dir):
            if file.endswith('.docx') and not file.startswith('~'):
                templates_count += 1
                templates_list.append(file.replace('.docx', ''))
        
        if templates_count == 0:
            return {
                "status": "no_templates",
                "message": "لا توجد قوالب في المجلد",
                "templates_count": 0,
                "templates_list": []
            }
        
        return {
            "status": "ready",
            "message": f"نظام القوالب جاهز - {templates_count} قالب متاح",
            "templates_count": templates_count,
            "templates_list": templates_list
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"خطأ في فحص نظام القوالب: {e}",
            "templates_count": 0,
            "templates_list": []
        }

def create_basic_template_if_missing():
    """إنشاء قالب أساسي إذا لم توجد قوالب"""
    try:
        # فحص وجود مكتبة python-docx
        try:
            from docx import Document
            from docx.shared import Inches
            from docx.enum.text import WD_ALIGN_PARAGRAPH
        except ImportError:
            print("⚠️ مكتبة python-docx غير متاحة - لا يمكن إنشاء قالب أساسي")
            return False
        
        templates_dir = "correspondence_templates"
        basic_template_path = os.path.join(templates_dir, "قالب_افتراضي.docx")
        
        # إنشاء القالب فقط إذا لم يكن موجوداً
        if os.path.exists(basic_template_path):
            return True
        
        # إنشاء مستند جديد
        doc = Document()
        
        # إعداد الصفحة
        section = doc.sections[0]
        section.page_height = Inches(11.69)  # A4
        section.page_width = Inches(8.27)
        section.left_margin = Inches(1)
        section.right_margin = Inches(1)
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        
        # العنوان
        title = doc.add_heading('بسم الله الرحمن الرحيم', level=1)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # إضافة مسافة
        doc.add_paragraph()
        
        # محتوى القالب
        content = [
            "التاريخ: {{التاريخ_الحالي}}",
            "",
            "السيد/ة: {{الاسم_العربي}}",
            "الرقم الوظيفي: {{الرقم_الوظيفي}}",
            "المسمى الوظيفي: {{المسمى_الوظيفي}}",
            "الدرجة الحالية: {{الدرجة_الحالية}}",
            "مكان العمل: {{مكان_العمل_الحالي}}",
            "",
            "تحية طيبة وبعد،",
            "",
            "نفيدكم بأنه تم إنجاز الإجراءات المطلوبة حسب الأنظمة المعمول بها.",
            "",
            "وتقبلوا فائق الاحترام والتقدير",
            "",
            "إدارة الموارد البشرية",
            "{{اسم_المؤسسة}}"
        ]
        
        for line in content:
            p = doc.add_paragraph(line)
            p.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        
        # حفظ القالب
        doc.save(basic_template_path)
        print(f"✅ تم إنشاء قالب أساسي: {basic_template_path}")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء القالب الأساسي: {e}")
        return False

def safe_template_initialization():
    """تهيئة آمنة لنظام القوالب بدون نوافذ"""
    print("🔧 تهيئة نظام القوالب...")
    
    # 1. تهيئة مجلد القوالب
    if not initialize_templates_directory():
        return False
    
    # 2. فحص حالة النظام
    status = check_template_system()
    print(f"📋 {status['message']}")
    
    # 3. إنشاء قالب أساسي إذا لم توجد قوالب
    if status['templates_count'] == 0:
        print("📝 محاولة إنشاء قالب أساسي...")
        create_basic_template_if_missing()
    
    return True

if __name__ == "__main__":
    # اختبار الأدوات
    print("🧪 اختبار أدوات القوالب...")
    safe_template_initialization()
    
    # عرض حالة النظام
    status = check_template_system()
    print(f"\n📊 حالة النظام: {status['status']}")
    print(f"📋 الرسالة: {status['message']}")
    if status['templates_list']:
        print("📄 القوالب المتاحة:")
        for template in status['templates_list']:
            print(f"   - {template}")
