#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لوظيفة التصدير
Comprehensive Export Test
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
import tempfile

def test_export_functionality():
    """اختبار شامل لوظيفة التصدير"""
    print("📤 اختبار شامل لوظيفة التصدير")
    print("=" * 60)
    
    try:
        # استيراد النظام
        print("📦 استيراد نظام إدارة الموظفين...")
        import employee_management
        print("✅ تم الاستيراد بنجاح")
        
        # فحص المكتبات المطلوبة
        print("\n📚 فحص المكتبات المطلوبة:")
        
        # فحص openpyxl
        try:
            import openpyxl
            print("   ✅ openpyxl: متوفرة")
        except ImportError:
            print("   ❌ openpyxl: غير متوفرة")
            print("   💡 لتثبيتها: pip install openpyxl")
        
        # فحص python-docx
        try:
            import docx
            print("   ✅ python-docx: متوفرة")
        except ImportError:
            print("   ❌ python-docx: غير متوفرة")
            print("   💡 لتثبيتها: pip install python-docx")
        
        # فحص filedialog
        try:
            from tkinter import filedialog
            print("   ✅ filedialog: متوفرة")
        except ImportError:
            print("   ❌ filedialog: غير متوفرة")
        
        # إنشاء نافذة اختبار
        print("\n🪟 إنشاء نافذة الاختبار...")
        root = tk.Tk()
        root.title("اختبار التصدير الشامل")
        root.geometry("1400x900")
        
        # إنشاء النظام
        print("🔧 إنشاء نظام إدارة الموظفين...")
        current_user = {"username": "export_tester", "name": "مختبر التصدير الشامل"}
        emp_system = employee_management.EmployeeManagementSystem(root, current_user)
        print("✅ تم إنشاء النظام بنجاح")
        
        # فحص الدوال المطلوبة
        print("\n🔍 فحص دوال التصدير:")
        
        functions_to_check = [
            ("export_employees", "دالة التصدير الرئيسية"),
            ("perform_export", "دالة تنفيذ التصدير"),
            ("export_to_excel", "دالة تصدير Excel"),
            ("export_to_word", "دالة تصدير Word"),
            ("preview_export_data", "دالة المعاينة")
        ]
        
        for func_name, description in functions_to_check:
            if hasattr(emp_system, func_name):
                print(f"   ✅ {description}: موجودة")
            else:
                print(f"   ❌ {description}: غير موجودة")
        
        # إنشاء واجهة تفاعلية للاختبار
        create_export_test_interface(root, emp_system)
        
        print("\n🎉 انتهى الاختبار - الواجهة التفاعلية جاهزة")
        print("\n📋 تعليمات الاختبار:")
        print("   • اضغط على 'اختبار قائمة التصدير' لفتح القائمة")
        print("   • جرب 'اختبار تصدير Excel' للتصدير المباشر")
        print("   • جرب 'اختبار تصدير Word' للتصدير المباشر")
        print("   • استخدم 'اختبار المعاينة' لرؤية البيانات")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def create_export_test_interface(root, emp_system):
    """إنشاء واجهة تفاعلية لاختبار التصدير"""
    
    # إطار الاختبار
    test_frame = tk.Frame(root, bg="#f0f8ff")
    test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
    
    # عنوان
    title_label = tk.Label(test_frame, text="📤 اختبار شامل لوظيفة التصدير", 
                          bg="#f0f8ff", fg="#1e3a8a", font=("Arial", 14, "bold"))
    title_label.pack(pady=5)
    
    # تعليمات
    instructions_text = """
🎯 اختبارات التصدير:
• اختبار قائمة التصدير المحسنة
• اختبار تصدير Excel مباشر
• اختبار تصدير Word مباشر
• اختبار معاينة البيانات
• فحص معالجة الأخطاء

🔧 التحسينات المطبقة:
• معالجة أخطاء محسنة مع رسائل واضحة
• تقارير تقدم مفصلة في وحدة التحكم
• التحقق من وجود الملفات بعد الإنشاء
• دعم أفضل للمكتبات المطلوبة
    """
    instructions_label = tk.Label(test_frame, text=instructions_text, bg="#f0f8ff", 
                                 fg="#1e3a8a", font=("Arial", 10), justify=tk.LEFT)
    instructions_label.pack(pady=5)
    
    # متغير لعرض النتائج
    result_var = tk.StringVar()
    result_label = tk.Label(test_frame, textvariable=result_var, bg="#f0f8ff", 
                           fg="#1e3a8a", font=("Arial", 10, "bold"))
    result_label.pack(pady=5)
    
    # أزرار الاختبار
    buttons_frame = tk.Frame(test_frame, bg="#f0f8ff")
    buttons_frame.pack(pady=10)
    
    def test_export_dialog():
        """اختبار قائمة التصدير"""
        print("\n📤 اختبار قائمة التصدير:")
        
        try:
            # فتح قائمة التصدير
            emp_system.export_employees()
            result_var.set("✅ تم فتح قائمة التصدير بنجاح")
            print("   ✅ تم فتح قائمة التصدير بنجاح")
        except Exception as e:
            print(f"   ❌ خطأ في فتح قائمة التصدير: {e}")
            result_var.set(f"❌ خطأ: {e}")
    
    def test_excel_export():
        """اختبار تصدير Excel مباشر"""
        print("\n📊 اختبار تصدير Excel مباشر:")
        
        try:
            # إنشاء ملف مؤقت
            temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
            temp_path = temp_file.name
            temp_file.close()
            
            print(f"   📁 ملف مؤقت: {temp_path}")
            
            # اختبار التصدير
            emp_system.export_to_excel(temp_path)
            
            # فحص الملف
            if os.path.exists(temp_path):
                file_size = os.path.getsize(temp_path)
                print(f"   ✅ تم إنشاء ملف Excel (حجم: {file_size:,} بايت)")
                result_var.set(f"✅ تصدير Excel نجح (حجم: {file_size:,} بايت)")
                
                # حذف الملف المؤقت
                os.unlink(temp_path)
                print("   🗑️ تم حذف الملف المؤقت")
            else:
                print("   ❌ لم يتم إنشاء الملف")
                result_var.set("❌ فشل في إنشاء ملف Excel")
                
        except Exception as e:
            print(f"   ❌ خطأ في تصدير Excel: {e}")
            result_var.set(f"❌ خطأ Excel: {e}")
    
    def test_word_export():
        """اختبار تصدير Word مباشر"""
        print("\n📄 اختبار تصدير Word مباشر:")
        
        try:
            # إنشاء ملف مؤقت
            temp_file = tempfile.NamedTemporaryFile(suffix='.docx', delete=False)
            temp_path = temp_file.name
            temp_file.close()
            
            print(f"   📁 ملف مؤقت: {temp_path}")
            
            # اختبار التصدير
            emp_system.export_to_word(temp_path)
            
            # فحص الملف
            if os.path.exists(temp_path):
                file_size = os.path.getsize(temp_path)
                print(f"   ✅ تم إنشاء ملف Word (حجم: {file_size:,} بايت)")
                result_var.set(f"✅ تصدير Word نجح (حجم: {file_size:,} بايت)")
                
                # حذف الملف المؤقت
                os.unlink(temp_path)
                print("   🗑️ تم حذف الملف المؤقت")
            else:
                print("   ❌ لم يتم إنشاء الملف")
                result_var.set("❌ فشل في إنشاء ملف Word")
                
        except Exception as e:
            print(f"   ❌ خطأ في تصدير Word: {e}")
            result_var.set(f"❌ خطأ Word: {e}")
    
    def test_preview():
        """اختبار المعاينة"""
        print("\n👁️ اختبار المعاينة:")
        
        try:
            # اختبار معاينة Excel
            emp_system.preview_export_data("excel")
            result_var.set("✅ تم فتح معاينة Excel بنجاح")
            print("   ✅ تم فتح معاينة Excel بنجاح")
        except Exception as e:
            print(f"   ❌ خطأ في المعاينة: {e}")
            result_var.set(f"❌ خطأ المعاينة: {e}")
    
    def check_data():
        """فحص البيانات"""
        print("\n📊 فحص البيانات:")
        
        try:
            data_count = len(emp_system.employees_data) if hasattr(emp_system, 'employees_data') else 0
            print(f"   📋 عدد الموظفين: {data_count}")
            
            if data_count > 0:
                sample_emp = emp_system.employees_data[0]
                fields_count = len(sample_emp.keys()) if sample_emp else 0
                print(f"   📝 عدد الحقول: {fields_count}")
                result_var.set(f"📊 البيانات: {data_count} موظف، {fields_count} حقل")
            else:
                print("   ⚠️ لا توجد بيانات")
                result_var.set("⚠️ لا توجد بيانات للتصدير")
                
        except Exception as e:
            print(f"   ❌ خطأ في فحص البيانات: {e}")
            result_var.set(f"❌ خطأ البيانات: {e}")
    
    def check_libraries():
        """فحص المكتبات"""
        print("\n📚 فحص المكتبات:")
        
        libraries = []
        
        # فحص openpyxl
        try:
            import openpyxl
            libraries.append("✅ Excel")
            print("   ✅ openpyxl: متوفرة")
        except ImportError:
            libraries.append("❌ Excel")
            print("   ❌ openpyxl: غير متوفرة")
        
        # فحص python-docx
        try:
            import docx
            libraries.append("✅ Word")
            print("   ✅ python-docx: متوفرة")
        except ImportError:
            libraries.append("❌ Word")
            print("   ❌ python-docx: غير متوفرة")
        
        result_var.set(" | ".join(libraries))
    
    def show_troubleshooting():
        """عرض دليل استكشاف الأخطاء"""
        troubleshooting = """
🔧 دليل استكشاف أخطاء التصدير:

1. مشكلة "مكتبة غير مثبتة":
   • Excel: pip install openpyxl
   • Word: pip install python-docx

2. مشكلة "لا توجد بيانات":
   • تأكد من وجود موظفين في النظام
   • جرب إضافة موظف تجريبي

3. مشكلة "فشل في الحفظ":
   • تأكد من صلاحيات الكتابة
   • تأكد من عدم فتح الملف في برنامج آخر
   • جرب مجلد مختلف

4. مشكلة "خطأ في التنسيق":
   • تأكد من إصدار المكتبات الحديث
   • أعد تثبيت المكتبات

5. مشكلة "القائمة لا تفتح":
   • تحقق من رسائل الخطأ في وحدة التحكم
   • أعد تشغيل النظام
        """
        
        messagebox.showinfo("دليل استكشاف الأخطاء", troubleshooting)
        result_var.set("📖 تم عرض دليل استكشاف الأخطاء")
    
    # أزرار الاختبار
    tk.Label(buttons_frame, text="اختبارات:", bg="#f0f8ff", 
            font=("Arial", 10, "bold")).pack(side=tk.LEFT)
    
    tk.Button(buttons_frame, text="📤 اختبار قائمة التصدير",
             command=test_export_dialog,
             bg="#3498db", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="📊 اختبار Excel",
             command=test_excel_export,
             bg="#27ae60", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="📄 اختبار Word",
             command=test_word_export,
             bg="#e67e22", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="👁️ اختبار المعاينة",
             command=test_preview,
             bg="#9b59b6", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="📊 فحص البيانات",
             command=check_data,
             bg="#34495e", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="📚 فحص المكتبات",
             command=check_libraries,
             bg="#e74c3c", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="🔧 استكشاف الأخطاء",
             command=show_troubleshooting,
             bg="#95a5a6", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    # معلومات إضافية
    info_frame = tk.Frame(test_frame, bg="#f0f8ff")
    info_frame.pack(pady=5)
    
    info_text = """
💡 ملاحظات:
• جميع الاختبارات تستخدم ملفات مؤقتة
• تحقق من وحدة التحكم لرسائل مفصلة
• الاختبارات آمنة ولا تؤثر على البيانات الأصلية
    """
    info_label = tk.Label(info_frame, text=info_text, bg="#f0f8ff", 
                         fg="#1e3a8a", font=("Arial", 9), justify=tk.LEFT)
    info_label.pack()
    
    # زر إغلاق
    tk.Button(test_frame, text="❌ إغلاق الاختبار", 
             command=root.destroy,
             bg="#95a5a6", fg="white", 
             font=("Arial", 12, "bold")).pack(pady=10)

def main():
    """الدالة الرئيسية"""
    print("📤 اختبار شامل لوظيفة التصدير")
    print("=" * 60)
    
    test_export_functionality()

if __name__ == "__main__":
    main()
