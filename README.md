# 🏢 نظام إدارة الموارد البشرية

نظام شامل ومتطور لإدارة الموارد البشرية مع واجهة مستخدم عربية سهلة الاستخدام.

## ✨ المميزات الرئيسية

### 👥 إدارة الموظفين
- إضافة وتعديل وحذف بيانات الموظفين
- قائمة منسدلة للمسمى الوظيفي (موظف، موظفة، معلم، معلمة)
- بحث متقدم وتصفية البيانات
- استيراد وتصدير البيانات

### 🏖️ نظام الإجازات
- إدارة إجازات الموظفين والموظفات فقط
- فحص الرصيد التلقائي قبل الموافقة
- أنواع إجازات متعددة
- تتبع حالة الإجازات

### 💰 نظام رصيد الإجازات
- حساب تلقائي للرصيد (30 يوم/سنة للموظفين)
- إضافة رصيد يدوي
- تمييز بين الموظفين والمعلمين
- تقارير مفصلة للأرصدة

### ⬆️ نظام الترقيات
- حساب الترقيات المستحقة
- تتبع تواريخ الترقيات
- تقارير الترقيات

### 📊 التقارير والإحصائيات
- تقارير شاملة للموظفين
- إحصائيات الإجازات
- تقارير الأرصدة
- تصدير التقارير

### 🎨 مدير قوالب المراسلات
- إنشاء وتعديل قوالب المراسلات
- دمج البيانات تلقائياً
- طباعة المراسلات

### 🔐 إدارة المستخدمين
- نظام تسجيل دخول آمن
- إدارة صلاحيات المستخدمين
- تشفير كلمات المرور

## 🛠️ متطلبات التشغيل

### المتطلبات الأساسية
```
Python 3.7+
tkinter (مدمج مع Python)
openpyxl
python-docx
```

### المتطلبات الاختيارية
```
tkcalendar (لتحسين اختيار التواريخ)
```

## 📦 التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
pip install openpyxl python-docx tkcalendar
```

### 2. تشغيل النظام
```bash
تشغيل_التصميم_العمودي.bat
```
أو
```bash
python hr_system.py
```

### 3. تسجيل الدخول
- **المستخدم الافتراضي:** `admin`
- **كلمة المرور الافتراضية:** `admin123`

## 🎯 كيفية الاستخدام

### إضافة موظف جديد
1. اضغط على "👥 إدارة الموظفين"
2. اضغط "إضافة موظف"
3. املأ البيانات المطلوبة
4. اختر المسمى الوظيفي من القائمة المنسدلة
5. احفظ البيانات

### إدارة الإجازات
1. اضغط على "🏖️ إدارة الإجازات"
2. اختر الموظف (الموظفون والموظفات فقط)
3. حدد نوع الإجازة والتواريخ
4. سيتم فحص الرصيد تلقائياً

### إدارة الرصيد
1. اضغط على "💰 رصيد الإجازات السنوي"
2. اعرض أرصدة جميع الموظفين
3. أضف رصيد يدوي حسب الحاجة

## 📁 الملفات الرئيسية

```
├── hr_system.py                 # الملف الرئيسي
├── employee_management.py       # إدارة الموظفين
├── leave_management.py          # إدارة الإجازات
├── leave_balance_system.py      # نظام رصيد الإجازات
├── promotion_system.py          # نظام الترقيات
├── reports_system.py            # نظام التقارير
├── template_manager.py          # مدير القوالب
├── user_management.py           # إدارة المستخدمين
├── employees_data.xlsx          # بيانات الموظفين
├── leaves_data.xlsx             # بيانات الإجازات
├── leave_balance_data.xlsx      # بيانات الأرصدة
└── users.json                   # بيانات المستخدمين
```

## 🔧 الميزات التقنية

- **واجهة عربية:** دعم كامل للغة العربية
- **تصميم عمودي:** استغلال أمثل للمساحة (4+3 وحدات)
- **قاعدة بيانات Excel:** سهولة في التعامل والنسخ الاحتياطي
- **نظام أمان:** تشفير كلمات المرور وإدارة الصلاحيات
- **تصدير البيانات:** إمكانية تصدير جميع التقارير
- **نسخ احتياطية:** حفظ تلقائي للبيانات

## 🎨 التصميم

- **الحجم:** 1400×900 (متوافق مع جميع الشاشات)
- **التخطيط:** عمودي - 4 وحدات في الصف الأول، 3 في الثاني
- **الألوان:** تدرجات احترافية مريحة للعين
- **الخطوط:** Arial للوضوح والقراءة

## 🧪 ملفات الاختبار

- `test_job_classification.py` - اختبار تصنيف الوظائف
- `test_balance_save.py` - اختبار حفظ الرصيد
- `test_job_dropdown.py` - اختبار القائمة المنسدلة
- `test_vertical_design.py` - اختبار التصميم العمودي

## 📚 التوثيق

- `دليل_القائمة_المنسدلة.md` - دليل القائمة المنسدلة
- `دليل_النظام_النهائي.md` - الدليل الشامل

## 📞 الدعم

للمساعدة أو الاستفسارات، يرجى مراجعة ملفات التوثيق أو تشغيل ملفات الاختبار للتحقق من صحة العمل.

## 📄 الترخيص

هذا النظام مطور للاستخدام الداخلي في المؤسسات.

---

**🎉 النظام جاهز للاستخدام الفعلي بكفاءة عالية وموثوقية كاملة!**
