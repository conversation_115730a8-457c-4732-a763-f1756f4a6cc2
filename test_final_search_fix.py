#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الإصلاح النهائي للبحث
Final Search Fix Test
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_final_search_fix():
    """اختبار الإصلاح النهائي للبحث"""
    print("🔍 اختبار الإصلاح النهائي للبحث")
    print("=" * 60)
    
    try:
        # استيراد النظام
        print("📦 استيراد نظام إدارة الموظفين...")
        import employee_management
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة اختبار...")
        root = tk.Tk()
        root.title("اختبار الإصلاح النهائي للبحث")
        root.geometry("1200x800")
        
        # إنشاء النظام
        print("🔧 إنشاء نظام إدارة الموظفين...")
        current_user = {"username": "test", "name": "مستخدم اختبار"}
        emp_system = employee_management.EmployeeManagementSystem(root, current_user)
        print("✅ تم إنشاء النظام بنجاح")
        
        # اختبارات المشاكل الشائعة
        problem_cases = [
            ("", "نص فارغ"),
            ("   ", "مسافات فقط"),
            ("أحمد", "نص عادي"),
            ("  أحمد  ", "مسافات في البداية والنهاية"),
            ("أحمد   محمد", "مسافات متعددة في الوسط"),
            ("أحمد محمد", "نص صحيح بمسافة واحدة"),
            ("\tأحمد\t", "تابات"),
            ("أحمد\nمحمد", "سطر جديد"),
            ("  أحمد   محمد   علي  ", "مسافات متعددة ومختلطة")
        ]
        
        print("\n🧪 اختبار المشاكل الشائعة:")
        for i, (test_text, description) in enumerate(problem_cases, 1):
            print(f"\n📝 اختبار {i}: {description}")
            print(f"   النص الأصلي: '{test_text}' (طول: {len(test_text)})")
            
            # تعيين النص
            emp_system.search_var.set(test_text)
            
            # اختبار زر البحث
            print("   🔍 اختبار زر البحث:")
            try:
                # محاكاة منطق زر البحث
                raw_text = emp_system.search_var.get()
                search_term = raw_text.strip()
                search_term = ' '.join(search_term.split())
                
                if len(search_term) == 0:
                    print("     ⚠️ سيظهر تنبيه: الرجاء إدخال نص")
                    result = "تنبيه"
                else:
                    print(f"     ✅ سيبحث عن: '{search_term}'")
                    result = "بحث"
                
                print(f"     النتيجة: {result}")
                
            except Exception as e:
                print(f"     ❌ خطأ: {e}")
            
            # اختبار البحث التلقائي
            print("   🔄 اختبار البحث التلقائي:")
            try:
                # محاكاة منطق البحث التلقائي
                raw_text = emp_system.search_var.get()
                search_term = raw_text.strip()
                search_term = ' '.join(search_term.split())
                
                if len(search_term) == 0:
                    print("     📋 سيعرض جميع البيانات")
                    result = "عرض الكل"
                else:
                    print(f"     🔍 سيبحث عن: '{search_term}'")
                    result = "بحث تلقائي"
                
                print(f"     النتيجة: {result}")
                
            except Exception as e:
                print(f"     ❌ خطأ: {e}")
        
        print("\n" + "=" * 60)
        print("🎉 انتهى الاختبار - النظام جاهز للاستخدام!")
        
        # إضافة تعليمات للمستخدم
        instructions_frame = tk.Frame(root, bg="#e8f5e8")
        instructions_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
        
        instructions_text = """
✅ تم إصلاح جميع مشاكل البحث! الآن يمكنك:

🔍 زر البحث:
• إذا كان الحقل فارغ أو يحتوي على مسافات فقط → سيظهر تنبيه
• إذا كان هناك نص صالح → سيبحث ويعرض النتائج
• المسافات الزائدة والأحرف الخفية ستُنظف تلقائياً

🔄 البحث التلقائي (أثناء الكتابة):
• إذا كان الحقل فارغ → سيعرض جميع البيانات
• إذا كان هناك نص → سيبحث ويعرض النتائج فوراً
• المسافات الزائدة والأحرف الخفية ستُنظف تلقائياً

🔧 أدوات التشخيص:
• زر "🔍 فحص النص" - لفحص النص بالتفصيل
• زر "🧹 تنظيف النص" - لتنظيف النص يدوياً
• زر "🔧 تشخيص" - لفحص النظام بالكامل
        """
        
        instructions_label = tk.Label(instructions_frame, text=instructions_text,
                                    bg="#e8f5e8", fg="#2c3e50",
                                    font=("Arial", 10), justify=tk.LEFT)
        instructions_label.pack(anchor=tk.W)
        
        # إضافة أزرار اختبار المشاكل الشائعة
        test_frame = tk.Frame(root, bg="#f8f9fa")
        test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=5)
        
        tk.Label(test_frame, text="اختبار المشاكل الشائعة:", 
                bg="#f8f9fa", fg="#2c3e50", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        
        def set_problem_text(text, desc):
            emp_system.search_var.set(text)
            print(f"🧪 تم تعيين نص المشكلة: {desc} - '{text}'")
        
        problem_buttons = [
            ("", "نص فارغ"),
            ("   ", "مسافات فقط"),
            ("  أحمد  ", "مسافات زائدة"),
            ("أحمد   محمد", "مسافات متعددة")
        ]
        
        for text, desc in problem_buttons:
            btn = tk.Button(test_frame, text=desc,
                           command=lambda t=text, d=desc: set_problem_text(t, d),
                           bg="#dc3545", fg="white", font=("Arial", 8))
            btn.pack(side=tk.LEFT, padx=2)
        
        # إضافة أزرار اختبار النصوص الصحيحة
        tk.Label(test_frame, text=" | نصوص صحيحة:", 
                bg="#f8f9fa", fg="#2c3e50", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        
        correct_buttons = [
            ("أحمد", "اسم واحد"),
            ("أحمد محمد", "اسمان"),
            ("001", "رقم وظيفي")
        ]
        
        for text, desc in correct_buttons:
            btn = tk.Button(test_frame, text=desc,
                           command=lambda t=text, d=desc: set_problem_text(t, d),
                           bg="#28a745", fg="white", font=("Arial", 8))
            btn.pack(side=tk.LEFT, padx=2)
        
        # إضافة زر إغلاق سريع
        close_btn = tk.Button(root, text="❌ إغلاق الاختبار", 
                             command=root.destroy,
                             bg="#6c757d", fg="white", 
                             font=("Arial", 12, "bold"))
        close_btn.pack(side=tk.BOTTOM, pady=10)
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def test_text_cleaning_comprehensive():
    """اختبار شامل لتنظيف النص"""
    print("\n🧹 اختبار شامل لتنظيف النص")
    print("-" * 50)
    
    test_cases = [
        ("", "نص فارغ"),
        ("   ", "مسافات فقط"),
        ("أحمد", "نص عادي"),
        ("  أحمد  ", "مسافات في البداية والنهاية"),
        ("أحمد   محمد", "مسافات متعددة في الوسط"),
        ("أحمد محمد", "مسافة واحدة صحيحة"),
        ("\tأحمد\t", "تابات"),
        ("أحمد\nمحمد", "سطر جديد"),
        ("  أحمد   محمد   علي  ", "مسافات متعددة ومختلطة"),
        ("أحمد\t\tمحمد", "تابات متعددة"),
        ("أحمد\n\nمحمد", "أسطر جديدة متعددة")
    ]
    
    print("📝 اختبار منطق التنظيف الجديد:")
    for text, description in test_cases:
        print(f"\n🔍 {description}:")
        print(f"   الأصلي: '{text}' (طول: {len(text)})")
        
        # تنظيف النص بالطريقة الجديدة
        cleaned = text.strip()  # إزالة المسافات من البداية والنهاية
        cleaned = ' '.join(cleaned.split())  # إزالة المسافات المتعددة والأحرف الخفية
        
        print(f"   المنظف: '{cleaned}' (طول: {len(cleaned)})")
        
        # تحديد النتيجة المتوقعة
        if len(cleaned) == 0:
            expected_action = "تنبيه (زر البحث) أو عرض الكل (البحث التلقائي)"
        else:
            expected_action = f"بحث عن: '{cleaned}'"
        
        print(f"   الإجراء المتوقع: {expected_action}")
        
        if text != cleaned:
            print(f"   ✅ تم التنظيف")
        else:
            print(f"   ℹ️ لا يحتاج تنظيف")

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار الإصلاح النهائي لمشكلة البحث")
    print("=" * 60)
    
    # اختبار منطق التنظيف أولاً
    test_text_cleaning_comprehensive()
    
    print("\n" + "=" * 60)
    
    # اختبار النظام الكامل
    response = input("هل تريد اختبار النظام الكامل؟ (y/n): ")
    if response.lower() in ['y', 'yes', 'نعم', 'ن']:
        test_final_search_fix()
    else:
        print("🏁 انتهى الاختبار")

if __name__ == "__main__":
    main()
