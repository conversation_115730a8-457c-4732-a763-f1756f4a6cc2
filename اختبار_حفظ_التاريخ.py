#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار حفظ التاريخ في نافذة اختيار التاريخ
Test Date Saving in Date Selection Window
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
from datetime import datetime, timed<PERSON>ta

def test_date_saving():
    """اختبار حفظ التاريخ"""
    print("💾 اختبار حفظ التاريخ في نافذة اختيار التاريخ")
    print("=" * 60)
    
    try:
        # استيراد قسم الإجازات
        print("📦 استيراد قسم الإجازات...")
        import leave_department_system
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة الاختبار...")
        root = tk.Tk()
        root.title("اختبار حفظ التاريخ")
        root.geometry("900x700")
        root.configure(bg="#f0f8ff")
        
        # إنشاء قسم الإجازات
        print("🔧 إنشاء قسم الإجازات...")
        leave_dept = leave_department_system.LeaveDepartmentSystem(root)
        print("✅ تم إنشاء قسم الإجازات بنجاح")
        
        # إنشاء واجهة اختبار الحفظ
        create_save_test_interface(root, leave_dept)
        
        print("\n🎉 انتهى الاختبار - الواجهة التفاعلية جاهزة")
        print("\n📋 تعليمات الاختبار:")
        print("   • اختبر حفظ التواريخ المختلفة")
        print("   • تحقق من ظهور التاريخ بعد الحفظ")
        print("   • جرب التواريخ الصحيحة والخاطئة")
        print("   • راجع رسائل التأكيد")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def create_save_test_interface(root, leave_dept):
    """إنشاء واجهة اختبار الحفظ"""
    
    # إطار الاختبار
    test_frame = tk.Frame(root, bg="#e8f4fd")
    test_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # عنوان
    title_label = tk.Label(test_frame, text="💾 اختبار حفظ التاريخ", 
                          bg="#e8f4fd", fg="#1565c0", font=("Arial", 18, "bold"))
    title_label.pack(pady=15)
    
    # معلومات الاختبار
    info_frame = tk.LabelFrame(test_frame, text="معلومات الاختبار", 
                              bg="#e8f4fd", fg="#1565c0", font=("Arial", 12, "bold"))
    info_frame.pack(fill=tk.X, pady=10, padx=20)
    
    info_text = """
💾 اختبار حفظ التاريخ:

🔧 التحسينات المطبقة:
• التحقق من وجود جميع القيم قبل الحفظ
• رسائل تأكيد واضحة عند الحفظ
• معالجة أخطاء التاريخ المحسنة
• تحديث فوري للمتغيرات
• طباعة تفصيلية للعمليات

✅ ما يجب أن يحدث:
• عند اختيار التاريخ وضغط "تأكيد الاختيار"
• يجب أن يظهر التاريخ في الحقل
• يجب أن تظهر رسالة تأكيد
• يجب أن تُغلق نافذة اختيار التاريخ
• يجب أن يُطبع التاريخ في وحدة التحكم

🧪 اختبارات مختلفة:
• تواريخ صحيحة
• تواريخ خاطئة
• تواريخ في الماضي والمستقبل
• حالات خاصة (29 فبراير، 31 أبريل...)
    """
    
    info_label = tk.Label(info_frame, text=info_text, bg="#e8f4fd", 
                         fg="#1565c0", font=("Arial", 10), justify=tk.LEFT)
    info_label.pack(padx=10, pady=10)
    
    # إطار اختبار التواريخ
    dates_frame = tk.LabelFrame(test_frame, text="اختبار التواريخ", 
                               bg="#e8f4fd", fg="#1565c0", font=("Arial", 12, "bold"))
    dates_frame.pack(fill=tk.X, pady=10, padx=20)
    
    # متغيرات التاريخ للاختبار
    test_dates = {}
    
    # إنشاء حقول اختبار متعددة
    test_cases = [
        ("تاريخ البدء", "start_date"),
        ("تاريخ الانتهاء", "end_date"),
        ("تاريخ مخصص 1", "custom_date1"),
        ("تاريخ مخصص 2", "custom_date2"),
        ("تاريخ الاختبار", "test_date")
    ]
    
    for i, (label, var_name) in enumerate(test_cases):
        # إطار لكل تاريخ
        date_row = tk.Frame(dates_frame, bg="#e8f4fd")
        date_row.pack(fill=tk.X, pady=8, padx=10)
        
        # تسمية
        tk.Label(date_row, text=f"{label}:", font=("Arial", 11, "bold"), 
                bg="#e8f4fd", fg="#1565c0", width=15, anchor="w").pack(side=tk.LEFT, padx=5)
        
        # متغير التاريخ
        date_var = tk.StringVar()
        test_dates[var_name] = date_var
        
        # حقل عرض التاريخ
        date_entry = tk.Entry(date_row, textvariable=date_var, width=15, 
                             font=("Arial", 11), state="readonly", bg="white")
        date_entry.pack(side=tk.LEFT, padx=5)
        
        # زر اختيار التاريخ
        select_btn = tk.Button(date_row, text="📅 اختيار", 
                              command=lambda dv=date_var: test_date_selection(leave_dept, dv),
                              bg="#3498db", fg="white", font=("Arial", 10, "bold"), 
                              padx=10, pady=3)
        select_btn.pack(side=tk.LEFT, padx=5)
        
        # زر مسح
        clear_btn = tk.Button(date_row, text="🗑️ مسح", 
                             command=lambda dv=date_var: dv.set(""),
                             bg="#e74c3c", fg="white", font=("Arial", 10, "bold"), 
                             padx=10, pady=3)
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        # عرض الحالة
        status_var = tk.StringVar()
        status_label = tk.Label(date_row, textvariable=status_var, 
                               font=("Arial", 9), bg="#e8f4fd", fg="#27ae60", width=20)
        status_label.pack(side=tk.LEFT, padx=5)
        
        # ربط تحديث الحالة
        def update_status(var=date_var, status=status_var):
            if var.get():
                try:
                    datetime.strptime(var.get(), "%Y-%m-%d")
                    status.set("✅ تاريخ صحيح")
                except:
                    status.set("❌ تاريخ خاطئ")
            else:
                status.set("⏳ لم يُحدد")
        
        date_var.trace('w', lambda *args, u=update_status: u())
        update_status()
    
    # إطار النتائج
    results_frame = tk.LabelFrame(test_frame, text="نتائج الاختبار", 
                                 bg="#e8f4fd", fg="#1565c0", font=("Arial", 12, "bold"))
    results_frame.pack(fill=tk.X, pady=10, padx=20)
    
    # منطقة عرض النتائج
    results_text = tk.Text(results_frame, height=8, width=80, font=("Arial", 10),
                          bg="white", fg="#2c3e50", wrap=tk.WORD)
    results_scrollbar = tk.Scrollbar(results_frame, orient=tk.VERTICAL, command=results_text.yview)
    results_text.configure(yscrollcommand=results_scrollbar.set)
    
    results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
    results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def log_result(message):
        """إضافة رسالة لسجل النتائج"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        results_text.insert(tk.END, f"[{timestamp}] {message}\n")
        results_text.see(tk.END)
        results_text.update()
    
    # أزرار الاختبار
    buttons_frame = tk.Frame(test_frame, bg="#e8f4fd")
    buttons_frame.pack(pady=20)
    
    def test_all_dates():
        """اختبار جميع التواريخ"""
        log_result("🧪 بدء اختبار جميع التواريخ...")
        
        valid_count = 0
        invalid_count = 0
        empty_count = 0
        
        for var_name, date_var in test_dates.items():
            date_value = date_var.get()
            if date_value:
                try:
                    datetime.strptime(date_value, "%Y-%m-%d")
                    log_result(f"✅ {var_name}: {date_value} - صحيح")
                    valid_count += 1
                except:
                    log_result(f"❌ {var_name}: {date_value} - خاطئ")
                    invalid_count += 1
            else:
                log_result(f"⏳ {var_name}: فارغ")
                empty_count += 1
        
        log_result(f"📊 النتيجة: {valid_count} صحيح، {invalid_count} خاطئ، {empty_count} فارغ")
    
    def clear_all_dates():
        """مسح جميع التواريخ"""
        for date_var in test_dates.values():
            date_var.set("")
        log_result("🗑️ تم مسح جميع التواريخ")
    
    def set_sample_dates():
        """تعيين تواريخ عينة"""
        today = datetime.now()
        sample_dates = [
            today.strftime("%Y-%m-%d"),
            (today + timedelta(days=7)).strftime("%Y-%m-%d"),
            (today + timedelta(days=30)).strftime("%Y-%m-%d"),
            (today - timedelta(days=7)).strftime("%Y-%m-%d"),
            "2024-12-25"
        ]
        
        for i, (var_name, date_var) in enumerate(test_dates.items()):
            if i < len(sample_dates):
                date_var.set(sample_dates[i])
        
        log_result("📅 تم تعيين تواريخ العينة")
    
    def test_invalid_dates():
        """اختبار تواريخ خاطئة"""
        invalid_dates = [
            "2024-02-30",  # 30 فبراير
            "2024-04-31",  # 31 أبريل
            "2024-13-01",  # شهر 13
            "2024-12-32",  # يوم 32
            "invalid-date"  # تنسيق خاطئ
        ]
        
        log_result("🧪 اختبار التواريخ الخاطئة...")
        
        for i, (var_name, date_var) in enumerate(test_dates.items()):
            if i < len(invalid_dates):
                date_var.set(invalid_dates[i])
                log_result(f"❌ تعيين تاريخ خاطئ لـ {var_name}: {invalid_dates[i]}")
    
    # أزرار الاختبار
    tk.Label(buttons_frame, text="أدوات الاختبار:", bg="#e8f4fd", 
            font=("Arial", 12, "bold")).pack()
    
    buttons_row1 = tk.Frame(buttons_frame, bg="#e8f4fd")
    buttons_row1.pack(pady=5)
    
    tk.Button(buttons_row1, text="🧪 اختبار جميع التواريخ",
             command=test_all_dates,
             bg="#3498db", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row1, text="🗑️ مسح الكل",
             command=clear_all_dates,
             bg="#e74c3c", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row1, text="📅 تواريخ عينة",
             command=set_sample_dates,
             bg="#27ae60", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
    
    buttons_row2 = tk.Frame(buttons_frame, bg="#e8f4fd")
    buttons_row2.pack(pady=5)
    
    tk.Button(buttons_row2, text="❌ تواريخ خاطئة",
             command=test_invalid_dates,
             bg="#e67e22", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
    
    # معلومات إضافية
    info_frame = tk.Frame(test_frame, bg="#e8f4fd")
    info_frame.pack(pady=10)
    
    info_text = """
💡 نصائح الاختبار:
• اضغط "📅 اختيار" لفتح نافذة التاريخ
• اختر التاريخ واضغط "تأكيد الاختيار"
• يجب أن يظهر التاريخ في الحقل
• راجع رسائل التأكيد والأخطاء
• استخدم أدوات الاختبار لاختبارات شاملة
    """
    info_label = tk.Label(info_frame, text=info_text, bg="#e8f4fd", 
                         fg="#1565c0", font=("Arial", 10), justify=tk.LEFT)
    info_label.pack()
    
    # زر إغلاق
    tk.Button(test_frame, text="❌ إغلاق الاختبار", 
             command=root.destroy,
             bg="#95a5a6", fg="white", 
             font=("Arial", 12, "bold")).pack(pady=15)
    
    # رسالة ترحيب
    log_result("🎯 مرحباً بك في اختبار حفظ التاريخ")
    log_result("📋 اضغط على أزرار 'اختيار' لاختبار نافذة التاريخ")

def test_date_selection(leave_dept, date_var):
    """اختبار اختيار التاريخ مع تسجيل مفصل"""
    print(f"\n🧪 بدء اختبار اختيار التاريخ...")
    print(f"📅 القيمة الحالية: '{date_var.get()}'")
    
    # حفظ القيمة السابقة
    old_value = date_var.get()
    
    try:
        # فتح نافذة اختيار التاريخ
        leave_dept.select_date(date_var)
        
        # التحقق من التغيير (سيتم بعد إغلاق النافذة)
        def check_change():
            new_value = date_var.get()
            print(f"📅 القيمة الجديدة: '{new_value}'")
            
            if new_value != old_value:
                print(f"✅ تم تغيير التاريخ من '{old_value}' إلى '{new_value}'")
            else:
                print(f"⚠️ لم يتغير التاريخ: '{new_value}'")
        
        # فحص التغيير بعد فترة قصيرة
        if hasattr(leave_dept, 'window') and leave_dept.window:
            leave_dept.window.after(1000, check_change)
        else:
            # فحص فوري إذا لم تكن النافذة متاحة
            check_change()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار اختيار التاريخ: {e}")

def main():
    """الدالة الرئيسية"""
    print("💾 اختبار حفظ التاريخ")
    print("=" * 60)
    
    test_date_saving()

if __name__ == "__main__":
    main()
