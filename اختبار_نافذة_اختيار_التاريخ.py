#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة اختيار التاريخ المحسنة
Test Enhanced Date Selection Window
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
from datetime import datetime, timed<PERSON><PERSON>

def test_date_selection_window():
    """اختبار نافذة اختيار التاريخ المحسنة"""
    print("📅 اختبار نافذة اختيار التاريخ المحسنة")
    print("=" * 60)
    
    try:
        # استيراد قسم الإجازات
        print("📦 استيراد قسم الإجازات...")
        import leave_department_system
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة الاختبار...")
        root = tk.Tk()
        root.title("اختبار نافذة اختيار التاريخ")
        root.geometry("800x600")
        root.configure(bg="#f0f8ff")
        
        # إنشاء قسم الإجازات
        print("🔧 إنشاء قسم الإجازات...")
        leave_dept = leave_department_system.LeaveDepartmentSystem(root)
        print("✅ تم إنشاء قسم الإجازات بنجاح")
        
        # فحص دالة اختيار التاريخ
        print("\n🔍 فحص دالة اختيار التاريخ:")
        if hasattr(leave_dept, 'select_date'):
            print("   ✅ دالة اختيار التاريخ: موجودة")
        else:
            print("   ❌ دالة اختيار التاريخ: غير موجودة")
        
        # إنشاء واجهة تفاعلية للاختبار
        create_date_test_interface(root, leave_dept)
        
        print("\n🎉 انتهى الاختبار - الواجهة التفاعلية جاهزة")
        print("\n📋 تعليمات الاختبار:")
        print("   • اضغط على أزرار اختبار التاريخ")
        print("   • جرب نافذة اختيار التاريخ المحسنة")
        print("   • اختبر الأزرار السريعة")
        print("   • راجع المعاينة والمعلومات الإضافية")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def create_date_test_interface(root, leave_dept):
    """إنشاء واجهة تفاعلية لاختبار نافذة التاريخ"""
    
    # إطار الاختبار
    test_frame = tk.Frame(root, bg="#e8f4fd")
    test_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # عنوان
    title_label = tk.Label(test_frame, text="📅 اختبار نافذة اختيار التاريخ المحسنة", 
                          bg="#e8f4fd", fg="#1565c0", font=("Arial", 16, "bold"))
    title_label.pack(pady=10)
    
    # معلومات التحسينات
    info_frame = tk.LabelFrame(test_frame, text="التحسينات المطبقة", 
                              bg="#e8f4fd", fg="#1565c0", font=("Arial", 12, "bold"))
    info_frame.pack(fill=tk.X, pady=10, padx=20)
    
    # إحصائيات
    improvements_text = """
📅 تحسينات نافذة اختيار التاريخ:

🎨 التصميم والواجهة:
• نافذة أكبر وأكثر وضوحاً (450x400)
• ألوان متناسقة ومريحة للعين
• تخطيط منظم ومنطقي
• عنوان واضح مع أيقونة

🔧 سهولة الاستخدام:
• قوائم منسدلة للسنة والشهر واليوم
• أسماء الشهور بالعربية (يناير، فبراير...)
• معاينة فورية للتاريخ المختار
• معلومات إضافية (يوم الأسبوع، المسافة من اليوم)

⚡ الأزرار السريعة:
• زر "اليوم" - للتاريخ الحالي
• زر "غداً" - لليوم التالي
• زر "الأسبوع القادم" - بعد 7 أيام

✅ التحقق والأمان:
• التحقق من صحة التاريخ
• رسائل خطأ واضحة
• منع إدخال تواريخ غير صحيحة
• تأكيد قبل الحفظ

🎯 تجربة مستخدم محسنة:
• توسيط النافذة على الشاشة
• تركيز تلقائي على أول عنصر
• أزرار كبيرة وواضحة
• تنسيق موحد للتواريخ
    """
    
    improvements_label = tk.Label(info_frame, text=improvements_text, bg="#e8f4fd", 
                                 fg="#1565c0", font=("Arial", 10), justify=tk.LEFT)
    improvements_label.pack(padx=10, pady=10)
    
    # إطار الاختبار التفاعلي
    interactive_frame = tk.LabelFrame(test_frame, text="اختبار تفاعلي", 
                                     bg="#e8f4fd", fg="#1565c0", font=("Arial", 12, "bold"))
    interactive_frame.pack(fill=tk.X, pady=10, padx=20)
    
    # متغيرات التاريخ للاختبار
    test_date1_var = tk.StringVar()
    test_date2_var = tk.StringVar()
    test_date3_var = tk.StringVar()
    
    # تعيين قيم افتراضية
    today = datetime.now()
    test_date1_var.set(today.strftime("%Y-%m-%d"))
    test_date2_var.set((today + timedelta(days=7)).strftime("%Y-%m-%d"))
    test_date3_var.set((today + timedelta(days=30)).strftime("%Y-%m-%d"))
    
    # صف الاختبار الأول
    row1_frame = tk.Frame(interactive_frame, bg="#e8f4fd")
    row1_frame.pack(fill=tk.X, pady=10, padx=10)
    
    tk.Label(row1_frame, text="تاريخ البدء:", font=("Arial", 11, "bold"), 
            bg="#e8f4fd", fg="#1565c0").pack(side=tk.LEFT, padx=5)
    
    date1_entry = tk.Entry(row1_frame, textvariable=test_date1_var, width=15, 
                          font=("Arial", 11), state="readonly")
    date1_entry.pack(side=tk.LEFT, padx=5)
    
    tk.Button(row1_frame, text="📅 اختيار", 
             command=lambda: leave_dept.select_date(test_date1_var),
             bg="#3498db", fg="white", font=("Arial", 10, "bold"), 
             padx=10, pady=5).pack(side=tk.LEFT, padx=5)
    
    # صف الاختبار الثاني
    row2_frame = tk.Frame(interactive_frame, bg="#e8f4fd")
    row2_frame.pack(fill=tk.X, pady=10, padx=10)
    
    tk.Label(row2_frame, text="تاريخ الانتهاء:", font=("Arial", 11, "bold"), 
            bg="#e8f4fd", fg="#1565c0").pack(side=tk.LEFT, padx=5)
    
    date2_entry = tk.Entry(row2_frame, textvariable=test_date2_var, width=15, 
                          font=("Arial", 11), state="readonly")
    date2_entry.pack(side=tk.LEFT, padx=5)
    
    tk.Button(row2_frame, text="📅 اختيار", 
             command=lambda: leave_dept.select_date(test_date2_var),
             bg="#27ae60", fg="white", font=("Arial", 10, "bold"), 
             padx=10, pady=5).pack(side=tk.LEFT, padx=5)
    
    # صف الاختبار الثالث
    row3_frame = tk.Frame(interactive_frame, bg="#e8f4fd")
    row3_frame.pack(fill=tk.X, pady=10, padx=10)
    
    tk.Label(row3_frame, text="تاريخ مخصص:", font=("Arial", 11, "bold"), 
            bg="#e8f4fd", fg="#1565c0").pack(side=tk.LEFT, padx=5)
    
    date3_entry = tk.Entry(row3_frame, textvariable=test_date3_var, width=15, 
                          font=("Arial", 11), state="readonly")
    date3_entry.pack(side=tk.LEFT, padx=5)
    
    tk.Button(row3_frame, text="📅 اختيار", 
             command=lambda: leave_dept.select_date(test_date3_var),
             bg="#e67e22", fg="white", font=("Arial", 10, "bold"), 
             padx=10, pady=5).pack(side=tk.LEFT, padx=5)
    
    # إطار النتائج
    results_frame = tk.LabelFrame(test_frame, text="نتائج الاختبار", 
                                 bg="#e8f4fd", fg="#1565c0", font=("Arial", 12, "bold"))
    results_frame.pack(fill=tk.X, pady=10, padx=20)
    
    # متغير لعرض النتائج
    result_var = tk.StringVar()
    result_label = tk.Label(results_frame, textvariable=result_var, bg="#e8f4fd", 
                           fg="#1565c0", font=("Arial", 11, "bold"))
    result_label.pack(pady=10)
    
    # أزرار الاختبار
    buttons_frame = tk.Frame(test_frame, bg="#e8f4fd")
    buttons_frame.pack(pady=20)
    
    def test_date_calculations():
        """اختبار حسابات التواريخ"""
        try:
            date1 = test_date1_var.get()
            date2 = test_date2_var.get()
            
            if date1 and date2:
                working_days = leave_dept.calculate_working_days(date1, date2)
                result_var.set(f"📊 أيام العمل من {date1} إلى {date2}: {working_days} يوم")
            else:
                result_var.set("⚠️ الرجاء اختيار التواريخ أولاً")
        except Exception as e:
            result_var.set(f"❌ خطأ في الحساب: {e}")
    
    def test_date_validation():
        """اختبار التحقق من التواريخ"""
        try:
            dates = [test_date1_var.get(), test_date2_var.get(), test_date3_var.get()]
            valid_dates = 0
            
            for date_str in dates:
                if date_str:
                    try:
                        datetime.strptime(date_str, "%Y-%m-%d")
                        valid_dates += 1
                    except:
                        pass
            
            result_var.set(f"✅ التواريخ الصحيحة: {valid_dates} من {len([d for d in dates if d])}")
        except Exception as e:
            result_var.set(f"❌ خطأ في التحقق: {e}")
    
    def show_selected_dates():
        """عرض التواريخ المختارة"""
        dates_info = f"""
📅 التواريخ المختارة:

🟦 تاريخ البدء: {test_date1_var.get() or 'غير محدد'}
🟩 تاريخ الانتهاء: {test_date2_var.get() or 'غير محدد'}  
🟧 تاريخ مخصص: {test_date3_var.get() or 'غير محدد'}

💡 لتغيير أي تاريخ، اضغط على زر "📅 اختيار" المقابل له
        """
        
        messagebox.showinfo("التواريخ المختارة", dates_info)
        result_var.set("📋 تم عرض التواريخ المختارة")
    
    def show_date_features():
        """عرض مميزات نافذة التاريخ"""
        features = """
📅 مميزات نافذة اختيار التاريخ المحسنة:

🎨 التصميم:
   • نافذة كبيرة وواضحة (450x400)
   • ألوان متناسقة ومريحة
   • تخطيط منظم ومنطقي

🔧 سهولة الاستخدام:
   • قوائم منسدلة سهلة الاستخدام
   • أسماء الشهور بالعربية
   • معاينة فورية للتاريخ
   • معلومات إضافية (يوم الأسبوع)

⚡ الأزرار السريعة:
   • "اليوم" - للتاريخ الحالي
   • "غداً" - لليوم التالي  
   • "الأسبوع القادم" - بعد 7 أيام

✅ الأمان والتحقق:
   • التحقق من صحة التاريخ
   • رسائل خطأ واضحة
   • منع التواريخ غير الصحيحة

🎯 تجربة محسنة:
   • توسيط النافذة
   • تركيز تلقائي
   • أزرار كبيرة وواضحة
   • تنسيق موحد (YYYY-MM-DD)

🚀 النتيجة: نافذة اختيار تاريخ احترافية!
        """
        
        messagebox.showinfo("مميزات نافذة التاريخ", features)
        result_var.set("🎯 تم عرض مميزات نافذة التاريخ")
    
    def reset_dates():
        """إعادة تعيين التواريخ"""
        today = datetime.now()
        test_date1_var.set(today.strftime("%Y-%m-%d"))
        test_date2_var.set((today + timedelta(days=7)).strftime("%Y-%m-%d"))
        test_date3_var.set((today + timedelta(days=30)).strftime("%Y-%m-%d"))
        result_var.set("🔄 تم إعادة تعيين التواريخ الافتراضية")
    
    # أزرار الاختبار
    tk.Label(buttons_frame, text="أدوات الاختبار:", bg="#e8f4fd", 
            font=("Arial", 12, "bold")).pack()
    
    buttons_row1 = tk.Frame(buttons_frame, bg="#e8f4fd")
    buttons_row1.pack(pady=5)
    
    tk.Button(buttons_row1, text="📊 حساب أيام العمل",
             command=test_date_calculations,
             bg="#3498db", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row1, text="✅ التحقق من التواريخ",
             command=test_date_validation,
             bg="#27ae60", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row1, text="📋 عرض التواريخ",
             command=show_selected_dates,
             bg="#9b59b6", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
    
    buttons_row2 = tk.Frame(buttons_frame, bg="#e8f4fd")
    buttons_row2.pack(pady=5)
    
    tk.Button(buttons_row2, text="🎯 مميزات النافذة",
             command=show_date_features,
             bg="#e67e22", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row2, text="🔄 إعادة تعيين",
             command=reset_dates,
             bg="#95a5a6", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
    
    # معلومات إضافية
    info_frame = tk.Frame(test_frame, bg="#e8f4fd")
    info_frame.pack(pady=10)
    
    info_text = """
💡 نصائح الاستخدام:
• اضغط على زر "📅 اختيار" لفتح نافذة التاريخ المحسنة
• استخدم الأزرار السريعة للاختيار السريع
• راجع المعاينة قبل التأكيد
• جرب التواريخ المختلفة لاختبار النافذة
    """
    info_label = tk.Label(info_frame, text=info_text, bg="#e8f4fd", 
                         fg="#1565c0", font=("Arial", 10), justify=tk.LEFT)
    info_label.pack()
    
    # زر إغلاق
    tk.Button(test_frame, text="❌ إغلاق الاختبار", 
             command=root.destroy,
             bg="#e74c3c", fg="white", 
             font=("Arial", 12, "bold")).pack(pady=15)
    
    # تعيين نتيجة أولية
    result_var.set("🎯 جاهز للاختبار - اضغط على أزرار اختيار التاريخ")

def main():
    """الدالة الرئيسية"""
    print("📅 اختبار نافذة اختيار التاريخ المحسنة")
    print("=" * 60)
    
    test_date_selection_window()

if __name__ == "__main__":
    main()
