#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إلغاء الحركة الحرة للماوس
Test Mouse Motion Cancellation
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_mouse_motion_cancellation():
    """اختبار إلغاء الحركة الحرة للماوس"""
    print("🚫 اختبار إلغاء الحركة الحرة للماوس")
    print("=" * 60)
    
    try:
        # استيراد النظام
        print("📦 استيراد نظام إدارة الموظفين...")
        import employee_management
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة الاختبار...")
        root = tk.Tk()
        root.title("اختبار إلغاء الحركة الحرة")
        root.geometry("1400x900")
        
        # إنشاء النظام
        print("🔧 إنشاء نظام إدارة الموظفين...")
        current_user = {"username": "motion_tester", "name": "مختبر إلغاء الحركة"}
        emp_system = employee_management.EmployeeManagementSystem(root, current_user)
        print("✅ تم إنشاء النظام بنجاح")
        
        # فحص التحديثات
        print("\n🔍 فحص إلغاء الحركة الحرة:")
        
        # فحص حذف دوال الحركة
        motion_functions = [
            ("on_mouse_motion", "دالة حركة الماوس"),
            ("on_mouse_leave", "دالة مغادرة الماوس")
        ]
        
        for func_name, description in motion_functions:
            if hasattr(emp_system, func_name):
                print(f"   ❌ {description}: ما زالت موجودة (يجب حذفها)")
            else:
                print(f"   ✅ {description}: تم حذفها بنجاح")
        
        # فحص الدوال المطلوبة
        required_functions = [
            ("show_context_menu", "قائمة السياق"),
            ("on_employee_select", "تحديد الموظف"),
            ("on_employee_double_click", "النقر المزدوج")
        ]
        
        for func_name, description in required_functions:
            if hasattr(emp_system, func_name):
                print(f"   ✅ {description}: موجودة")
            else:
                print(f"   ❌ {description}: غير موجودة")
        
        # إنشاء واجهة تفاعلية للاختبار
        create_motion_test_interface(root, emp_system)
        
        print("\n🎉 انتهى الاختبار - الواجهة التفاعلية جاهزة")
        print("\n📋 تعليمات الاختبار:")
        print("   • مرر الماوس فوق الجدول - يجب ألا يحدث تمييز تلقائي")
        print("   • اضغط كليك يسار لتحديد موظف")
        print("   • اضغط كليك يمين لعرض قائمة السياق")
        print("   • انقر مرتين لعرض تفاصيل الموظف")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def create_motion_test_interface(root, emp_system):
    """إنشاء واجهة تفاعلية لاختبار إلغاء الحركة"""
    
    # إطار الاختبار
    test_frame = tk.Frame(root, bg="#fff3e0")
    test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
    
    # عنوان
    title_label = tk.Label(test_frame, text="🚫 اختبار إلغاء الحركة الحرة للماوس", 
                          bg="#fff3e0", fg="#e65100", font=("Arial", 14, "bold"))
    title_label.pack(pady=5)
    
    # تعليمات
    instructions_text = """
🎯 التحديثات المطبقة:
• ❌ إلغاء ربط حركة الماوس (<Motion>) من الجدول
• ❌ حذف دالة on_mouse_motion التي تسبب التمييز التلقائي
• ❌ حذف دالة on_mouse_leave التي تمسح التمييز
• ✅ الاحتفاظ بالكليك اليمين لتحديد الموظف وعرض القائمة
• ✅ تحسين شريط الحالة لإظهار الموظف المحدد

🔧 السلوك الجديد:
• لا يحدث تمييز تلقائي عند تحريك الماوس فوق الجدول
• الكليك اليسار يحدد الموظف ويظهر معلوماته في شريط الحالة
• الكليك اليمين يحدد الموظف ويعرض قائمة السياق
• النقر المزدوج يعرض تفاصيل الموظف
    """
    instructions_label = tk.Label(test_frame, text=instructions_text, bg="#fff3e0", 
                                 fg="#e65100", font=("Arial", 10), justify=tk.LEFT)
    instructions_label.pack(pady=5)
    
    # متغير لعرض النتائج
    result_var = tk.StringVar()
    result_label = tk.Label(test_frame, textvariable=result_var, bg="#fff3e0", 
                           fg="#e65100", font=("Arial", 10, "bold"))
    result_label.pack(pady=5)
    
    # أزرار الاختبار
    buttons_frame = tk.Frame(test_frame, bg="#fff3e0")
    buttons_frame.pack(pady=10)
    
    def check_motion_removal():
        """فحص حذف دوال الحركة"""
        print("\n🔍 فحص حذف دوال الحركة:")
        
        results = []
        
        # فحص دالة حركة الماوس
        if hasattr(emp_system, 'on_mouse_motion'):
            results.append("❌ Motion موجودة")
            print("   ❌ دالة on_mouse_motion ما زالت موجودة")
        else:
            results.append("✅ Motion محذوفة")
            print("   ✅ دالة on_mouse_motion تم حذفها")
        
        # فحص دالة مغادرة الماوس
        if hasattr(emp_system, 'on_mouse_leave'):
            results.append("❌ Leave موجودة")
            print("   ❌ دالة on_mouse_leave ما زالت موجودة")
        else:
            results.append("✅ Leave محذوفة")
            print("   ✅ دالة on_mouse_leave تم حذفها")
        
        # فحص قائمة السياق
        if hasattr(emp_system, 'show_context_menu'):
            results.append("✅ Context متوفرة")
            print("   ✅ دالة show_context_menu متوفرة")
        else:
            results.append("❌ Context مفقودة")
            print("   ❌ دالة show_context_menu مفقودة")
        
        result_var.set(" | ".join(results))
    
    def test_table_interactions():
        """اختبار تفاعلات الجدول"""
        print("\n🖱️ اختبار تفاعلات الجدول:")
        
        interactions = """
🖱️ تعليمات الاختبار التفاعلي:

1. تحريك الماوس:
   • مرر الماوس فوق صفوف الجدول
   • يجب ألا يحدث تمييز تلقائي (لون أزرق)
   • شريط الحالة يجب أن يبقى كما هو

2. الكليك اليسار:
   • اضغط كليك يسار على أي موظف
   • يجب أن يتم تحديد الموظف
   • شريط الحالة يعرض معلومات الموظف

3. الكليك اليمين:
   • اضغط كليك يمين على أي موظف
   • يجب أن يتم تحديد الموظف
   • تظهر قائمة السياق
   • شريط الحالة يعرض "تم تحديد"

4. النقر المزدوج:
   • انقر مرتين على أي موظف
   • تظهر نافذة تفاصيل الموظف

جرب هذه التفاعلات الآن!
        """
        
        messagebox.showinfo("اختبار تفاعلات الجدول", interactions)
        result_var.set("📋 جرب التفاعلات المختلفة مع الجدول")
        print("   📋 تم عرض تعليمات الاختبار التفاعلي")
    
    def test_status_bar_updates():
        """اختبار تحديثات شريط الحالة"""
        print("\n📊 اختبار تحديثات شريط الحالة:")
        
        try:
            # محاولة تحديث شريط الحالة
            total_employees = len(emp_system.employees_data) if hasattr(emp_system, 'employees_data') else 0
            emp_system.update_status(f"📊 إجمالي الموظفين: {total_employees}")
            
            print(f"   ✅ تم تحديث شريط الحالة: {total_employees} موظف")
            result_var.set(f"✅ شريط الحالة يعمل - {total_employees} موظف")
            
        except Exception as e:
            print(f"   ❌ خطأ في شريط الحالة: {e}")
            result_var.set(f"❌ خطأ في شريط الحالة: {e}")
    
    def simulate_interactions():
        """محاكاة التفاعلات"""
        print("\n🎭 محاكاة التفاعلات:")
        
        try:
            # محاولة الحصول على أول موظف
            if hasattr(emp_system, 'employees_data') and emp_system.employees_data:
                first_emp = emp_system.employees_data[0]
                emp_name = first_emp.get("الاسم العربي", "غير محدد")
                emp_id = first_emp.get("الرقم الوظيفي", "غير محدد")
                
                # محاكاة تحديد موظف
                emp_system.update_status(f"👤 محدد: {emp_name} (الرقم الوظيفي: {emp_id}) - انقر مرتين للتفاصيل، كليك يمين للقائمة")
                
                print(f"   ✅ محاكاة تحديد موظف: {emp_name}")
                result_var.set(f"✅ محاكاة تحديد: {emp_name}")
            else:
                print("   ⚠️ لا توجد بيانات موظفين للمحاكاة")
                result_var.set("⚠️ لا توجد بيانات للمحاكاة")
                
        except Exception as e:
            print(f"   ❌ خطأ في المحاكاة: {e}")
            result_var.set(f"❌ خطأ في المحاكاة: {e}")
    
    def show_behavior_comparison():
        """عرض مقارنة السلوك"""
        comparison = """
📊 مقارنة السلوك قبل وبعد التحديث:

🔴 السلوك القديم (مع الحركة الحرة):
   • تحريك الماوس فوق الجدول → تمييز تلقائي (لون أزرق)
   • تغيير شريط الحالة تلقائياً مع كل حركة
   • إزعاج للمستخدم مع الحركة غير المقصودة
   • استهلاك موارد إضافية

🟢 السلوك الجديد (بدون الحركة الحرة):
   • تحريك الماوس فوق الجدول → لا يحدث شيء
   • شريط الحالة يبقى ثابت
   • تحديد الموظف فقط بالكليك المقصود
   • أداء أفضل وتجربة أكثر استقراراً

✅ المزايا:
   • تحكم أفضل من المستخدم
   • عدم التمييز غير المرغوب فيه
   • شريط حالة أكثر استقراراً
   • تركيز على التفاعل المقصود فقط

🎯 النتيجة: تجربة مستخدم محسنة ومستقرة!
        """
        
        messagebox.showinfo("مقارنة السلوك", comparison)
        result_var.set("📊 تم عرض مقارنة السلوك")
    
    def show_usage_guide():
        """عرض دليل الاستخدام الجديد"""
        guide = """
📖 دليل الاستخدام الجديد:

🖱️ تفاعلات الماوس مع الجدول:

1. تحريك الماوس:
   • مرر الماوس فوق الجدول بحرية
   • لن يحدث أي تمييز تلقائي
   • شريط الحالة يبقى ثابت

2. الكليك اليسار (تحديد):
   • اضغط كليك يسار على موظف
   • يتم تحديد الموظف (لون أزرق)
   • شريط الحالة يعرض معلومات الموظف
   • "👤 محدد: [الاسم] (الرقم الوظيفي: [الرقم])"

3. الكليك اليمين (قائمة السياق):
   • اضغط كليك يمين على موظف
   • يتم تحديد الموظف تلقائياً
   • تظهر قائمة السياق مع الخيارات
   • شريط الحالة يعرض "تم تحديد"

4. النقر المزدوج (التفاصيل):
   • انقر مرتين على موظف
   • تظهر نافذة تفاصيل الموظف

5. مسح التحديد:
   • اضغط Esc لمسح التحديد
   • أو اضغط في منطقة فارغة

🎯 النتيجة: تحكم كامل ودقيق في التحديد!
        """
        
        messagebox.showinfo("دليل الاستخدام الجديد", guide)
        result_var.set("📖 تم عرض دليل الاستخدام الجديد")
    
    # أزرار الاختبار
    tk.Label(buttons_frame, text="اختبارات:", bg="#fff3e0", 
            font=("Arial", 10, "bold")).pack(side=tk.LEFT)
    
    tk.Button(buttons_frame, text="🔍 فحص حذف الحركة",
             command=check_motion_removal,
             bg="#f57c00", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="🖱️ اختبار التفاعلات",
             command=test_table_interactions,
             bg="#388e3c", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="📊 اختبار شريط الحالة",
             command=test_status_bar_updates,
             bg="#1976d2", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="🎭 محاكاة التفاعل",
             command=simulate_interactions,
             bg="#7b1fa2", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="📊 مقارنة السلوك",
             command=show_behavior_comparison,
             bg="#d32f2f", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="📖 دليل الاستخدام",
             command=show_usage_guide,
             bg="#455a64", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    # معلومات إضافية
    info_frame = tk.Frame(test_frame, bg="#fff3e0")
    info_frame.pack(pady=5)
    
    info_text = """
💡 ملاحظات:
• تم إلغاء التمييز التلقائي عند تحريك الماوس
• التحديد يحدث فقط بالكليك المقصود
• شريط الحالة أكثر استقراراً
• تجربة مستخدم محسنة ومستقرة
    """
    info_label = tk.Label(info_frame, text=info_text, bg="#fff3e0", 
                         fg="#e65100", font=("Arial", 9), justify=tk.LEFT)
    info_label.pack()
    
    # زر إغلاق
    tk.Button(test_frame, text="❌ إغلاق الاختبار", 
             command=root.destroy,
             bg="#616161", fg="white", 
             font=("Arial", 12, "bold")).pack(pady=10)

def main():
    """الدالة الرئيسية"""
    print("🚫 اختبار إلغاء الحركة الحرة للماوس")
    print("=" * 60)
    
    test_mouse_motion_cancellation()

if __name__ == "__main__":
    main()
