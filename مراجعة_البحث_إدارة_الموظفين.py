#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراجعة شاملة للبحث في إدارة الموظفين
Comprehensive Review of Employee Management Search
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def review_employee_search_system():
    """مراجعة شاملة لنظام البحث في إدارة الموظفين"""
    print("🔍 مراجعة شاملة للبحث في إدارة الموظفين")
    print("=" * 70)
    
    try:
        # استيراد النظام
        print("📦 استيراد نظام إدارة الموظفين...")
        import employee_management
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة المراجعة...")
        root = tk.Tk()
        root.title("مراجعة البحث في إدارة الموظفين")
        root.geometry("1400x900")
        
        # إنشاء النظام
        print("🔧 إنشاء نظام إدارة الموظفين...")
        current_user = {"username": "reviewer", "name": "مراجع النظام"}
        emp_system = employee_management.EmployeeManagementSystem(root, current_user)
        print("✅ تم إنشاء النظام بنجاح")
        
        # مراجعة شاملة للنظام
        print("\n" + "="*70)
        print("📋 مراجعة مكونات نظام البحث")
        print("="*70)
        
        # 1. فحص متغيرات البحث
        print("\n1️⃣ فحص متغيرات البحث:")
        search_vars = [
            ('search_var', 'متغير البحث الرئيسي'),
            ('filter_workplace_var', 'متغير تصفية مكان العمل'),
            ('filter_qualification_var', 'متغير تصفية المؤهل'),
            ('filter_nationality_var', 'متغير تصفية الجنسية')
        ]
        
        for var_name, description in search_vars:
            if hasattr(emp_system, var_name):
                var_obj = getattr(emp_system, var_name)
                if var_obj is not None:
                    print(f"   ✅ {description}: موجود ويعمل")
                    try:
                        current_value = var_obj.get()
                        print(f"      القيمة الحالية: '{current_value}'")
                    except Exception as e:
                        print(f"      ❌ خطأ في قراءة القيمة: {e}")
                else:
                    print(f"   ❌ {description}: موجود لكن قيمته None")
            else:
                print(f"   ❌ {description}: غير موجود")
        
        # 2. فحص دوال البحث
        print("\n2️⃣ فحص دوال البحث:")
        search_functions = [
            ('perform_search', 'دالة البحث الرئيسية (زر البحث)'),
            ('auto_search', 'دالة البحث التلقائي'),
            ('on_search_change', 'معالج تغيير نص البحث'),
            ('apply_filters', 'دالة تطبيق المرشحات'),
            ('reset_filters', 'دالة إعادة تعيين المرشحات'),
            ('debug_search_text', 'دالة فحص النص'),
            ('clean_search_text', 'دالة تنظيف النص'),
            ('debug_search_system', 'دالة تشخيص النظام')
        ]
        
        for func_name, description in search_functions:
            if hasattr(emp_system, func_name):
                func_obj = getattr(emp_system, func_name)
                if callable(func_obj):
                    print(f"   ✅ {description}: موجودة وقابلة للاستدعاء")
                else:
                    print(f"   ❌ {description}: موجودة لكن غير قابلة للاستدعاء")
            else:
                print(f"   ❌ {description}: غير موجودة")
        
        # 3. فحص واجهة البحث
        print("\n3️⃣ فحص واجهة البحث:")
        ui_components = [
            ('employees_table', 'جدول الموظفين'),
            ('filtered_data', 'البيانات المفلترة'),
            ('employees_data', 'بيانات الموظفين الأساسية')
        ]
        
        for comp_name, description in ui_components:
            if hasattr(emp_system, comp_name):
                comp_obj = getattr(emp_system, comp_name)
                if comp_obj is not None:
                    if comp_name.endswith('_data'):
                        print(f"   ✅ {description}: موجود ({len(comp_obj)} عنصر)")
                    else:
                        print(f"   ✅ {description}: موجود ويعمل")
                else:
                    print(f"   ❌ {description}: موجود لكن قيمته None")
            else:
                print(f"   ❌ {description}: غير موجود")
        
        # 4. اختبار سيناريوهات البحث المختلفة
        print("\n4️⃣ اختبار سيناريوهات البحث:")
        test_scenarios = [
            ("", "نص فارغ"),
            ("   ", "مسافات فقط"),
            ("أحمد", "نص عادي"),
            ("  أحمد  ", "نص مع مسافات في البداية والنهاية"),
            ("أحمد   محمد", "نص مع مسافات متعددة"),
            ("001", "رقم وظيفي"),
            ("موظف", "مسمى وظيفي"),
            ("بكالوريوس", "مؤهل"),
            ("ليبي", "جنسية"),
            ("غير موجود", "نص غير موجود"),
            ("أحمد محمد علي", "اسم كامل"),
            ("Ahmed", "نص إنجليزي")
        ]
        
        for i, (test_text, description) in enumerate(test_scenarios, 1):
            print(f"\n   📝 اختبار {i}: {description}")
            print(f"      النص: '{test_text}' (طول: {len(test_text)})")
            
            try:
                # تعيين النص
                emp_system.search_var.set(test_text)
                
                # اختبار التنظيف
                cleaned = test_text.strip()
                cleaned = ' '.join(cleaned.split())
                print(f"      النص بعد التنظيف: '{cleaned}' (طول: {len(cleaned)})")
                
                # تحديد النتيجة المتوقعة
                if len(cleaned) == 0:
                    expected_button = "تنبيه: الرجاء إدخال نص"
                    expected_auto = "عرض جميع البيانات"
                else:
                    expected_button = f"بحث عن: '{cleaned}'"
                    expected_auto = f"بحث تلقائي عن: '{cleaned}'"
                
                print(f"      زر البحث: {expected_button}")
                print(f"      البحث التلقائي: {expected_auto}")
                
                # اختبار فعلي للبحث التلقائي
                try:
                    # محاكاة البحث التلقائي
                    if len(cleaned) > 0:
                        # عد النتائج المتوقعة
                        results_count = 0
                        for emp in emp_system.employees_data:
                            search_fields = ["الرقم الوظيفي", "الاسم العربي", "الاسم الإنجليزي", 
                                           "المسمى الوظيفي", "الرقم الوطني", "المؤهل", 
                                           "مكان العمل الحالي", "التخصص", "الجنسية"]
                            for field in search_fields:
                                value = str(emp.get(field, "")).lower()
                                if cleaned.lower() in value:
                                    results_count += 1
                                    break
                        print(f"      النتائج المتوقعة: {results_count} من أصل {len(emp_system.employees_data)}")
                    else:
                        print(f"      النتائج المتوقعة: عرض جميع البيانات ({len(emp_system.employees_data)})")
                        
                except Exception as e:
                    print(f"      ❌ خطأ في محاكاة البحث: {e}")
                
            except Exception as e:
                print(f"      ❌ خطأ في الاختبار: {e}")
        
        # 5. اختبار الأدوات المساعدة
        print("\n5️⃣ اختبار الأدوات المساعدة:")
        helper_tools = [
            ('debug_search_system', 'أداة تشخيص النظام'),
            ('debug_search_text', 'أداة فحص النص'),
            ('clean_search_text', 'أداة تنظيف النص')
        ]
        
        for tool_name, description in helper_tools:
            print(f"   🔧 {description}:")
            try:
                if hasattr(emp_system, tool_name):
                    print(f"      ✅ متوفرة ويمكن استخدامها")
                else:
                    print(f"      ❌ غير متوفرة")
            except Exception as e:
                print(f"      ❌ خطأ: {e}")
        
        print("\n" + "="*70)
        print("📊 ملخص المراجعة")
        print("="*70)
        
        # إحصائيات النظام
        total_employees = len(emp_system.employees_data) if hasattr(emp_system, 'employees_data') else 0
        print(f"📈 إجمالي الموظفين في النظام: {total_employees}")
        
        # حالة المتغيرات
        vars_ok = all(hasattr(emp_system, var) and getattr(emp_system, var) is not None 
                     for var, _ in search_vars)
        print(f"🔧 حالة متغيرات البحث: {'✅ جميعها تعمل' if vars_ok else '❌ بعضها لا يعمل'}")
        
        # حالة الدوال
        funcs_ok = all(hasattr(emp_system, func) and callable(getattr(emp_system, func)) 
                      for func, _ in search_functions)
        print(f"⚙️ حالة دوال البحث: {'✅ جميعها متوفرة' if funcs_ok else '❌ بعضها غير متوفر'}")
        
        # التوصيات
        print(f"\n💡 التوصيات:")
        if vars_ok and funcs_ok and total_employees > 0:
            print("   ✅ النظام يعمل بشكل مثالي")
            print("   ✅ جميع مكونات البحث متوفرة وتعمل")
            print("   ✅ البيانات موجودة ومتاحة للبحث")
            print("   🎯 النظام جاهز للاستخدام الفعلي")
        else:
            print("   ⚠️ هناك بعض المشاكل التي تحتاج إصلاح")
            if not vars_ok:
                print("   🔧 فحص متغيرات البحث")
            if not funcs_ok:
                print("   🔧 فحص دوال البحث")
            if total_employees == 0:
                print("   📊 إضافة بيانات موظفين للاختبار")
        
        # إضافة واجهة تفاعلية للمراجعة
        create_review_interface(root, emp_system)
        
        print("\n🎉 انتهت المراجعة - النافذة التفاعلية جاهزة للاستخدام")
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في المراجعة: {e}")
        import traceback
        traceback.print_exc()

def create_review_interface(root, emp_system):
    """إنشاء واجهة تفاعلية للمراجعة"""
    
    # إطار المراجعة
    review_frame = tk.Frame(root, bg="#f8f9fa")
    review_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
    
    # عنوان المراجعة
    title_label = tk.Label(review_frame, text="🔍 مراجعة البحث في إدارة الموظفين", 
                          bg="#f8f9fa", fg="#2c3e50", font=("Arial", 14, "bold"))
    title_label.pack(pady=5)
    
    # أزرار الاختبار السريع
    buttons_frame = tk.Frame(review_frame, bg="#f8f9fa")
    buttons_frame.pack(pady=5)
    
    def test_search(text, desc):
        emp_system.search_var.set(text)
        print(f"🧪 اختبار: {desc} - '{text}'")
    
    def test_button_search():
        try:
            emp_system.perform_search()
            print("✅ تم اختبار زر البحث بنجاح")
        except Exception as e:
            print(f"❌ خطأ في زر البحث: {e}")
    
    def test_auto_search():
        try:
            emp_system.auto_search()
            print("✅ تم اختبار البحث التلقائي بنجاح")
        except Exception as e:
            print(f"❌ خطأ في البحث التلقائي: {e}")
    
    def run_diagnostics():
        try:
            emp_system.debug_search_system()
            print("✅ تم تشغيل التشخيص بنجاح")
        except Exception as e:
            print(f"❌ خطأ في التشخيص: {e}")
    
    def check_text():
        try:
            emp_system.debug_search_text()
            print("✅ تم فحص النص بنجاح")
        except Exception as e:
            print(f"❌ خطأ في فحص النص: {e}")
    
    def clean_text():
        try:
            emp_system.clean_search_text()
            print("✅ تم تنظيف النص بنجاح")
        except Exception as e:
            print(f"❌ خطأ في تنظيف النص: {e}")
    
    # أزرار اختبار النصوص
    tk.Label(buttons_frame, text="اختبار النصوص:", bg="#f8f9fa", 
            font=("Arial", 10, "bold")).pack(side=tk.LEFT)
    
    test_texts = [
        ("", "فارغ"),
        ("   ", "مسافات"),
        ("أحمد", "عادي"),
        ("  أحمد  ", "مسافات زائدة"),
        ("أحمد   محمد", "مسافات متعددة")
    ]
    
    for text, desc in test_texts:
        btn = tk.Button(buttons_frame, text=desc,
                       command=lambda t=text, d=desc: test_search(t, d),
                       bg="#007bff", fg="white", font=("Arial", 8))
        btn.pack(side=tk.LEFT, padx=2)
    
    # أزرار اختبار الوظائف
    functions_frame = tk.Frame(review_frame, bg="#f8f9fa")
    functions_frame.pack(pady=5)
    
    tk.Label(functions_frame, text="اختبار الوظائف:", bg="#f8f9fa", 
            font=("Arial", 10, "bold")).pack(side=tk.LEFT)
    
    function_buttons = [
        ("🔍 زر البحث", test_button_search, "#28a745"),
        ("🔄 البحث التلقائي", test_auto_search, "#17a2b8"),
        ("🔧 تشخيص", run_diagnostics, "#ffc107"),
        ("📝 فحص النص", check_text, "#6f42c1"),
        ("🧹 تنظيف النص", clean_text, "#20c997")
    ]
    
    for text, command, color in function_buttons:
        btn = tk.Button(functions_frame, text=text, command=command,
                       bg=color, fg="white", font=("Arial", 9))
        btn.pack(side=tk.LEFT, padx=2)
    
    # زر إغلاق
    close_btn = tk.Button(review_frame, text="❌ إغلاق المراجعة", 
                         command=root.destroy,
                         bg="#dc3545", fg="white", 
                         font=("Arial", 12, "bold"))
    close_btn.pack(pady=10)

def main():
    """الدالة الرئيسية"""
    print("🔍 مراجعة شاملة للبحث في إدارة الموظفين")
    print("=" * 70)
    
    review_employee_search_system()

if __name__ == "__main__":
    main()
