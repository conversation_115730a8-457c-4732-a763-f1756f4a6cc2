#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسينات التحكم بالماوس
Test Mouse Control Enhancements
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_mouse_control_enhancements():
    """اختبار تحسينات التحكم بالماوس"""
    print("🖱️ اختبار تحسينات التحكم بالماوس")
    print("=" * 60)
    
    try:
        # استيراد النظام
        print("📦 استيراد نظام إدارة الموظفين...")
        import employee_management
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة الاختبار...")
        root = tk.Tk()
        root.title("اختبار التحكم بالماوس")
        root.geometry("1400x900")
        
        # إنشاء النظام
        print("🔧 إنشاء نظام إدارة الموظفين...")
        current_user = {"username": "mouse_tester", "name": "مختبر التحكم بالماوس"}
        emp_system = employee_management.EmployeeManagementSystem(root, current_user)
        print("✅ تم إنشاء النظام بنجاح")
        
        # فحص الميزات الجديدة
        print("\n🔍 فحص ميزات التحكم بالماوس:")
        
        # فحص دوال التأثيرات
        features_to_check = [
            ("lighten_color", "تفتيح الألوان"),
            ("add_button_hover_effects", "تأثيرات الأزرار"),
            ("add_table_scroll_effects", "تأثيرات التمرير"),
            ("on_mouse_motion", "حركة الماوس"),
            ("on_mouse_leave", "مغادرة الماوس"),
            ("copy_to_clipboard", "نسخ للحافظة"),
            ("setup_keyboard_shortcuts", "اختصارات المفاتيح"),
            ("focus_search", "التركيز على البحث"),
            ("clear_selection", "مسح التحديد"),
            ("clear_search", "مسح البحث")
        ]
        
        for func_name, description in features_to_check:
            if hasattr(emp_system, func_name):
                print(f"   ✅ {description}: موجود")
            else:
                print(f"   ❌ {description}: غير موجود")
        
        # إنشاء واجهة تفاعلية للاختبار
        create_mouse_test_interface(root, emp_system)
        
        print("\n🎉 انتهى الاختبار - الواجهة التفاعلية جاهزة")
        print("\n📋 تعليمات الاختبار:")
        print("   • مرر الماوس فوق الأزرار لرؤية التأثيرات")
        print("   • مرر الماوس فوق الجدول لرؤية التمييز")
        print("   • استخدم الكليك اليمين على الجدول للقائمة المحسنة")
        print("   • جرب اختصارات لوحة المفاتيح (Ctrl+N, Ctrl+F, إلخ)")
        print("   • استخدم عجلة الماوس للتمرير في الجدول")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def create_mouse_test_interface(root, emp_system):
    """إنشاء واجهة تفاعلية لاختبار التحكم بالماوس"""
    
    # إطار الاختبار
    test_frame = tk.Frame(root, bg="#f0f8ff")
    test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
    
    # عنوان
    title_label = tk.Label(test_frame, text="🖱️ اختبار تحسينات التحكم بالماوس", 
                          bg="#f0f8ff", fg="#1e3a8a", font=("Arial", 14, "bold"))
    title_label.pack(pady=5)
    
    # تعليمات
    instructions_text = """
🎯 الميزات الجديدة:
• تأثيرات الماوس للأزرار (تغيير اللون عند التمرير)
• تمييز الصفوف في الجدول عند التمرير
• قائمة سياق محسنة (كليك يمين)
• تمرير بعجلة الماوس في الجدول
• اختصارات لوحة المفاتيح
• نسخ البيانات للحافظة

🔧 اختصارات لوحة المفاتيح:
Ctrl+N: إضافة موظف | Ctrl+E: تعديل | Ctrl+D: حذف | Ctrl+F: البحث
Ctrl+R: تحديث | Ctrl+P: طباعة | Ctrl+S: تصدير | F5: تحديث | Esc: مسح التحديد
    """
    instructions_label = tk.Label(test_frame, text=instructions_text, bg="#f0f8ff", 
                                 fg="#1e3a8a", font=("Arial", 10), justify=tk.LEFT)
    instructions_label.pack(pady=5)
    
    # متغير لعرض النتائج
    result_var = tk.StringVar()
    result_label = tk.Label(test_frame, textvariable=result_var, bg="#f0f8ff", 
                           fg="#1e3a8a", font=("Arial", 10, "bold"))
    result_label.pack(pady=5)
    
    # أزرار الاختبار
    buttons_frame = tk.Frame(test_frame, bg="#f0f8ff")
    buttons_frame.pack(pady=10)
    
    def test_button_effects():
        """اختبار تأثيرات الأزرار"""
        print("\n🔍 اختبار تأثيرات الأزرار:")
        
        if hasattr(emp_system, 'add_button_hover_effects'):
            print("   ✅ دالة تأثيرات الأزرار موجودة")
            result_var.set("✅ تأثيرات الأزرار متوفرة - مرر الماوس فوق الأزرار")
        else:
            print("   ❌ دالة تأثيرات الأزرار غير موجودة")
            result_var.set("❌ تأثيرات الأزرار غير متوفرة")
    
    def test_table_interactions():
        """اختبار تفاعلات الجدول"""
        print("\n🔍 اختبار تفاعلات الجدول:")
        
        features = [
            ("on_mouse_motion", "حركة الماوس"),
            ("on_mouse_leave", "مغادرة الماوس"),
            ("add_table_scroll_effects", "تأثيرات التمرير")
        ]
        
        results = []
        for func_name, description in features:
            if hasattr(emp_system, func_name):
                results.append(f"✅ {description}")
                print(f"   ✅ {description}: متوفر")
            else:
                results.append(f"❌ {description}")
                print(f"   ❌ {description}: غير متوفر")
        
        result_var.set(" | ".join(results))
    
    def test_keyboard_shortcuts():
        """اختبار اختصارات لوحة المفاتيح"""
        print("\n🔍 اختبار اختصارات لوحة المفاتيح:")
        
        if hasattr(emp_system, 'setup_keyboard_shortcuts'):
            print("   ✅ اختصارات لوحة المفاتيح مُعدة")
            
            # اختبار بعض الاختصارات
            shortcuts = [
                ("focus_search", "Ctrl+F"),
                ("clear_selection", "Esc"),
                ("clear_search", "Ctrl+L")
            ]
            
            available_shortcuts = []
            for func_name, shortcut in shortcuts:
                if hasattr(emp_system, func_name):
                    available_shortcuts.append(shortcut)
            
            result_var.set(f"✅ اختصارات متوفرة: {', '.join(available_shortcuts)}")
            print(f"   ✅ اختصارات متوفرة: {', '.join(available_shortcuts)}")
        else:
            print("   ❌ اختصارات لوحة المفاتيح غير مُعدة")
            result_var.set("❌ اختصارات لوحة المفاتيح غير متوفرة")
    
    def test_context_menu():
        """اختبار قائمة السياق"""
        print("\n🔍 اختبار قائمة السياق:")
        
        if hasattr(emp_system, 'show_context_menu'):
            print("   ✅ قائمة السياق متوفرة")
            
            if hasattr(emp_system, 'copy_to_clipboard'):
                print("   ✅ ميزة النسخ للحافظة متوفرة")
                result_var.set("✅ قائمة السياق محسنة مع ميزة النسخ")
            else:
                print("   ⚠️ ميزة النسخ للحافظة غير متوفرة")
                result_var.set("⚠️ قائمة السياق متوفرة لكن بدون ميزة النسخ")
        else:
            print("   ❌ قائمة السياق غير متوفرة")
            result_var.set("❌ قائمة السياق غير متوفرة")
    
    def test_color_effects():
        """اختبار تأثيرات الألوان"""
        print("\n🔍 اختبار تأثيرات الألوان:")
        
        if hasattr(emp_system, 'lighten_color'):
            print("   ✅ دالة تفتيح الألوان متوفرة")
            
            # اختبار بعض الألوان
            test_colors = ["#27ae60", "#3498db", "#e74c3c"]
            results = []
            
            for color in test_colors:
                try:
                    lighter = emp_system.lighten_color(color)
                    results.append(f"{color}→{lighter}")
                    print(f"   🎨 {color} → {lighter}")
                except Exception as e:
                    print(f"   ❌ خطأ في تفتيح {color}: {e}")
            
            result_var.set(f"✅ تأثيرات الألوان: {', '.join(results[:2])}")
        else:
            print("   ❌ دالة تفتيح الألوان غير متوفرة")
            result_var.set("❌ تأثيرات الألوان غير متوفرة")
    
    def show_usage_tips():
        """عرض نصائح الاستخدام"""
        tips = """
🖱️ نصائح استخدام الماوس:

1. الأزرار:
   • مرر الماوس فوق أي زر لرؤية تأثير التمييز
   • اضغط على الزر لرؤية تأثير الضغط

2. الجدول:
   • مرر الماوس فوق الصفوف لتمييزها
   • استخدم عجلة الماوس للتمرير العمودي
   • استخدم Shift + عجلة الماوس للتمرير الأفقي
   • كليك يمين على أي صف لقائمة الخيارات

3. قائمة السياق:
   • كليك يمين على موظف لعرض القائمة
   • اختر "نسخ الرقم الوظيفي" لنسخه للحافظة

4. اختصارات المفاتيح:
   • Ctrl+F: التركيز على حقل البحث
   • Ctrl+N: إضافة موظف جديد
   • Ctrl+E: تعديل الموظف المحدد
   • Delete: حذف الموظف المحدد
   • F5: تحديث البيانات
   • Esc: مسح التحديد
        """
        
        messagebox.showinfo("نصائح الاستخدام", tips)
        result_var.set("📋 تم عرض نصائح الاستخدام")
    
    # أزرار الاختبار
    tk.Label(buttons_frame, text="اختبارات:", bg="#f0f8ff", 
            font=("Arial", 10, "bold")).pack(side=tk.LEFT)
    
    tk.Button(buttons_frame, text="🔘 تأثيرات الأزرار",
             command=test_button_effects,
             bg="#3498db", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="📋 تفاعلات الجدول",
             command=test_table_interactions,
             bg="#27ae60", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="⌨️ اختصارات المفاتيح",
             command=test_keyboard_shortcuts,
             bg="#9b59b6", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="📋 قائمة السياق",
             command=test_context_menu,
             bg="#e67e22", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="🎨 تأثيرات الألوان",
             command=test_color_effects,
             bg="#e74c3c", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="💡 نصائح الاستخدام",
             command=show_usage_tips,
             bg="#34495e", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    # معلومات إضافية
    info_frame = tk.Frame(test_frame, bg="#f0f8ff")
    info_frame.pack(pady=5)
    
    info_text = """
💡 ملاحظات:
• جرب تمرير الماوس فوق الأزرار والجدول لرؤية التأثيرات
• استخدم الكليك اليمين على الجدول لقائمة محسنة
• جرب اختصارات لوحة المفاتيح المختلفة
    """
    info_label = tk.Label(info_frame, text=info_text, bg="#f0f8ff", 
                         fg="#1e3a8a", font=("Arial", 9), justify=tk.LEFT)
    info_label.pack()
    
    # زر إغلاق
    tk.Button(test_frame, text="❌ إغلاق الاختبار", 
             command=root.destroy,
             bg="#95a5a6", fg="white", 
             font=("Arial", 12, "bold")).pack(pady=10)

def main():
    """الدالة الرئيسية"""
    print("🖱️ اختبار تحسينات التحكم بالماوس")
    print("=" * 60)
    
    test_mouse_control_enhancements()

if __name__ == "__main__":
    main()
