#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة تصدير Word الجديدة
Test New Word Export Feature
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
from datetime import datetime

def test_word_export_feature():
    """اختبار ميزة تصدير Word"""
    print("📄 اختبار ميزة تصدير Word الجديدة")
    print("=" * 60)
    
    try:
        # فحص توفر مكتبة python-docx
        print("📦 فحص توفر مكتبة python-docx...")
        try:
            import docx
            print("✅ مكتبة python-docx متوفرة")
        except ImportError:
            print("❌ مكتبة python-docx غير متوفرة")
            print("💡 لتثبيتها: pip install python-docx")
            return
        
        # استيراد النظام
        print("📦 استيراد نظام إدارة الموظفين...")
        import employee_management
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة الاختبار...")
        root = tk.Tk()
        root.title("اختبار تصدير Word")
        root.geometry("1200x800")
        
        # إنشاء النظام
        print("🔧 إنشاء نظام إدارة الموظفين...")
        current_user = {"username": "word_tester", "name": "مختبر تصدير Word"}
        emp_system = employee_management.EmployeeManagementSystem(root, current_user)
        print("✅ تم إنشاء النظام بنجاح")
        
        # فحص الميزات
        print("\n🔍 فحص ميزات التصدير:")
        
        # فحص وجود دالة تصدير Word
        if hasattr(emp_system, 'export_to_word'):
            print("✅ دالة تصدير Word موجودة")
        else:
            print("❌ دالة تصدير Word غير موجودة")
        
        # فحص عدم وجود دوال CSV وTXT
        csv_exists = hasattr(emp_system, 'export_to_csv')
        txt_exists = hasattr(emp_system, 'export_to_txt')
        
        if not csv_exists:
            print("✅ دالة تصدير CSV محذوفة")
        else:
            print("❌ دالة تصدير CSV ما زالت موجودة")
            
        if not txt_exists:
            print("✅ دالة تصدير TXT محذوفة")
        else:
            print("❌ دالة تصدير TXT ما زالت موجودة")
        
        # فحص الأزرار في واجهة التصدير
        print("\n🔍 فحص واجهة التصدير:")
        
        def test_export_interface():
            """اختبار واجهة التصدير"""
            try:
                # محاكاة فتح نافذة التصدير
                emp_system.export_data()
            except Exception as e:
                print(f"❌ خطأ في فتح واجهة التصدير: {e}")
        
        # إنشاء واجهة تفاعلية للاختبار
        create_word_test_interface(root, emp_system)
        
        print("\n🎉 انتهى الاختبار - الواجهة التفاعلية جاهزة")
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def create_word_test_interface(root, emp_system):
    """إنشاء واجهة تفاعلية لاختبار تصدير Word"""
    
    # إطار الاختبار
    test_frame = tk.Frame(root, bg="#e8f5e8")
    test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
    
    # عنوان
    title_label = tk.Label(test_frame, text="📄 اختبار ميزة تصدير Word الجديدة", 
                          bg="#e8f5e8", fg="#2d5a2d", font=("Arial", 14, "bold"))
    title_label.pack(pady=5)
    
    # تعليمات
    instructions_text = """
✅ الميزات الجديدة:
• تصدير إلى ملف Word (.docx) بدلاً من CSV وTXT
• جدول منسق مع عنوان وتاريخ
• تنسيق احترافي مع ألوان وخطوط
• اسم الملف يتضمن التاريخ تلقائياً

🔧 المتطلبات:
• مكتبة python-docx (pip install python-docx)
    """
    instructions_label = tk.Label(test_frame, text=instructions_text, bg="#e8f5e8", 
                                 fg="#2d5a2d", font=("Arial", 10), justify=tk.LEFT)
    instructions_label.pack(pady=5)
    
    # متغير لعرض النتائج
    result_var = tk.StringVar()
    result_label = tk.Label(test_frame, textvariable=result_var, bg="#e8f5e8", 
                           fg="#2d5a2d", font=("Arial", 10, "bold"))
    result_label.pack(pady=5)
    
    # أزرار الاختبار
    buttons_frame = tk.Frame(test_frame, bg="#e8f5e8")
    buttons_frame.pack(pady=10)
    
    def test_word_function():
        """اختبار دالة تصدير Word"""
        print("\n📄 اختبار دالة تصدير Word:")
        
        if hasattr(emp_system, 'export_to_word'):
            print("   ✅ دالة export_to_word موجودة")
            
            # اختبار إنشاء ملف Word تجريبي
            try:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                test_file = f"test_export_{timestamp}.docx"
                
                print(f"   🔧 إنشاء ملف تجريبي: {test_file}")
                emp_system.export_to_word(test_file)
                
                # فحص وجود الملف
                if os.path.exists(test_file):
                    file_size = os.path.getsize(test_file)
                    print(f"   ✅ تم إنشاء الملف بنجاح (حجم: {file_size} بايت)")
                    result_var.set(f"✅ تم إنشاء ملف Word بنجاح ({file_size} بايت)")
                    
                    # حذف الملف التجريبي
                    os.remove(test_file)
                    print(f"   🗑️ تم حذف الملف التجريبي")
                else:
                    print("   ❌ لم يتم إنشاء الملف")
                    result_var.set("❌ فشل في إنشاء ملف Word")
                    
            except Exception as e:
                print(f"   ❌ خطأ في اختبار التصدير: {e}")
                result_var.set(f"❌ خطأ: {e}")
        else:
            print("   ❌ دالة export_to_word غير موجودة")
            result_var.set("❌ دالة تصدير Word غير موجودة")
    
    def test_export_interface():
        """اختبار واجهة التصدير"""
        print("\n🔍 اختبار واجهة التصدير:")
        
        try:
            # فتح نافذة التصدير
            emp_system.export_data()
            result_var.set("✅ تم فتح واجهة التصدير")
            print("   ✅ تم فتح واجهة التصدير")
        except Exception as e:
            print(f"   ❌ خطأ في فتح واجهة التصدير: {e}")
            result_var.set(f"❌ خطأ في الواجهة: {e}")
    
    def check_removed_functions():
        """فحص الدوال المحذوفة"""
        print("\n🗑️ فحص الدوال المحذوفة:")
        
        results = []
        
        # فحص دالة CSV
        if hasattr(emp_system, 'export_to_csv'):
            results.append("❌ CSV موجودة")
            print("   ❌ دالة export_to_csv ما زالت موجودة")
        else:
            results.append("✅ CSV محذوفة")
            print("   ✅ دالة export_to_csv محذوفة")
        
        # فحص دالة TXT
        if hasattr(emp_system, 'export_to_txt'):
            results.append("❌ TXT موجودة")
            print("   ❌ دالة export_to_txt ما زالت موجودة")
        else:
            results.append("✅ TXT محذوفة")
            print("   ✅ دالة export_to_txt محذوفة")
        
        # فحص دالة Word
        if hasattr(emp_system, 'export_to_word'):
            results.append("✅ Word موجودة")
            print("   ✅ دالة export_to_word موجودة")
        else:
            results.append("❌ Word غير موجودة")
            print("   ❌ دالة export_to_word غير موجودة")
        
        result_var.set(" | ".join(results))
    
    def check_docx_library():
        """فحص مكتبة python-docx"""
        print("\n📦 فحص مكتبة python-docx:")
        
        try:
            import docx
            from docx import Document
            from docx.shared import Inches, Pt
            
            # إنشاء مستند تجريبي
            doc = Document()
            doc.add_heading('اختبار', 0)
            
            print("   ✅ مكتبة python-docx تعمل بشكل صحيح")
            result_var.set("✅ مكتبة python-docx متوفرة وتعمل")
            
        except ImportError:
            print("   ❌ مكتبة python-docx غير مثبتة")
            result_var.set("❌ مكتبة python-docx غير مثبتة")
        except Exception as e:
            print(f"   ❌ خطأ في مكتبة python-docx: {e}")
            result_var.set(f"❌ خطأ في المكتبة: {e}")
    
    def show_sample_filename():
        """عرض مثال على اسم الملف"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        sample_filename = f"employees_export_{timestamp}.docx"
        
        print(f"\n📝 مثال على اسم الملف: {sample_filename}")
        result_var.set(f"مثال: {sample_filename}")
    
    # أزرار الاختبار
    tk.Label(buttons_frame, text="اختبارات:", bg="#e8f5e8", 
            font=("Arial", 10, "bold")).pack(side=tk.LEFT)
    
    tk.Button(buttons_frame, text="📄 اختبار Word",
             command=test_word_function,
             bg="#28a745", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="🔍 اختبار الواجهة",
             command=test_export_interface,
             bg="#007bff", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="🗑️ فحص الحذف",
             command=check_removed_functions,
             bg="#dc3545", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="📦 فحص المكتبة",
             command=check_docx_library,
             bg="#6f42c1", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="📝 مثال الملف",
             command=show_sample_filename,
             bg="#fd7e14", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    # معلومات إضافية
    info_frame = tk.Frame(test_frame, bg="#e8f5e8")
    info_frame.pack(pady=5)
    
    info_text = """
💡 ملاحظات:
• تم استبدال تصدير CSV وTXT بتصدير Word احترافي
• الملف يحتوي على جدول منسق مع عنوان وتاريخ
• اسم الملف يتضمن التاريخ والوقت تلقائياً
    """
    info_label = tk.Label(info_frame, text=info_text, bg="#e8f5e8", 
                         fg="#2d5a2d", font=("Arial", 9), justify=tk.LEFT)
    info_label.pack()
    
    # زر إغلاق
    tk.Button(test_frame, text="❌ إغلاق الاختبار", 
             command=root.destroy,
             bg="#6c757d", fg="white", 
             font=("Arial", 12, "bold")).pack(pady=10)

def main():
    """الدالة الرئيسية"""
    print("📄 اختبار ميزة تصدير Word الجديدة")
    print("=" * 60)
    
    test_word_export_feature()

if __name__ == "__main__":
    main()
