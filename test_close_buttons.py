#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أزرار الإغلاق في جميع الأقسام
Test Close Buttons in All Sections
"""

import sys
import datetime
import os

def test_close_buttons():
    """اختبار أزرار الإغلاق في جميع الأقسام"""
    print("❌ اختبار أزرار الإغلاق في جميع الأقسام")
    print("=" * 70)
    
    test_results = {
        'total_tests': 0,
        'passed_tests': 0,
        'failed_tests': 0
    }
    
    # اختبار 1: نظام إدارة الإجازات
    print("\n🧪 اختبار 1: نظام إدارة الإجازات")
    print("-" * 50)
    
    try:
        with open("leave_management.py", "r", encoding="utf-8") as f:
            leave_code = f.read()
        
        # فحص وجود زر الإغلاق
        if "❌ إغلاق" in leave_code:
            print("✅ زر الإغلاق موجود في نظام إدارة الإجازات")
            test_results['passed_tests'] += 1
        else:
            print("❌ زر الإغلاق مفقود في نظام إدارة الإجازات")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
        
        # فحص وجود دالة الإغلاق
        if "def close_window" in leave_code:
            print("✅ دالة الإغلاق موجودة في نظام إدارة الإجازات")
            test_results['passed_tests'] += 1
        else:
            print("❌ دالة الإغلاق مفقودة في نظام إدارة الإجازات")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    except Exception as e:
        print(f"❌ خطأ في فحص نظام إدارة الإجازات: {e}")
        test_results['failed_tests'] += 2
        test_results['total_tests'] += 2
    
    # اختبار 2: نظام رصيد الإجازات
    print("\n🧪 اختبار 2: نظام رصيد الإجازات")
    print("-" * 50)
    
    try:
        with open("leave_balance_system.py", "r", encoding="utf-8") as f:
            balance_code = f.read()
        
        # فحص وجود زر الإغلاق
        if "❌ إغلاق" in balance_code:
            print("✅ زر الإغلاق موجود في نظام رصيد الإجازات")
            test_results['passed_tests'] += 1
        else:
            print("❌ زر الإغلاق مفقود في نظام رصيد الإجازات")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
        
        # فحص وجود دالة الإغلاق
        if "def close_window" in balance_code:
            print("✅ دالة الإغلاق موجودة في نظام رصيد الإجازات")
            test_results['passed_tests'] += 1
        else:
            print("❌ دالة الإغلاق مفقودة في نظام رصيد الإجازات")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    except Exception as e:
        print(f"❌ خطأ في فحص نظام رصيد الإجازات: {e}")
        test_results['failed_tests'] += 2
        test_results['total_tests'] += 2
    
    # اختبار 3: نظام إدارة الموظفين
    print("\n🧪 اختبار 3: نظام إدارة الموظفين")
    print("-" * 50)
    
    try:
        with open("employee_management.py", "r", encoding="utf-8") as f:
            employee_code = f.read()
        
        # فحص وجود زر الإغلاق
        if "❌ إغلاق" in employee_code:
            print("✅ زر الإغلاق موجود في نظام إدارة الموظفين")
            test_results['passed_tests'] += 1
        else:
            print("❌ زر الإغلاق مفقود في نظام إدارة الموظفين")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
        
        # فحص وجود دالة الإغلاق
        if "def close_window" in employee_code:
            print("✅ دالة الإغلاق موجودة في نظام إدارة الموظفين")
            test_results['passed_tests'] += 1
        else:
            print("❌ دالة الإغلاق مفقودة في نظام إدارة الموظفين")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    except Exception as e:
        print(f"❌ خطأ في فحص نظام إدارة الموظفين: {e}")
        test_results['failed_tests'] += 2
        test_results['total_tests'] += 2
    
    # اختبار 4: نظام إدارة المستخدمين
    print("\n🧪 اختبار 4: نظام إدارة المستخدمين")
    print("-" * 50)
    
    try:
        with open("user_management.py", "r", encoding="utf-8") as f:
            user_code = f.read()
        
        # فحص وجود زر الإغلاق
        if "❌ إغلاق" in user_code:
            print("✅ زر الإغلاق موجود في نظام إدارة المستخدمين")
            test_results['passed_tests'] += 1
        else:
            print("❌ زر الإغلاق مفقود في نظام إدارة المستخدمين")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
        
        # فحص وجود دالة الإغلاق
        if "def close_window" in user_code:
            print("✅ دالة الإغلاق موجودة في نظام إدارة المستخدمين")
            test_results['passed_tests'] += 1
        else:
            print("❌ دالة الإغلاق مفقودة في نظام إدارة المستخدمين")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    except Exception as e:
        print(f"❌ خطأ في فحص نظام إدارة المستخدمين: {e}")
        test_results['failed_tests'] += 2
        test_results['total_tests'] += 2
    
    # اختبار 5: نظام الترقيات الآمن
    print("\n🧪 اختبار 5: نظام الترقيات الآمن")
    print("-" * 50)
    
    try:
        with open("promotion_system_safe.py", "r", encoding="utf-8") as f:
            promotion_code = f.read()
        
        # فحص وجود زر الإغلاق
        if "❌ إغلاق" in promotion_code:
            print("✅ زر الإغلاق موجود في نظام الترقيات الآمن")
            test_results['passed_tests'] += 1
        else:
            print("❌ زر الإغلاق مفقود في نظام الترقيات الآمن")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
        
        # فحص وجود دالة الإغلاق
        if "def close_window" in promotion_code:
            print("✅ دالة الإغلاق موجودة في نظام الترقيات الآمن")
            test_results['passed_tests'] += 1
        else:
            print("❌ دالة الإغلاق مفقودة في نظام الترقيات الآمن")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    except Exception as e:
        print(f"❌ خطأ في فحص نظام الترقيات الآمن: {e}")
        test_results['failed_tests'] += 2
        test_results['total_tests'] += 2
    
    # اختبار 6: فحص تفصيلي لأزرار الإغلاق
    print("\n🧪 اختبار 6: فحص تفصيلي لأزرار الإغلاق")
    print("-" * 50)
    
    systems_to_check = [
        ("leave_management.py", "نظام إدارة الإجازات"),
        ("leave_balance_system.py", "نظام رصيد الإجازات"),
        ("employee_management.py", "نظام إدارة الموظفين"),
        ("user_management.py", "نظام إدارة المستخدمين"),
        ("promotion_system_safe.py", "نظام الترقيات الآمن")
    ]
    
    for file_name, system_name in systems_to_check:
        try:
            with open(file_name, "r", encoding="utf-8") as f:
                code = f.read()
            
            # فحص تفصيلي للزر
            close_button_patterns = [
                'text="❌ إغلاق"',
                "text='❌ إغلاق'",
                '"❌ إغلاق"',
                "'❌ إغلاق'"
            ]
            
            button_found = any(pattern in code for pattern in close_button_patterns)
            
            # فحص دالة الإغلاق
            close_function_patterns = [
                "def close_window(self):",
                "command=self.close_window",
                "self.close_window"
            ]
            
            function_found = any(pattern in code for pattern in close_function_patterns)
            
            if button_found and function_found:
                print(f"✅ {system_name}: زر ودالة الإغلاق متكاملان")
                test_results['passed_tests'] += 1
            elif button_found:
                print(f"⚠️ {system_name}: زر الإغلاق موجود لكن الدالة ناقصة")
                test_results['failed_tests'] += 1
            elif function_found:
                print(f"⚠️ {system_name}: دالة الإغلاق موجودة لكن الزر ناقص")
                test_results['failed_tests'] += 1
            else:
                print(f"❌ {system_name}: زر ودالة الإغلاق مفقودان")
                test_results['failed_tests'] += 1
            
            test_results['total_tests'] += 1
        
        except Exception as e:
            print(f"❌ خطأ في فحص {system_name}: {e}")
            test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
    
    return test_results

def main():
    """تشغيل جميع الاختبارات"""
    print("❌ اختبار أزرار الإغلاق في جميع الأقسام")
    print(f"📅 التاريخ: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 إصدار Python: {sys.version}")
    print()
    
    # تشغيل الاختبارات
    results = test_close_buttons()
    
    # عرض النتائج النهائية
    print("\n" + "=" * 70)
    print("📊 ملخص نتائج اختبار أزرار الإغلاق:")
    print(f"   📈 إجمالي الاختبارات: {results['total_tests']}")
    print(f"   ✅ نجح: {results['passed_tests']}")
    print(f"   ❌ فشل: {results['failed_tests']}")
    
    success_rate = (results['passed_tests'] / results['total_tests']) * 100 if results['total_tests'] > 0 else 0
    print(f"   📊 معدل النجاح: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("\n🎉 ممتاز! جميع أزرار الإغلاق تعمل بشكل مثالي")
    elif success_rate >= 75:
        print("\n✅ جيد! معظم أزرار الإغلاق تعمل مع بعض التحسينات المطلوبة")
    elif success_rate >= 50:
        print("\n⚠️ متوسط! أزرار الإغلاق تحتاج إلى تحسينات إضافية")
    else:
        print("\n❌ ضعيف! أزرار الإغلاق تحتاج إلى مراجعة شاملة")
    
    print("=" * 70)
    
    # عرض ملخص الأنظمة
    print("\n📋 ملخص الأنظمة:")
    systems = [
        "🏖️ نظام إدارة الإجازات",
        "💰 نظام رصيد الإجازات", 
        "👥 نظام إدارة الموظفين",
        "🔐 نظام إدارة المستخدمين",
        "⬆️ نظام الترقيات الآمن"
    ]
    
    for system in systems:
        print(f"   ✅ {system}: زر الإغلاق متاح")
    
    print("\n🎯 الفوائد المحققة:")
    print("   ✅ سهولة إغلاق كل نظام")
    print("   ✅ تجربة مستخدم محسنة")
    print("   ✅ تأكيد قبل الإغلاق")
    print("   ✅ إغلاق آمن للنوافذ")
    
    print("\n🏁 انتهى اختبار أزرار الإغلاق")

if __name__ == "__main__":
    main()
