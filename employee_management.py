#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الموظفين - الإصدار النهائي المحسن
تم تطويره وتحسينه بواسطة Augment Agent
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import sys
from datetime import datetime, timedelta
import json
import threading

# استيراد أنظمة التحسين الجديدة
try:
    from data_validation import DataValidator, CalculationEngine
    from error_handler import error_handler, backup_manager
    from performance_optimizer import performance_monitor, memory_optimizer
    ENHANCED_FEATURES = True
    print("✅ تم تحميل أنظمة التحسين المتقدمة")
except ImportError as e:
    print(f"⚠️ لم يتم تحميل أنظمة التحسين: {e}")
    ENHANCED_FEATURES = False
import time

# استيراد البيانات المرجعية
try:
    from reference_data import get_bank_names, get_nationalities, get_qualifications, get_work_places
except ImportError:
    print("⚠️ تحذير: لا يمكن استيراد البيانات المرجعية")
    def get_bank_names(): return ["مصرف افتراضي"]
    def get_nationalities(): return ["ليبي", "ليبية"]
    def get_qualifications(): return ["بكالوريوس", "ليسانس"]
    def get_work_places(): return ["مكان عمل افتراضي"]

# استيراد مكتبة Excel
try:
    from openpyxl import Workbook, load_workbook
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    print("❌ خطأ: مكتبة openpyxl غير مثبتة")

class EmployeeManagementSystem:
    """نظام إدارة الموظفين المحسن مع أنظمة التحقق والتحسين"""

    def __init__(self, root, current_user=None):
        """تهيئة النظام مع التحسينات المتقدمة"""
        self.root = root

        # تهيئة أنظمة التحسين
        if ENHANCED_FEATURES:
            self.validator = DataValidator()
            self.calculator = CalculationEngine()
            self.performance_enabled = True
            print("🔧 تم تفعيل أنظمة التحسين المتقدمة")
        else:
            self.validator = None
            self.calculator = None
            self.performance_enabled = False
        self.current_user = current_user or {"username": "admin", "role": "admin"}

        # إعداد الملفات والمجلدات
        self.setup_directories()
        self.setup_files()

        # تهيئة البيانات الأساسية (بدون تحميل البيانات)
        self.initialize_basic_data()

        # تهيئة متغيرات البحث أولاً
        self.initialize_search_vars()

        # إنشاء الواجهة
        self.create_interface()

        # تحميل البيانات بعد إنشاء الواجهة
        self.load_employees_data()

        print("✅ تم تهيئة نظام إدارة الموظفين المحسن بنجاح")

    def setup_directories(self):
        """إنشاء المجلدات المطلوبة"""
        directories = ["data", "exports", "logs", "backups"]
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"📁 تم إنشاء مجلد: {directory}")

    def setup_files(self):
        """إعداد ملفات النظام"""
        self.excel_file = "employees_data.xlsx"
        self.sheet_name = "الموظفين"
        self.log_file = os.path.join("logs", f"employees_{datetime.now().strftime('%Y%m%d')}.log")

        # إنشاء ملف Excel إذا لم يكن موجوداً
        if not os.path.exists(self.excel_file):
            self.create_empty_excel_file()

    def initialize_basic_data(self):
        """تهيئة البيانات الأساسية فقط"""
        # البيانات المرجعية
        self.banks = get_bank_names()
        self.nationalities = get_nationalities()
        self.qualifications = get_qualifications()
        self.work_places = get_work_places()
        self.grades = [str(i) for i in range(1, 15)]

        # بيانات الموظفين
        self.employees_data = []
        self.filtered_data = []
        self.selected_employee = None

        print("✅ تم تهيئة البيانات الأساسية")

    def initialize_search_vars(self):
        """تهيئة متغيرات البحث والتصفية"""
        # متغيرات البحث والتصفية
        self.search_var = tk.StringVar()
        self.filter_workplace_var = tk.StringVar()
        self.filter_qualification_var = tk.StringVar()
        self.filter_nationality_var = tk.StringVar()

        # ربط أحداث البحث والتصفية
        self.search_var.trace('w', self.on_search_change)
        self.filter_workplace_var.trace('w', self.on_filter_change)
        self.filter_qualification_var.trace('w', self.on_filter_change)
        self.filter_nationality_var.trace('w', self.on_filter_change)

    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        # إعداد النافذة الرئيسية
        self.root.title("نظام إدارة الموظفين المحسن")
        self.root.geometry("1400x800")
        self.root.configure(bg="#f0f0f0")

        # شريط الأدوات العلوي
        self.create_toolbar()

        # منطقة البحث والتصفية
        self.create_search_filter_area()

        # جدول الموظفين
        self.create_employees_table()

        # شريط الحالة
        self.create_status_bar()

        print("✅ تم إنشاء واجهة المستخدم")

    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = tk.Frame(self.root, bg="#2c3e50", height=60)
        toolbar_frame.pack(fill=tk.X, padx=5, pady=5)
        toolbar_frame.pack_propagate(False)

        # أزرار العمليات الرئيسية
        buttons_data = [
            ("➕ إضافة موظف", self.add_employee, "#27ae60"),
            ("✏️ تعديل", self.edit_employee, "#3498db"),
            ("🗑️ حذف", self.delete_employee, "#e74c3c"),
            ("📊 تفاصيل", self.show_employee_details, "#9b59b6"),
            ("📤 تصدير", self.export_employees, "#f39c12"),
            ("🔄 تحديث", self.refresh_data, "#34495e"),
            ("📋 نسخ احتياطي", self.create_backup, "#16a085"),
            ("❌ إغلاق", self.close_window, "#e74c3c")
        ]

        for text, command, color in buttons_data:
            btn = tk.Button(
                toolbar_frame,
                text=text,
                command=command,
                bg=color,
                fg="white",
                font=("Arial", 10, "bold"),
                relief=tk.FLAT,
                padx=15,
                pady=8,
                cursor="hand2"
            )
            btn.pack(side=tk.LEFT, padx=5, pady=10)

        # معلومات المستخدم الحالي
        user_info = f"👤 {self.current_user.get('name', self.current_user.get('username', 'مستخدم'))}"
        user_label = tk.Label(
            toolbar_frame,
            text=user_info,
            bg="#2c3e50",
            fg="white",
            font=("Arial", 10)
        )
        user_label.pack(side=tk.RIGHT, padx=10, pady=15)

    def create_search_filter_area(self):
        """إنشاء منطقة البحث والتصفية"""
        search_frame = tk.LabelFrame(self.root, text="🔍 البحث والتصفية", font=("Arial", 12, "bold"))
        search_frame.pack(fill=tk.X, padx=10, pady=5)

        # الصف الأول - البحث
        search_row = tk.Frame(search_frame)
        search_row.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(search_row, text="البحث:", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
        search_entry = tk.Entry(search_row, textvariable=self.search_var, font=("Arial", 10), width=30)
        search_entry.pack(side=tk.LEFT, padx=5)

        tk.Button(
            search_row,
            text="🔍 بحث",
            command=self.force_search,
            bg="#3498db",
            fg="white",
            font=("Arial", 9)
        ).pack(side=tk.LEFT, padx=5)

        tk.Button(
            search_row,
            text="🔄 إعادة تعيين",
            command=self.reset_filters,
            bg="#95a5a6",
            fg="white",
            font=("Arial", 9)
        ).pack(side=tk.LEFT, padx=5)







        # الصف الثاني - التصفية
        filter_row = tk.Frame(search_frame)
        filter_row.pack(fill=tk.X, padx=10, pady=5)

        # تصفية مكان العمل
        tk.Label(filter_row, text="مكان العمل:", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
        workplace_combo = ttk.Combobox(
            filter_row,
            textvariable=self.filter_workplace_var,
            values=["الكل"] + self.work_places,
            state="readonly",
            width=15
        )
        workplace_combo.pack(side=tk.LEFT, padx=5)
        workplace_combo.set("الكل")

        # تصفية المؤهل
        tk.Label(filter_row, text="المؤهل:", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
        qualification_combo = ttk.Combobox(
            filter_row,
            textvariable=self.filter_qualification_var,
            values=["الكل"] + self.qualifications,
            state="readonly",
            width=15
        )
        qualification_combo.pack(side=tk.LEFT, padx=5)
        qualification_combo.set("الكل")

        # تصفية الجنسية
        tk.Label(filter_row, text="الجنسية:", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
        nationality_combo = ttk.Combobox(
            filter_row,
            textvariable=self.filter_nationality_var,
            values=["الكل"] + self.nationalities,
            state="readonly",
            width=15
        )
        nationality_combo.pack(side=tk.LEFT, padx=5)
        nationality_combo.set("الكل")

        # عداد النتائج
        self.results_label = tk.Label(
            filter_row,
            text="عدد النتائج: 0",
            font=("Arial", 10, "bold"),
            fg="#2c3e50"
        )
        self.results_label.pack(side=tk.RIGHT, padx=10)

    def create_employees_table(self):
        """إنشاء جدول الموظفين"""
        # إطار الجدول
        table_frame = tk.LabelFrame(self.root, text="📋 قائمة الموظفين", font=("Arial", 12, "bold"))
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # إنشاء Treeview
        columns = (
            "الرقم الوظيفي", "الاسم العربي", "المسمى الوظيفي",
            "مكان العمل", "المؤهل", "الجنسية", "الدرجة"
        )

        self.employees_table = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # تعريف العناوين
        column_widths = {
            "الرقم الوظيفي": 100,
            "الاسم العربي": 200,
            "المسمى الوظيفي": 150,
            "مكان العمل": 150,
            "المؤهل": 120,
            "الجنسية": 80,
            "الدرجة": 80
        }

        for col in columns:
            self.employees_table.heading(col, text=col, anchor=tk.CENTER)
            self.employees_table.column(col, width=column_widths.get(col, 100), anchor=tk.CENTER)

        # أشرطة التمرير
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.employees_table.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.employees_table.xview)

        self.employees_table.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # تخطيط الجدول
        self.employees_table.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # ربط الأحداث
        self.employees_table.bind("<Double-1>", self.on_employee_double_click)
        self.employees_table.bind("<Button-1>", self.on_employee_select)
        self.employees_table.bind("<Button-3>", self.show_context_menu)

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = tk.Frame(self.root, bg="#34495e", height=30)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        self.status_frame.pack_propagate(False)

        self.status_label = tk.Label(
            self.status_frame,
            text="جاهز",
            bg="#34495e",
            fg="white",
            font=("Arial", 10)
        )
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)

        # معلومات إضافية
        self.info_label = tk.Label(
            self.status_frame,
            text="",
            bg="#34495e",
            fg="white",
            font=("Arial", 10)
        )
        self.info_label.pack(side=tk.RIGHT, padx=10, pady=5)

    def update_status(self, message, info=""):
        """تحديث شريط الحالة"""
        self.status_label.config(text=message)
        self.info_label.config(text=info)
        self.root.update_idletasks()

    def log_action(self, action, details=""):
        """تسجيل العمليات"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            user = self.current_user.get('username', 'unknown')
            log_entry = f"[{timestamp}] {user}: {action} - {details}\n"

            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(log_entry)
        except Exception as e:
            print(f"خطأ في تسجيل العملية: {e}")

    def create_empty_excel_file(self):
        """إنشاء ملف Excel فارغ"""
        if not EXCEL_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة openpyxl غير متاحة")
            return False

        try:
            wb = Workbook()
            ws = wb.active
            ws.title = self.sheet_name

            # العناوين الثابتة
            headers = [
                "الرقم الوظيفي", "الاسم العربي", "الاسم الإنجليزي", "الرقم المالي",
                "الرقم الوطني", "المؤهل", "مكان العمل الحالي", "رقم الحساب",
                "اسم المصرف", "تاريخ أول مباشرة", "الدرجة الحالية", "العلاوة",
                "تاريخ الدرجة الحالية", "التخصص", "المسمى الوظيفي",
                "تاريخ التعيين", "رقم الهاتف", "الجنسية", "تاريخ الميلاد"
            ]
            ws.append(headers)

            wb.save(self.excel_file)
            print(f"✅ تم إنشاء ملف Excel: {self.excel_file}")
            return True

        except Exception as e:
            print(f"❌ خطأ في إنشاء ملف Excel: {e}")
            return False

    def load_employees_data(self):
        """تحميل بيانات الموظفين من Excel"""
        if not EXCEL_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة openpyxl غير متاحة")
            return

        try:
            self.update_status("جاري تحميل بيانات الموظفين...")

            if not os.path.exists(self.excel_file):
                self.create_empty_excel_file()
                self.employees_data = []
                self.update_employees_table()
                self.update_status("تم إنشاء ملف جديد")
                return

            wb = load_workbook(self.excel_file)

            # البحث عن الورقة المناسبة
            if self.sheet_name in wb.sheetnames:
                ws = wb[self.sheet_name]
            elif "الموظفين" in wb.sheetnames:
                ws = wb["الموظفين"]
            elif "Employees" in wb.sheetnames:
                ws = wb["Employees"]
            else:
                ws = wb.active

            # قراءة البيانات
            self.employees_data = []
            if ws.max_row > 1:
                headers = [cell.value for cell in ws[1]]

                for row in ws.iter_rows(min_row=2, values_only=True):
                    if any(row):  # تجاهل الصفوف الفارغة
                        emp_data = {}
                        for i, value in enumerate(row):
                            if i < len(headers) and headers[i]:
                                emp_data[headers[i]] = value if value is not None else ""

                        # التأكد من وجود الحقول الأساسية
                        required_fields = [
                            "الرقم الوظيفي", "الاسم العربي", "الاسم الإنجليزي", "الرقم المالي",
                            "الرقم الوطني", "المؤهل", "مكان العمل الحالي", "رقم الحساب",
                            "اسم المصرف", "تاريخ أول مباشرة", "الدرجة الحالية", "العلاوة",
                            "تاريخ الدرجة الحالية", "التخصص", "المسمى الوظيفي",
                            "تاريخ التعيين", "رقم الهاتف", "الجنسية", "تاريخ الميلاد"
                        ]

                        for field in required_fields:
                            if field not in emp_data:
                                emp_data[field] = ""

                        self.employees_data.append(emp_data)

            self.update_employees_table()
            self.update_status(f"تم تحميل {len(self.employees_data)} موظف", f"آخر تحديث: {datetime.now().strftime('%H:%M:%S')}")
            self.log_action("تحميل البيانات", f"تم تحميل {len(self.employees_data)} موظف")

        except Exception as e:
            error_msg = f"خطأ في تحميل بيانات الموظفين: {str(e)}"
            print(f"❌ {error_msg}")
            messagebox.showerror("خطأ", error_msg)
            self.update_status("خطأ في تحميل البيانات")

    def save_employees_data(self):
        """حفظ بيانات الموظفين في Excel"""
        if not EXCEL_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة openpyxl غير متاحة")
            return False

        try:
            self.update_status("جاري حفظ البيانات...")

            # إنشاء نسخة احتياطية
            self.create_backup()

            # إنشاء ملف جديد
            wb = Workbook()
            ws = wb.active
            ws.title = self.sheet_name

            # العناوين الثابتة
            headers = [
                "الرقم الوظيفي", "الاسم العربي", "الاسم الإنجليزي", "الرقم المالي",
                "الرقم الوطني", "المؤهل", "مكان العمل الحالي", "رقم الحساب",
                "اسم المصرف", "تاريخ أول مباشرة", "الدرجة الحالية", "العلاوة",
                "تاريخ الدرجة الحالية", "التخصص", "المسمى الوظيفي",
                "تاريخ التعيين", "رقم الهاتف", "الجنسية", "تاريخ الميلاد"
            ]
            ws.append(headers)

            # كتابة البيانات
            for emp in self.employees_data:
                row_data = []
                for header in headers:
                    value = emp.get(header, "")
                    row_data.append(value)
                ws.append(row_data)

            wb.save(self.excel_file)
            self.update_status(f"تم حفظ {len(self.employees_data)} موظف", f"حُفظ في: {datetime.now().strftime('%H:%M:%S')}")
            self.log_action("حفظ البيانات", f"تم حفظ {len(self.employees_data)} موظف")
            return True

        except Exception as e:
            error_msg = f"خطأ في حفظ البيانات: {str(e)}"
            print(f"❌ {error_msg}")
            messagebox.showerror("خطأ", error_msg)
            self.update_status("خطأ في حفظ البيانات")
            return False

    def update_employees_table(self):
        """تحديث جدول الموظفين"""
        try:
            # التحقق من وجود الجدول قبل التحديث
            if not hasattr(self, 'employees_table') or self.employees_table is None:
                print("⚠️ جدول الموظفين غير جاهز بعد")
                return

            # مسح البيانات الحالية
            for item in self.employees_table.get_children():
                self.employees_table.delete(item)

            # تحديد البيانات المراد عرضها
            # إذا كانت هناك بيانات مفلترة، اعرضها، وإلا اعرض جميع البيانات
            if hasattr(self, 'filtered_data') and self.filtered_data:
                data_to_show = self.filtered_data
                print(f"📊 عرض البيانات المفلترة: {len(data_to_show)} عنصر")
            else:
                data_to_show = self.employees_data
                print(f"📊 عرض جميع البيانات: {len(data_to_show)} عنصر")

            # إضافة البيانات الجديدة
            for i, emp in enumerate(data_to_show):
                try:
                    values = (
                        str(emp.get("الرقم الوظيفي", "")),
                        str(emp.get("الاسم العربي", "")),
                        str(emp.get("المسمى الوظيفي", "")),
                        str(emp.get("مكان العمل الحالي", "")),
                        str(emp.get("المؤهل", "")),
                        str(emp.get("الجنسية", "")),
                        str(emp.get("الدرجة الحالية", ""))
                    )
                    self.employees_table.insert("", tk.END, values=values)
                except Exception as e:
                    print(f"❌ خطأ في إضافة الموظف {i}: {e}")

            # تحديث عداد النتائج
            count = len(data_to_show)
            if hasattr(self, 'results_label') and self.results_label is not None:
                self.results_label.config(text=f"عدد النتائج: {count}")

            print(f"✅ تم تحديث الجدول بنجاح: {count} موظف")

        except Exception as e:
            print(f"❌ خطأ في تحديث الجدول: {e}")
            import traceback
            traceback.print_exc()

    def on_search_change(self, *args):
        """معالج تغيير البحث التلقائي"""
        print("🔄 تغيير في نص البحث")
        self.auto_search()

    def auto_search(self):
        """البحث التلقائي عند الكتابة"""
        try:
            # التحقق من وجود متغير البحث
            if not hasattr(self, 'search_var') or self.search_var is None:
                print("❌ متغير البحث غير موجود")
                return

            # تنظيف شامل للنص
            raw_text = self.search_var.get()
            search_term = raw_text.strip()  # إزالة المسافات من البداية والنهاية
            search_term = ' '.join(search_term.split())  # إزالة المسافات المتعددة والأحرف الخفية
            search_term = search_term.lower()  # تحويل إلى أحرف صغيرة
            print(f"🔍 البحث التلقائي عن: '{search_term}'")

            if not search_term:
                # إذا لم يكن هناك نص بحث، اعرض جميع البيانات (للبحث التلقائي فقط)
                print("📋 لا يوجد نص بحث - عرض جميع البيانات")
                self.filtered_data = []
                self.update_employees_table()
                if hasattr(self, 'employees_data'):
                    self.update_status(f"عرض جميع الموظفين ({len(self.employees_data)})")
                return

            # البحث في الحقول المختلفة
            search_fields = ["الرقم الوظيفي", "الاسم العربي", "الاسم الإنجليزي", "المسمى الوظيفي", "الرقم الوطني"]
            additional_fields = ["المؤهل", "مكان العمل الحالي", "التخصص", "الجنسية"]
            all_fields = search_fields + additional_fields

            filtered = []
            for emp in self.employees_data:
                found = False
                for field in all_fields:
                    value = str(emp.get(field, "")).lower()
                    if search_term in value:
                        filtered.append(emp)
                        found = True
                        print(f"✅ وجد في {field}: {emp.get('الاسم العربي', 'غير محدد')}")
                        break

            print(f"📊 نتائج البحث التلقائي: {len(filtered)} من أصل {len(self.employees_data)}")

            self.filtered_data = filtered
            self.update_employees_table()

            # عرض رسالة النتائج
            if not filtered:
                self.update_status(f"لم يتم العثور على نتائج للبحث: '{self.search_var.get()}'")
            else:
                self.update_status(f"تم العثور على {len(filtered)} نتيجة للبحث: '{self.search_var.get()}'")

        except Exception as e:
            print(f"❌ خطأ في البحث التلقائي: {e}")
            self.update_status("خطأ في البحث")

    def on_filter_change(self, *args):
        """معالج تغيير التصفية"""
        print("🔄 تغيير في المرشحات")
        self.apply_filters()







    def force_search(self):
        """البحث المباشر - بدون فحص أو تنظيف"""
        try:
            print("🚀 بدء البحث المباشر...")

            # الحصول على النص مباشرة
            search_text = ""

            # من متغير البحث
            if hasattr(self, 'search_var') and self.search_var is not None:
                try:
                    search_text = self.search_var.get()
                    print(f"🔍 نص البحث: '{search_text}'")
                except:
                    pass

            # البحث في الواجهة إذا لم نجد النص
            if not search_text:
                try:
                    def find_search_entry(widget):
                        if isinstance(widget, tk.Entry):
                            text = widget.get()
                            if text:
                                return text
                        for child in widget.winfo_children():
                            result = find_search_entry(child)
                            if result:
                                return result
                        return None

                    found_text = find_search_entry(self.root)
                    if found_text:
                        search_text = found_text
                        print(f"🔍 نص من الواجهة: '{search_text}'")
                except Exception as e:
                    print(f"❌ خطأ في البحث في الواجهة: {e}")

            # تنفيذ البحث مباشرة بدون أي فحص أو تنظيف
            if search_text:
                self.execute_search(search_text.lower())
            else:
                print("⚠️ لم يتم العثور على نص للبحث")
                messagebox.showwarning("تنبيه", "الرجاء إدخال نص في حقل البحث")

        except Exception as e:
            print(f"❌ خطأ في البحث المباشر: {e}")
            messagebox.showerror("خطأ", f"خطأ في البحث: {e}")

    def execute_search(self, search_term):
        """تنفيذ البحث المباشر"""
        try:
            print(f"🔍 البحث عن: '{search_term}'")

            # البحث في جميع الحقول
            all_fields = ["الرقم الوظيفي", "الاسم العربي", "الاسم الإنجليزي", "المسمى الوظيفي", "الرقم الوطني", "المؤهل", "مكان العمل الحالي", "التخصص", "الجنسية"]

            filtered = []
            for emp in self.employees_data:
                for field in all_fields:
                    value = str(emp.get(field, "")).lower()
                    if search_term in value:
                        filtered.append(emp)
                        print(f"✅ وجد في {field}: {emp.get('الاسم العربي', 'غير محدد')}")
                        break

            print(f"📊 النتائج: {len(filtered)} من أصل {len(self.employees_data)}")

            self.filtered_data = filtered
            self.update_employees_table()

            # عرض النتائج
            if not filtered:
                self.update_status(f"لا توجد نتائج للبحث: '{search_term}'")
            else:
                self.update_status(f"وجد {len(filtered)} نتيجة للبحث: '{search_term}'")

        except Exception as e:
            print(f"❌ خطأ في البحث: {e}")
            self.update_status("خطأ في البحث")

    def perform_search(self):
        """تنفيذ البحث - إصلاح نهائي لمشكلة الرسالة"""
        try:
            print("🔍 بدء عملية البحث...")

            # التحقق من وجود متغير البحث
            if not hasattr(self, 'search_var') or self.search_var is None:
                print("❌ متغير البحث غير موجود")
                messagebox.showerror("خطأ", "متغير البحث غير موجود")
                return

            # الحصول على النص مباشرة من حقل البحث
            # محاولة الحصول على النص من مصادر متعددة للتأكد
            raw_text = ""

            # الطريقة الأولى: من متغير البحث
            try:
                raw_text = self.search_var.get()
                print(f"🔍 النص من متغير البحث: '{raw_text}' (طول: {len(raw_text)})")
            except Exception as e:
                print(f"❌ خطأ في قراءة متغير البحث: {e}")

            # الطريقة الثانية: البحث عن حقل البحث مباشرة في الواجهة
            if not raw_text:
                try:
                    # البحث عن Entry widget في الواجهة
                    for widget in self.root.winfo_children():
                        if isinstance(widget, tk.LabelFrame) and "البحث" in widget.cget("text"):
                            for child in widget.winfo_children():
                                if isinstance(child, tk.Frame):
                                    for grandchild in child.winfo_children():
                                        if isinstance(grandchild, tk.Entry):
                                            raw_text = grandchild.get()
                                            print(f"🔍 النص من حقل البحث مباشرة: '{raw_text}' (طول: {len(raw_text)})")
                                            break
                except Exception as e:
                    print(f"❌ خطأ في البحث المباشر: {e}")

            print(f"🔍 النص النهائي المحصل عليه: '{raw_text}' (طول: {len(raw_text)})")

            # تنظيف شامل للنص
            if raw_text:
                search_term = raw_text.strip()  # إزالة المسافات من البداية والنهاية
                search_term = ' '.join(search_term.split())  # إزالة المسافات المتعددة والأحرف الخفية
                print(f"🔍 النص بعد التنظيف: '{search_term}' (طول: {len(search_term)})")
            else:
                search_term = ""

            # فحص النص بشكل أكثر تساهلاً
            if not search_term or len(search_term) == 0:
                print("⚠️ لا يوجد نص للبحث")
                self.update_status("الرجاء إدخال نص للبحث")
                messagebox.showwarning("تنبيه", "الرجاء إدخال نص في حقل البحث أولاً")
                return

            # إذا وصلنا هنا، فالنص صالح للبحث
            print(f"✅ النص صالح للبحث: '{search_term}'")
            # تحويل إلى أحرف صغيرة للبحث
            search_term = search_term.lower()
            print(f"🔍 النص النهائي للبحث: '{search_term}'")

            # البحث في الحقول المختلفة
            search_fields = ["الرقم الوظيفي", "الاسم العربي", "الاسم الإنجليزي", "المسمى الوظيفي", "الرقم الوطني"]
            additional_fields = ["المؤهل", "مكان العمل الحالي", "التخصص", "الجنسية"]
            all_fields = search_fields + additional_fields

            filtered = []
            for emp in self.employees_data:
                found = False
                for field in all_fields:
                    value = str(emp.get(field, "")).lower()
                    if search_term in value:
                        filtered.append(emp)
                        found = True
                        print(f"✅ وجد في {field}: {emp.get('الاسم العربي', 'غير محدد')}")
                        break

            print(f"📊 نتائج البحث: {len(filtered)} من أصل {len(self.employees_data)}")

            self.filtered_data = filtered
            self.update_employees_table()

            # عرض رسالة النتائج
            if not filtered:
                self.update_status(f"لم يتم العثور على نتائج للبحث: '{self.search_var.get()}'")
            else:
                self.update_status(f"تم العثور على {len(filtered)} نتيجة للبحث: '{self.search_var.get()}'")

        except Exception as e:
            print(f"❌ خطأ في البحث: {e}")
            self.update_status("خطأ في البحث")

    def apply_filters(self):
        """تطبيق التصفية"""
        try:
            print("🎛️ تطبيق المرشحات...")

            # الحصول على قيم المرشحات
            workplace_filter = self.filter_workplace_var.get() if hasattr(self, 'filter_workplace_var') else "الكل"
            qualification_filter = self.filter_qualification_var.get() if hasattr(self, 'filter_qualification_var') else "الكل"
            nationality_filter = self.filter_nationality_var.get() if hasattr(self, 'filter_nationality_var') else "الكل"

            # تحديد البيانات الأساسية للتصفية
            search_term = self.search_var.get().strip() if hasattr(self, 'search_var') else ""

            # إذا كان هناك بحث، ابدأ من النتائج المفلترة، وإلا من جميع البيانات
            if search_term and self.filtered_data:
                data_to_filter = self.filtered_data.copy()
                print(f"📋 بدء التصفية من نتائج البحث: {len(data_to_filter)} عنصر")
            else:
                data_to_filter = self.employees_data.copy()
                print(f"📋 بدء التصفية من جميع البيانات: {len(data_to_filter)} عنصر")

            # تطبيق تصفية مكان العمل
            if workplace_filter and workplace_filter != "الكل":
                before_count = len(data_to_filter)
                data_to_filter = [emp for emp in data_to_filter if emp.get("مكان العمل الحالي") == workplace_filter]
                print(f"🏢 تصفية مكان العمل '{workplace_filter}': {before_count} → {len(data_to_filter)}")

            # تطبيق تصفية المؤهل
            if qualification_filter and qualification_filter != "الكل":
                before_count = len(data_to_filter)
                data_to_filter = [emp for emp in data_to_filter if emp.get("المؤهل") == qualification_filter]
                print(f"🎓 تصفية المؤهل '{qualification_filter}': {before_count} → {len(data_to_filter)}")

            # تطبيق تصفية الجنسية
            if nationality_filter and nationality_filter != "الكل":
                before_count = len(data_to_filter)
                data_to_filter = [emp for emp in data_to_filter if emp.get("الجنسية") == nationality_filter]
                print(f"🌍 تصفية الجنسية '{nationality_filter}': {before_count} → {len(data_to_filter)}")

            # تحديث البيانات المفلترة
            if search_term:
                # إذا كان هناك بحث، احتفظ بالنتائج المفلترة
                self.filtered_data = data_to_filter
                print(f"🔍 نتائج البحث والتصفية: {len(data_to_filter)}")
            else:
                # إذا لم يكن هناك بحث، تحقق من وجود مرشحات أخرى
                has_filters = any([
                    workplace_filter and workplace_filter != "الكل",
                    qualification_filter and qualification_filter != "الكل",
                    nationality_filter and nationality_filter != "الكل"
                ])

                if has_filters:
                    self.filtered_data = data_to_filter
                    print(f"🎛️ نتائج التصفية فقط: {len(data_to_filter)}")
                else:
                    # لا توجد مرشحات، اعرض جميع البيانات
                    self.filtered_data = []
                    print("📋 لا توجد مرشحات - عرض جميع البيانات")

            self.update_employees_table()

        except Exception as e:
            print(f"❌ خطأ في تطبيق المرشحات: {e}")
            self.update_status("خطأ في تطبيق المرشحات")

    def reset_filters(self):
        """إعادة تعيين جميع المرشحات"""
        self.search_var.set("")
        self.filter_workplace_var.set("الكل")
        self.filter_qualification_var.set("الكل")
        self.filter_nationality_var.set("الكل")
        self.filtered_data = []
        self.update_employees_table()
        self.update_status(f"تم إعادة تعيين المرشحات - عرض جميع الموظفين ({len(self.employees_data)})")

    def on_employee_select(self, event):
        """معالج تحديد موظف"""
        selected_item = self.employees_table.focus()
        if selected_item:
            values = self.employees_table.item(selected_item)["values"]
            if values:
                emp_id = values[0]
                self.selected_employee = self.get_employee_by_id(emp_id)

    def on_employee_double_click(self, event):
        """معالج النقر المزدوج على موظف"""
        self.show_employee_details()

    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        selected_item = self.employees_table.focus()
        if not selected_item:
            return

        context_menu = tk.Menu(self.root, tearoff=0)
        context_menu.add_command(label="📊 عرض التفاصيل", command=self.show_employee_details)
        context_menu.add_command(label="✏️ تعديل", command=self.edit_employee)
        context_menu.add_separator()
        context_menu.add_command(label="🗑️ حذف", command=self.delete_employee)
        context_menu.add_separator()
        context_menu.add_command(label="📄 طباعة بيانات", command=self.print_employee_data)

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def get_employee_by_id(self, emp_id):
        """الحصول على بيانات موظف بالرقم الوظيفي"""
        # تحويل الرقم المطلوب إلى نص للمقارنة
        search_id = str(emp_id).strip()

        for emp in self.employees_data:
            stored_id = str(emp.get("الرقم الوظيفي", "")).strip()
            if stored_id == search_id:
                return emp

        # إذا لم نجد مطابقة مباشرة، نحاول البحث بتنسيقات مختلفة
        # مثل إضافة الأصفار في البداية
        if search_id.isdigit():
            # محاولة البحث بإضافة أصفار
            for zero_count in [2, 3, 4]:  # 001, 0001, إلخ
                padded_id = search_id.zfill(zero_count)
                for emp in self.employees_data:
                    stored_id = str(emp.get("الرقم الوظيفي", "")).strip()
                    if stored_id == padded_id:
                        return emp

        return None

    def add_employee(self):
        """إضافة موظف جديد"""
        self.open_employee_form(mode="add")

    def edit_employee(self):
        """تعديل بيانات موظف"""
        selected_item = self.employees_table.focus()
        if not selected_item:
            messagebox.showwarning("تحذير", "الرجاء تحديد موظف للتعديل")
            return

        values = self.employees_table.item(selected_item)["values"]
        emp_id = values[0]
        employee = self.get_employee_by_id(emp_id)

        if employee:
            self.open_employee_form(mode="edit", employee_data=employee)
        else:
            messagebox.showerror("خطأ", "لم يتم العثور على بيانات الموظف")

    def delete_employee(self):
        """حذف موظف"""
        selected_item = self.employees_table.focus()
        if not selected_item:
            messagebox.showwarning("تحذير", "الرجاء تحديد موظف للحذف")
            return

        values = self.employees_table.item(selected_item)["values"]
        emp_id = values[0]
        emp_name = values[1]

        # تأكيد الحذف
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف الموظف:\n{emp_name} ({emp_id})؟\n\nهذا الإجراء لا يمكن التراجع عنه."
        )

        if result:
            try:
                # البحث عن الموظف وحذفه باستخدام البحث المحسن
                employee_to_delete = self.get_employee_by_id(emp_id)
                if employee_to_delete:
                    for i, emp in enumerate(self.employees_data):
                        if emp is employee_to_delete:
                            deleted_emp = self.employees_data.pop(i)
                            break
                else:
                    messagebox.showerror("خطأ", "لم يتم العثور على الموظف للحذف")
                    return

                # حفظ البيانات
                if self.save_employees_data():
                    self.update_employees_table()
                    self.update_status(f"تم حذف الموظف: {emp_name}")
                    self.log_action("حذف موظف", f"{emp_name} ({emp_id})")
                    messagebox.showinfo("نجح", f"تم حذف الموظف {emp_name} بنجاح")
                else:
                    # إعادة الموظف في حالة فشل الحفظ
                    self.employees_data.insert(i, deleted_emp)
                    messagebox.showerror("خطأ", "فشل في حفظ التغييرات")

            except Exception as e:
                error_msg = f"خطأ في حذف الموظف: {str(e)}"
                print(f"❌ {error_msg}")
                messagebox.showerror("خطأ", error_msg)

    def open_employee_form(self, mode="add", employee_data=None):
        """فتح نموذج إضافة/تعديل موظف"""
        # إنشاء نافذة النموذج
        form_window = tk.Toplevel(self.root)
        form_window.title("إضافة موظف جديد" if mode == "add" else f"تعديل بيانات الموظف")
        form_window.geometry("800x700")
        form_window.resizable(True, True)
        form_window.grab_set()  # جعل النافذة modal

        # إطار التمرير
        canvas = tk.Canvas(form_window)
        scrollbar = ttk.Scrollbar(form_window, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # تخطيط التمرير
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # عنوان النموذج
        title_frame = tk.Frame(scrollable_frame, bg="#3498db", height=60)
        title_frame.pack(fill=tk.X, padx=10, pady=10)
        title_frame.pack_propagate(False)

        title_text = "➕ إضافة موظف جديد" if mode == "add" else "✏️ تعديل بيانات الموظف"
        title_label = tk.Label(
            title_frame,
            text=title_text,
            bg="#3498db",
            fg="white",
            font=("Arial", 16, "bold")
        )
        title_label.pack(expand=True)

        # إطار الحقول
        fields_frame = tk.Frame(scrollable_frame)
        fields_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # تعريف الحقول
        self.form_entries = {}
        fields_config = [
            # (اسم الحقل, نوع الحقل, القيم المتاحة, مطلوب)
            ("الرقم الوظيفي", "entry", None, True),
            ("الاسم العربي", "entry", None, True),
            ("الاسم الإنجليزي", "entry", None, False),
            ("الرقم المالي", "entry", None, False),
            ("الرقم الوطني", "entry", None, True),
            ("المؤهل", "combobox", self.qualifications, False),
            ("مكان العمل الحالي", "combobox", self.work_places, False),
            ("رقم الحساب", "entry", None, False),
            ("اسم المصرف", "combobox", self.banks, False),
            ("تاريخ أول مباشرة", "date", None, False),
            ("الدرجة الحالية", "combobox", self.grades, False),
            ("العلاوة", "entry", None, False),
            ("تاريخ الدرجة الحالية", "date", None, False),
            ("التخصص", "entry", None, False),
            ("المسمى الوظيفي", "combobox", ["موظف", "موظفة", "معلم", "معلمة"], True),
            ("تاريخ التعيين", "date", None, False),
            ("رقم الهاتف", "entry", None, False),
            ("الجنسية", "combobox", self.nationalities, False),
            ("تاريخ الميلاد", "date", None, False)
        ]

        # إنشاء الحقول
        row = 0
        for field_name, field_type, field_values, is_required in fields_config:
            # إطار الحقل
            field_frame = tk.Frame(fields_frame)
            field_frame.grid(row=row//2, column=row%2, sticky="ew", padx=10, pady=5)

            # تسمية الحقل
            label_text = field_name + (" *" if is_required else "")
            label_color = "#e74c3c" if is_required else "#2c3e50"

            label = tk.Label(
                field_frame,
                text=label_text,
                font=("Arial", 10, "bold"),
                fg=label_color,
                anchor="w"
            )
            label.pack(fill=tk.X, pady=(0, 5))

            # إنشاء عنصر الإدخال
            if field_type == "entry":
                widget = tk.Entry(field_frame, font=("Arial", 10), width=25)
            elif field_type == "combobox":
                widget = ttk.Combobox(
                    field_frame,
                    values=field_values or [],
                    font=("Arial", 10),
                    width=23,
                    state="readonly"
                )
            elif field_type == "date":
                widget = tk.Entry(field_frame, font=("Arial", 10), width=25)
                # إضافة نص توضيحي للتاريخ
                widget.insert(0, "YYYY-MM-DD")
                widget.bind("<FocusIn>", lambda e, w=widget: w.delete(0, tk.END) if w.get() == "YYYY-MM-DD" else None)

            widget.pack(fill=tk.X)
            self.form_entries[field_name] = widget

            row += 1

        # تكوين الشبكة
        fields_frame.grid_columnconfigure(0, weight=1)
        fields_frame.grid_columnconfigure(1, weight=1)

        # ملء البيانات في حالة التعديل
        if mode == "edit" and employee_data:
            for field_name, widget in self.form_entries.items():
                value = employee_data.get(field_name, "")
                if isinstance(widget, tk.Entry):
                    widget.delete(0, tk.END)
                    widget.insert(0, str(value))
                elif isinstance(widget, ttk.Combobox):
                    widget.set(str(value))

        # أزرار التحكم
        buttons_frame = tk.Frame(scrollable_frame, bg="#ecf0f1")
        buttons_frame.pack(fill=tk.X, padx=20, pady=20)

        # زر الحفظ
        save_text = "💾 حفظ" if mode == "add" else "💾 تحديث"
        save_btn = tk.Button(
            buttons_frame,
            text=save_text,
            command=lambda: self.save_employee_form(mode, employee_data, form_window),
            bg="#27ae60",
            fg="white",
            font=("Arial", 12, "bold"),
            padx=20,
            pady=10,
            cursor="hand2"
        )
        save_btn.pack(side=tk.RIGHT, padx=10)

        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=form_window.destroy,
            bg="#95a5a6",
            fg="white",
            font=("Arial", 12, "bold"),
            padx=20,
            pady=10,
            cursor="hand2"
        )
        cancel_btn.pack(side=tk.LEFT, padx=10)

        # زر إعادة تعيين (للإضافة فقط)
        if mode == "add":
            reset_btn = tk.Button(
                buttons_frame,
                text="🔄 إعادة تعيين",
                command=lambda: self.reset_form(),
                bg="#f39c12",
                fg="white",
                font=("Arial", 12, "bold"),
                padx=20,
                pady=10,
                cursor="hand2"
            )
            reset_btn.pack(side=tk.RIGHT, padx=10)

        # ربط مفتاح Enter للحفظ
        form_window.bind("<Return>", lambda e: self.save_employee_form(mode, employee_data, form_window))
        form_window.bind("<Escape>", lambda e: form_window.destroy())

        # تركيز على أول حقل
        if self.form_entries:
            first_field = list(self.form_entries.values())[0]
            first_field.focus_set()

    def reset_form(self):
        """إعادة تعيين النموذج"""
        for field_name, widget in self.form_entries.items():
            if isinstance(widget, tk.Entry):
                widget.delete(0, tk.END)
                if "تاريخ" in field_name:
                    widget.insert(0, "YYYY-MM-DD")
            elif isinstance(widget, ttk.Combobox):
                widget.set("")

    def save_employee_form(self, mode, original_data, form_window):
        """حفظ بيانات النموذج مع التحقق المتقدم"""
        try:
            # بدء قياس الأداء
            if self.performance_enabled:
                start_time = performance_monitor.measure_time("save_employee_form")

            # جمع البيانات من النموذج
            emp_data = {}
            for field_name, widget in self.form_entries.items():
                if isinstance(widget, tk.Entry):
                    value = widget.get().strip()
                    # تنظيف قيم التاريخ الافتراضية
                    if value == "YYYY-MM-DD":
                        value = ""
                    emp_data[field_name] = value
                elif isinstance(widget, ttk.Combobox):
                    emp_data[field_name] = widget.get().strip()

            # التحقق المتقدم من البيانات
            if self.performance_enabled and self.validator:
                # استخدام نظام التحقق المتقدم
                is_valid = self.validator.validate_employee_data(emp_data)

                if not is_valid:
                    # عرض رسائل الخطأ
                    error_messages = "\n".join(self.validator.get_all_messages())
                    messagebox.showerror("أخطاء في البيانات", error_messages)

                    # تسجيل الأخطاء
                    error_handler.log_validation("employee_data", str(emp_data), False,
                                               error_messages, self.current_user.get("username", ""))
                    return

                # تسجيل التحقق الناجح
                if self.validator.has_warnings():
                    warning_messages = "\n".join(self.validator.warnings)
                    messagebox.showwarning("تحذيرات", warning_messages)

                error_handler.log_validation("employee_data", str(emp_data), True,
                                           "تم التحقق من البيانات بنجاح", self.current_user.get("username", ""))
            else:
                # التحقق التقليدي من البيانات المطلوبة
                required_fields = ["الرقم الوظيفي", "الاسم العربي", "الرقم الوطني", "المسمى الوظيفي"]
                missing_fields = []

                for field in required_fields:
                    if not emp_data.get(field):
                        missing_fields.append(field)

                if missing_fields:
                    messagebox.showwarning(
                        "بيانات ناقصة",
                        f"الرجاء ملء الحقول المطلوبة:\n" + "\n".join(f"• {field}" for field in missing_fields)
                    )
                    return

            # التحقق من صحة الرقم الوظيفي
            emp_id = emp_data["الرقم الوظيفي"]
            if not emp_id.replace("-", "").replace("_", "").isalnum():
                messagebox.showerror("خطأ", "الرقم الوظيفي يجب أن يحتوي على أرقام وحروف فقط")
                return

            # التحقق من تكرار الرقم الوظيفي
            if mode == "add":
                for existing_emp in self.employees_data:
                    if existing_emp.get("الرقم الوظيفي") == emp_id:
                        messagebox.showerror("خطأ", f"الرقم الوظيفي {emp_id} موجود مسبقاً")
                        return
            elif mode == "edit":
                original_id = original_data.get("الرقم الوظيفي") if original_data else ""
                if emp_id != original_id:
                    for existing_emp in self.employees_data:
                        if existing_emp.get("الرقم الوظيفي") == emp_id:
                            messagebox.showerror("خطأ", f"الرقم الوظيفي {emp_id} موجود مسبقاً")
                            return

            # التحقق من صحة التواريخ
            date_fields = ["تاريخ أول مباشرة", "تاريخ الدرجة الحالية", "تاريخ التعيين", "تاريخ الميلاد"]
            for date_field in date_fields:
                date_value = emp_data.get(date_field, "")
                if date_value and not self.validate_date(date_value):
                    messagebox.showerror("خطأ", f"تنسيق التاريخ غير صحيح في حقل: {date_field}\nالتنسيق المطلوب: YYYY-MM-DD")
                    return

            # حفظ البيانات
            if mode == "add":
                self.employees_data.append(emp_data)
                action_msg = f"تم إضافة الموظف {emp_data['الاسم العربي']} ({emp_id}) بنجاح"
                log_msg = f"إضافة موظف: {emp_data['الاسم العربي']} ({emp_id})"
            else:  # edit
                # البحث عن الموظف وتحديثه
                original_id = original_data.get("الرقم الوظيفي") if original_data else ""
                for i, emp in enumerate(self.employees_data):
                    if emp.get("الرقم الوظيفي") == original_id:
                        self.employees_data[i] = emp_data
                        break
                action_msg = f"تم تحديث بيانات الموظف {emp_data['الاسم العربي']} ({emp_id}) بنجاح"
                log_msg = f"تحديث موظف: {emp_data['الاسم العربي']} ({emp_id})"

            # إنشاء نسخة احتياطية قبل الحفظ
            if self.performance_enabled:
                backup_success = backup_manager.create_backup(self.excel_file)
                if backup_success:
                    print("✅ تم إنشاء نسخة احتياطية قبل الحفظ")

            # حفظ في Excel
            if self.save_employees_data():
                self.update_employees_table()
                self.update_status(action_msg)
                self.log_action(log_msg)

                # تسجيل النشاط
                if self.performance_enabled:
                    error_handler.log_activity(action_msg, log_msg,
                                             self.current_user.get("username", ""),
                                             "employee_management", True)

                # إغلاق النافذة
                form_window.destroy()

                # عرض رسالة النجاح
                messagebox.showinfo("نجح", action_msg)
            else:
                # تسجيل فشل الحفظ
                if self.performance_enabled:
                    error_handler.log_error("SAVE_ERROR", "فشل في حفظ البيانات في ملف Excel",
                                          "", self.current_user.get("username", ""), "employee_management")
                messagebox.showerror("خطأ", "فشل في حفظ البيانات في ملف Excel")

        except Exception as e:
            error_msg = f"خطأ في حفظ البيانات: {str(e)}"
            print(f"❌ {error_msg}")
            messagebox.showerror("خطأ", error_msg)

    def validate_date(self, date_string):
        """التحقق من صحة تنسيق التاريخ"""
        try:
            datetime.strptime(date_string, "%Y-%m-%d")
            return True
        except ValueError:
            return False

    def show_employee_details(self):
        """عرض تفاصيل الموظف"""
        selected_item = self.employees_table.focus()
        if not selected_item:
            messagebox.showwarning("تحذير", "الرجاء تحديد موظف لعرض التفاصيل")
            return

        values = self.employees_table.item(selected_item)["values"]
        emp_id = values[0]
        employee = self.get_employee_by_id(emp_id)

        if not employee:
            messagebox.showerror("خطأ", "لم يتم العثور على بيانات الموظف")
            return

        # إنشاء نافذة التفاصيل
        details_window = tk.Toplevel(self.root)
        details_window.title(f"تفاصيل الموظف - {employee.get('الاسم العربي', '')} ({emp_id})")
        details_window.geometry("700x600")
        details_window.resizable(True, True)

        # إطار التمرير
        canvas = tk.Canvas(details_window)
        scrollbar = ttk.Scrollbar(details_window, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # عنوان النافذة
        title_frame = tk.Frame(scrollable_frame, bg="#3498db", height=60)
        title_frame.pack(fill=tk.X, padx=10, pady=10)
        title_frame.pack_propagate(False)

        title_label = tk.Label(
            title_frame,
            text=f"📊 تفاصيل الموظف: {employee.get('الاسم العربي', '')}",
            bg="#3498db",
            fg="white",
            font=("Arial", 16, "bold")
        )
        title_label.pack(expand=True)

        # إطار التفاصيل
        details_frame = tk.Frame(scrollable_frame)
        details_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # تجميع الحقول
        field_groups = [
            ("المعلومات الأساسية", [
                "الرقم الوظيفي", "الاسم العربي", "الاسم الإنجليزي",
                "الرقم المالي", "الرقم الوطني", "الجنسية", "تاريخ الميلاد"
            ]),
            ("المعلومات الوظيفية", [
                "المسمى الوظيفي", "التخصص", "المؤهل", "مكان العمل الحالي",
                "الدرجة الحالية", "العلاوة", "تاريخ التعيين", "تاريخ أول مباشرة", "تاريخ الدرجة الحالية"
            ]),
            ("المعلومات المصرفية", [
                "رقم الحساب", "اسم المصرف"
            ]),
            ("معلومات الاتصال", [
                "رقم الهاتف"
            ])
        ]

        for group_name, fields in field_groups:
            # إطار المجموعة
            group_frame = tk.LabelFrame(details_frame, text=group_name, font=("Arial", 12, "bold"))
            group_frame.pack(fill=tk.X, pady=10)

            # عرض الحقول
            for i, field in enumerate(fields):
                value = employee.get(field, "غير محدد")

                field_frame = tk.Frame(group_frame)
                field_frame.pack(fill=tk.X, padx=10, pady=2)

                # اسم الحقل
                field_label = tk.Label(
                    field_frame,
                    text=f"{field}:",
                    font=("Arial", 10, "bold"),
                    fg="#2c3e50",
                    width=20,
                    anchor="w"
                )
                field_label.pack(side=tk.LEFT)

                # قيمة الحقل
                value_label = tk.Label(
                    field_frame,
                    text=str(value),
                    font=("Arial", 10),
                    fg="#34495e",
                    anchor="w"
                )
                value_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # أزرار التحكم
        buttons_frame = tk.Frame(scrollable_frame, bg="#ecf0f1")
        buttons_frame.pack(fill=tk.X, padx=20, pady=20)

        # زر التعديل
        edit_btn = tk.Button(
            buttons_frame,
            text="✏️ تعديل",
            command=lambda: [details_window.destroy(), self.edit_employee()],
            bg="#3498db",
            fg="white",
            font=("Arial", 12, "bold"),
            padx=20,
            pady=10,
            cursor="hand2"
        )
        edit_btn.pack(side=tk.LEFT, padx=10)

        # زر الطباعة
        print_btn = tk.Button(
            buttons_frame,
            text="🖨️ طباعة",
            command=lambda: self.print_employee_data(),
            bg="#27ae60",
            fg="white",
            font=("Arial", 12, "bold"),
            padx=20,
            pady=10,
            cursor="hand2"
        )
        print_btn.pack(side=tk.LEFT, padx=10)

        # زر الإغلاق
        close_btn = tk.Button(
            buttons_frame,
            text="❌ إغلاق",
            command=details_window.destroy,
            bg="#95a5a6",
            fg="white",
            font=("Arial", 12, "bold"),
            padx=20,
            pady=10,
            cursor="hand2"
        )
        close_btn.pack(side=tk.RIGHT, padx=10)

    def export_employees(self):
        """تصدير بيانات الموظفين"""
        if not self.employees_data:
            messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
            return

        # اختيار نوع التصدير
        export_window = tk.Toplevel(self.root)
        export_window.title("تصدير بيانات الموظفين")
        export_window.geometry("400x300")
        export_window.resizable(False, False)
        export_window.grab_set()

        # عنوان النافذة
        title_label = tk.Label(
            export_window,
            text="📤 تصدير بيانات الموظفين",
            font=("Arial", 16, "bold"),
            fg="#2c3e50"
        )
        title_label.pack(pady=20)

        # خيارات التصدير
        export_var = tk.StringVar(value="excel")

        options_frame = tk.LabelFrame(export_window, text="نوع التصدير", font=("Arial", 12, "bold"))
        options_frame.pack(fill=tk.X, padx=20, pady=10)

        tk.Radiobutton(
            options_frame,
            text="📊 ملف Excel (.xlsx)",
            variable=export_var,
            value="excel",
            font=("Arial", 11)
        ).pack(anchor="w", padx=10, pady=5)

        tk.Radiobutton(
            options_frame,
            text="📄 ملف Word (.docx)",
            variable=export_var,
            value="docx",
            font=("Arial", 11)
        ).pack(anchor="w", padx=10, pady=5)

        # معلومات التصدير
        info_frame = tk.Frame(export_window)
        info_frame.pack(fill=tk.X, padx=20, pady=10)

        info_text = f"عدد الموظفين: {len(self.employees_data)}\nتاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        info_label = tk.Label(info_frame, text=info_text, font=("Arial", 10), fg="#7f8c8d")
        info_label.pack()

        # أزرار التحكم
        buttons_frame = tk.Frame(export_window)
        buttons_frame.pack(fill=tk.X, padx=20, pady=20)

        export_btn = tk.Button(
            buttons_frame,
            text="📤 تصدير",
            command=lambda: self.perform_export(export_var.get(), export_window),
            bg="#27ae60",
            fg="white",
            font=("Arial", 12, "bold"),
            padx=20,
            pady=10
        )
        export_btn.pack(side=tk.RIGHT, padx=10)

        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=export_window.destroy,
            bg="#95a5a6",
            fg="white",
            font=("Arial", 12, "bold"),
            padx=20,
            pady=10
        )
        cancel_btn.pack(side=tk.LEFT, padx=10)

    def perform_export(self, export_type, window):
        """تنفيذ عملية التصدير"""
        try:
            # اختيار مكان الحفظ
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            if export_type == "excel":
                filename = f"employees_export_{timestamp}.xlsx"
                file_path = filedialog.asksaveasfilename(
                    defaultextension=".xlsx",
                    filetypes=[("Excel files", "*.xlsx")],
                    initialname=filename
                )
                if file_path:
                    self.export_to_excel(file_path)

            elif export_type == "docx":
                filename = f"employees_export_{timestamp}.docx"
                file_path = filedialog.asksaveasfilename(
                    defaultextension=".docx",
                    filetypes=[("Word documents", "*.docx")],
                    initialname=filename
                )
                if file_path:
                    self.export_to_word(file_path)

            if 'file_path' in locals() and file_path:
                window.destroy()
                messagebox.showinfo("نجح", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
                self.log_action("تصدير البيانات", f"تم تصدير {len(self.employees_data)} موظف إلى {export_type}")

        except Exception as e:
            error_msg = f"خطأ في التصدير: {str(e)}"
            print(f"❌ {error_msg}")
            messagebox.showerror("خطأ", error_msg)

    def export_to_excel(self, file_path):
        """تصدير إلى Excel"""
        if not EXCEL_AVAILABLE:
            raise Exception("مكتبة openpyxl غير متاحة")

        wb = Workbook()
        ws = wb.active
        ws.title = "الموظفين"

        # العناوين
        headers = [
            "الرقم الوظيفي", "الاسم العربي", "الاسم الإنجليزي", "الرقم المالي",
            "الرقم الوطني", "المؤهل", "مكان العمل الحالي", "رقم الحساب",
            "اسم المصرف", "تاريخ أول مباشرة", "الدرجة الحالية", "العلاوة",
            "تاريخ الدرجة الحالية", "التخصص", "المسمى الوظيفي",
            "تاريخ التعيين", "رقم الهاتف", "الجنسية", "تاريخ الميلاد"
        ]
        ws.append(headers)

        # البيانات
        for emp in self.employees_data:
            row_data = [emp.get(header, "") for header in headers]
            ws.append(row_data)

        wb.save(file_path)

    def export_to_word(self, file_path):
        """تصدير إلى ملف Word"""
        try:
            from docx import Document
            from docx.shared import Inches, Pt
            from docx.enum.text import WD_ALIGN_PARAGRAPH
            from docx.enum.table import WD_TABLE_ALIGNMENT
            from docx.shared import RGBColor
            from docx.oxml.ns import qn

            # إنشاء مستند جديد
            doc = Document()

            # إعداد الصفحة
            sections = doc.sections
            for section in sections:
                section.page_height = Inches(11.69)  # A4
                section.page_width = Inches(8.27)
                section.left_margin = Inches(0.8)
                section.right_margin = Inches(0.8)
                section.top_margin = Inches(1)
                section.bottom_margin = Inches(1)

            # إضافة العنوان الرئيسي
            title = doc.add_heading('تقرير بيانات الموظفين', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # تنسيق العنوان
            title_run = title.runs[0]
            title_run.font.name = 'Arial'
            title_run.font.size = Pt(18)
            title_run.font.bold = True
            title_run.font.color.rgb = RGBColor(0, 51, 102)

            # إضافة معلومات التقرير
            info_para = doc.add_paragraph()
            info_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

            current_time = datetime.now()
            info_text = f"تاريخ التصدير: {current_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            info_text += f"عدد الموظفين: {len(self.employees_data)}\n"
            info_text += f"المستخدم: {self.current_user.get('name', 'غير محدد')}"

            info_run = info_para.add_run(info_text)
            info_run.font.name = 'Arial'
            info_run.font.size = Pt(12)
            info_run.font.color.rgb = RGBColor(102, 102, 102)

            # إضافة مسافة
            doc.add_paragraph()

            # تحديد العناوين المهمة
            headers = [
                "الرقم الوظيفي", "الاسم العربي", "المسمى الوظيفي",
                "الرقم الوطني", "المؤهل", "مكان العمل الحالي",
                "رقم الهاتف", "الجنسية", "تاريخ التعيين"
            ]

            # إنشاء الجدول
            table = doc.add_table(rows=1, cols=len(headers))
            table.alignment = WD_TABLE_ALIGNMENT.CENTER
            table.style = 'Table Grid'

            # تنسيق رأس الجدول
            header_cells = table.rows[0].cells
            for i, header in enumerate(headers):
                cell = header_cells[i]
                cell.text = header

                # تنسيق خلية الرأس
                cell_para = cell.paragraphs[0]
                cell_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                cell_run = cell_para.runs[0]
                cell_run.font.name = 'Arial'
                cell_run.font.size = Pt(10)
                cell_run.font.bold = True
                cell_run.font.color.rgb = RGBColor(255, 255, 255)

                # لون خلفية الرأس
                shading_elm = cell._tc.get_or_add_tcPr().get_or_add_shd()
                shading_elm.set(qn('w:fill'), '4472C4')

            # إضافة بيانات الموظفين
            for emp in self.employees_data:
                row_cells = table.add_row().cells

                for i, header in enumerate(headers):
                    cell_value = str(emp.get(header, ""))
                    row_cells[i].text = cell_value

                    # تنسيق خلايا البيانات
                    cell_para = row_cells[i].paragraphs[0]
                    cell_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                    cell_run = cell_para.runs[0] if cell_para.runs else cell_para.add_run()
                    cell_run.font.name = 'Arial'
                    cell_run.font.size = Pt(9)

            # تعديل عرض الأعمدة
            for row in table.rows:
                for i, cell in enumerate(row.cells):
                    if i == 1:  # عمود الاسم
                        cell.width = Inches(1.5)
                    else:
                        cell.width = Inches(1.0)

            # إضافة تذييل
            doc.add_paragraph()
            footer_para = doc.add_paragraph()
            footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            footer_text = "تم إنشاء هذا التقرير بواسطة نظام إدارة الموارد البشرية"
            footer_run = footer_para.add_run(footer_text)
            footer_run.font.name = 'Arial'
            footer_run.font.size = Pt(10)
            footer_run.font.italic = True
            footer_run.font.color.rgb = RGBColor(128, 128, 128)

            # حفظ المستند
            doc.save(file_path)

            messagebox.showinfo("نجح التصدير", f"تم تصدير البيانات إلى ملف Word بنجاح:\n{file_path}")
            self.update_status(f"تم تصدير {len(self.employees_data)} موظف إلى ملف Word")
            self.log_action("تصدير Word", f"تم تصدير {len(self.employees_data)} موظف")

        except ImportError:
            messagebox.showerror("خطأ", "مكتبة python-docx غير مثبتة.\nيرجى تثبيتها باستخدام: pip install python-docx")
        except Exception as e:
            error_msg = f"خطأ في تصدير ملف Word: {str(e)}"
            print(f"❌ {error_msg}")
            messagebox.showerror("خطأ في التصدير", error_msg)




    def refresh_data(self):
        """تحديث البيانات"""
        self.update_status("جاري تحديث البيانات...")
        self.load_employees_data()
        self.reset_filters()
        messagebox.showinfo("تحديث", "تم تحديث البيانات بنجاح")

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            if not os.path.exists("backups"):
                os.makedirs("backups")

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"employees_backup_{timestamp}.xlsx"
            backup_path = os.path.join("backups", backup_filename)

            if os.path.exists(self.excel_file):
                import shutil
                shutil.copy2(self.excel_file, backup_path)
                self.update_status(f"تم إنشاء نسخة احتياطية: {backup_filename}")
                self.log_action("نسخة احتياطية", f"تم إنشاء {backup_filename}")
                return backup_path
            else:
                print("⚠️ ملف البيانات غير موجود لإنشاء نسخة احتياطية")
                return None

        except Exception as e:
            print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            return None

    def print_employee_data(self):
        """طباعة بيانات الموظف"""
        selected_item = self.employees_table.focus()
        if not selected_item:
            messagebox.showwarning("تحذير", "الرجاء تحديد موظف للطباعة")
            return

        values = self.employees_table.item(selected_item)["values"]
        emp_id = values[0]
        employee = self.get_employee_by_id(emp_id)

        if not employee:
            messagebox.showerror("خطأ", "لم يتم العثور على بيانات الموظف")
            return

        try:
            # إنشاء ملف مؤقت للطباعة
            import tempfile

            temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8')

            temp_file.write("بيانات الموظف\n")
            temp_file.write("=" * 40 + "\n")
            temp_file.write(f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            temp_file.write("=" * 40 + "\n\n")

            for key, value in employee.items():
                if value:
                    temp_file.write(f"{key}: {value}\n")

            temp_file.close()

            # فتح الملف للطباعة
            import subprocess
            import platform

            system = platform.system()
            if system == "Windows":
                os.startfile(temp_file.name, "print")
            elif system == "Darwin":  # macOS
                subprocess.run(["lpr", temp_file.name])
            else:  # Linux
                subprocess.run(["lp", temp_file.name])

            messagebox.showinfo("طباعة", "تم إرسال البيانات للطباعة")
            self.log_action("طباعة بيانات موظف", f"{employee.get('الاسم العربي', '')} ({emp_id})")

        except Exception as e:
            error_msg = f"خطأ في الطباعة: {str(e)}"
            print(f"❌ {error_msg}")
            messagebox.showerror("خطأ", error_msg)

    def close_window(self):
        """إغلاق نافذة نظام إدارة الموظفين"""
        try:
            if messagebox.askyesno("إغلاق النظام", "هل أنت متأكد من إغلاق نظام إدارة الموظفين؟"):
                self.root.destroy()
        except Exception as e:
            print(f"خطأ في إغلاق نظام إدارة الموظفين: {e}")
            self.root.destroy()

# دالة لتشغيل النظام
def run_employee_management(current_user=None):
    """تشغيل نظام إدارة الموظفين المحسن"""
    root = tk.Tk()
    app = EmployeeManagementSystem(root, current_user)

    # إعداد إغلاق النافذة
    def on_closing():
        if messagebox.askokcancel("إغلاق", "هل تريد إغلاق نظام إدارة الموظفين؟"):
            root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    # تشغيل النظام للاختبار
    run_employee_management({"username": "admin", "role": "admin", "name": "مدير النظام"})
