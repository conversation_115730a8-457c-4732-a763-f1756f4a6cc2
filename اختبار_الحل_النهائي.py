#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الحل النهائي لمشكلة رسالة البحث
Final Solution Test for Search Message Issue
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_final_solution():
    """اختبار الحل النهائي"""
    print("🚀 اختبار الحل النهائي لمشكلة رسالة البحث")
    print("=" * 70)
    
    try:
        # استيراد النظام
        print("📦 استيراد نظام إدارة الموظفين...")
        import employee_management
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة الاختبار...")
        root = tk.Tk()
        root.title("اختبار الحل النهائي - البحث القسري")
        root.geometry("1400x900")
        
        # إنشاء النظام
        print("🔧 إنشاء نظام إدارة الموظفين...")
        current_user = {"username": "final_tester", "name": "مختبر الحل النهائي"}
        emp_system = employee_management.EmployeeManagementSystem(root, current_user)
        print("✅ تم إنشاء النظام بنجاح")
        
        # اختبار الدالة الجديدة
        print("\n🧪 اختبار الدالة الجديدة force_search:")
        
        test_cases = [
            ("أحمد", "اسم عربي"),
            ("Ahmed", "اسم إنجليزي"),
            ("001", "رقم وظيفي"),
            ("موظف", "مسمى وظيفي"),
            ("بكالوريوس", "مؤهل"),
            ("ليبي", "جنسية"),
            ("  أحمد  ", "اسم مع مسافات"),
            ("أحمد   محمد", "اسم مع مسافات متعددة")
        ]
        
        for i, (test_text, description) in enumerate(test_cases, 1):
            print(f"\n📝 اختبار {i}: {description}")
            print(f"   النص: '{test_text}'")
            
            try:
                # تعيين النص
                emp_system.search_var.set(test_text)
                
                # حفظ الدوال الأصلية
                original_showwarning = messagebox.showwarning
                original_showerror = messagebox.showerror
                
                messages = []
                
                def mock_showwarning(title, message):
                    messages.append(f"تحذير: {message}")
                    print(f"     📢 رسالة تحذير: {message}")
                
                def mock_showerror(title, message):
                    messages.append(f"خطأ: {message}")
                    print(f"     📢 رسالة خطأ: {message}")
                
                # استبدال الدوال مؤقتاً
                messagebox.showwarning = mock_showwarning
                messagebox.showerror = mock_showerror
                
                # تشغيل البحث القسري
                print("     🚀 تشغيل البحث القسري...")
                emp_system.force_search()
                
                # استعادة الدوال الأصلية
                messagebox.showwarning = original_showwarning
                messagebox.showerror = original_showerror
                
                # تحليل النتيجة
                if messages:
                    result = f"❌ ظهرت رسالة: {messages[0]}"
                else:
                    result = "✅ تم البحث بنجاح بدون رسائل خطأ"
                
                print(f"     النتيجة: {result}")
                
            except Exception as e:
                print(f"     ❌ خطأ في الاختبار: {e}")
        
        # إنشاء واجهة تفاعلية للاختبار
        create_final_test_interface(root, emp_system)
        
        print("\n🎉 انتهى الاختبار - الواجهة التفاعلية جاهزة")
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def create_final_test_interface(root, emp_system):
    """إنشاء واجهة تفاعلية للاختبار النهائي"""
    
    # إطار الاختبار
    test_frame = tk.Frame(root, bg="#d1ecf1")
    test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
    
    # عنوان
    title_label = tk.Label(test_frame, text="🚀 اختبار الحل النهائي - البحث القسري", 
                          bg="#d1ecf1", fg="#0c5460", font=("Arial", 14, "bold"))
    title_label.pack(pady=5)
    
    # تعليمات
    instructions_text = """
✅ الحل النهائي: تم إضافة دالة "البحث القسري" التي:
• تبحث عن النص من مصادر متعددة
• تتجاهل الفحوصات الصارمة
• تنفذ البحث مباشرة إذا وجدت نص
• لا تظهر رسالة خطأ إلا إذا لم تجد أي نص فعلاً

الآن زر "🔍 بحث" يستخدم الدالة الجديدة force_search بدلاً من perform_search
    """
    instructions_label = tk.Label(test_frame, text=instructions_text, bg="#d1ecf1", 
                                 fg="#0c5460", font=("Arial", 10), justify=tk.LEFT)
    instructions_label.pack(pady=5)
    
    # متغير لعرض النتائج
    result_var = tk.StringVar()
    result_label = tk.Label(test_frame, textvariable=result_var, bg="#d1ecf1", 
                           fg="#0c5460", font=("Arial", 10, "bold"))
    result_label.pack(pady=5)
    
    # أزرار الاختبار
    buttons_frame = tk.Frame(test_frame, bg="#d1ecf1")
    buttons_frame.pack(pady=10)
    
    def test_with_text(text, desc):
        emp_system.search_var.set(text)
        result_var.set(f"تم تعيين النص: '{text}' - {desc}")
        print(f"🧪 تم تعيين نص الاختبار: {desc} - '{text}'")
    
    def test_force_search():
        current_text = emp_system.search_var.get()
        print(f"\n🚀 اختبار البحث القسري للنص: '{current_text}'")
        
        # حفظ الدوال الأصلية
        original_showwarning = messagebox.showwarning
        original_showerror = messagebox.showerror
        
        messages = []
        
        def mock_showwarning(title, message):
            messages.append(f"تحذير: {message}")
            print(f"📢 رسالة تحذير: {message}")
        
        def mock_showerror(title, message):
            messages.append(f"خطأ: {message}")
            print(f"📢 رسالة خطأ: {message}")
        
        messagebox.showwarning = mock_showwarning
        messagebox.showerror = mock_showerror
        
        try:
            emp_system.force_search()
            
            if messages:
                result_var.set(f"❌ ظهرت رسالة: {messages[0]}")
            else:
                result_var.set("✅ تم البحث بنجاح بدون رسائل خطأ")
                
        except Exception as e:
            result_var.set(f"❌ خطأ: {e}")
        finally:
            messagebox.showwarning = original_showwarning
            messagebox.showerror = original_showerror
    
    def test_old_search():
        current_text = emp_system.search_var.get()
        print(f"\n🔍 اختبار البحث القديم للنص: '{current_text}'")
        
        # حفظ الدوال الأصلية
        original_showwarning = messagebox.showwarning
        original_showerror = messagebox.showerror
        
        messages = []
        
        def mock_showwarning(title, message):
            messages.append(f"تحذير: {message}")
            print(f"📢 رسالة تحذير: {message}")
        
        def mock_showerror(title, message):
            messages.append(f"خطأ: {message}")
            print(f"📢 رسالة خطأ: {message}")
        
        messagebox.showwarning = mock_showwarning
        messagebox.showerror = mock_showerror
        
        try:
            emp_system.perform_search()
            
            if messages:
                result_var.set(f"❌ البحث القديم: ظهرت رسالة: {messages[0]}")
            else:
                result_var.set("✅ البحث القديم: تم البحث بنجاح")
                
        except Exception as e:
            result_var.set(f"❌ البحث القديم: خطأ: {e}")
        finally:
            messagebox.showwarning = original_showwarning
            messagebox.showerror = original_showerror
    
    def compare_searches():
        current_text = emp_system.search_var.get()
        print(f"\n🔄 مقارنة البحث القديم والجديد للنص: '{current_text}'")
        
        # اختبار البحث القديم
        print("1️⃣ اختبار البحث القديم:")
        test_old_search()
        
        # اختبار البحث الجديد
        print("2️⃣ اختبار البحث الجديد:")
        test_force_search()
    
    # أزرار اختبار النصوص
    tk.Label(buttons_frame, text="اختبار نصوص:", bg="#d1ecf1", 
            font=("Arial", 10, "bold")).pack(side=tk.LEFT)
    
    test_texts = [
        ("", "فارغ"),
        ("   ", "مسافات"),
        ("أحمد", "عادي"),
        ("  أحمد  ", "مسافات زائدة"),
        ("أحمد   محمد", "مسافات متعددة")
    ]
    
    for text, desc in test_texts:
        btn = tk.Button(buttons_frame, text=desc,
                       command=lambda t=text, d=desc: test_with_text(t, d),
                       bg="#17a2b8", fg="white", font=("Arial", 8))
        btn.pack(side=tk.LEFT, padx=2)
    
    # أزرار الوظائف
    functions_frame = tk.Frame(test_frame, bg="#d1ecf1")
    functions_frame.pack(pady=5)
    
    tk.Label(functions_frame, text="وظائف الاختبار:", bg="#d1ecf1", 
            font=("Arial", 10, "bold")).pack(side=tk.LEFT)
    
    tk.Button(functions_frame, text="🚀 البحث القسري (الجديد)",
             command=test_force_search,
             bg="#28a745", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(functions_frame, text="🔍 البحث القديم",
             command=test_old_search,
             bg="#dc3545", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(functions_frame, text="🔄 مقارنة البحثين",
             command=compare_searches,
             bg="#6f42c1", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    # معلومات إضافية
    info_frame = tk.Frame(test_frame, bg="#d1ecf1")
    info_frame.pack(pady=5)
    
    info_text = """
💡 ملاحظة: زر "🔍 بحث" في الواجهة الرئيسية يستخدم الآن البحث القسري الجديد
🎯 النتيجة المتوقعة: لن تظهر رسالة "أدخل النص أولاً" إلا إذا كان الحقل فارغاً فعلاً
    """
    info_label = tk.Label(info_frame, text=info_text, bg="#d1ecf1", 
                         fg="#0c5460", font=("Arial", 9), justify=tk.LEFT)
    info_label.pack()
    
    # زر إغلاق
    tk.Button(test_frame, text="❌ إغلاق الاختبار", 
             command=root.destroy,
             bg="#6c757d", fg="white", 
             font=("Arial", 12, "bold")).pack(pady=10)

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار الحل النهائي لمشكلة رسالة البحث")
    print("=" * 70)
    
    test_final_solution()

if __name__ == "__main__":
    main()
