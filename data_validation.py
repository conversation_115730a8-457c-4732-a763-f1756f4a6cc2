#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التحقق من صحة البيانات والحسابات
Data Validation and Calculation System
"""

import re
import datetime
from typing import Dict, List, Tuple, Any, Optional

class DataValidator:
    """فئة التحقق من صحة البيانات"""
    
    def __init__(self):
        """تهيئة نظام التحقق"""
        self.errors = []
        self.warnings = []
        
    def clear_messages(self):
        """مسح الرسائل السابقة"""
        self.errors.clear()
        self.warnings.clear()
    
    def add_error(self, field: str, message: str):
        """إضافة رسالة خطأ"""
        self.errors.append(f"❌ {field}: {message}")
    
    def add_warning(self, field: str, message: str):
        """إضافة رسالة تحذير"""
        self.warnings.append(f"⚠️ {field}: {message}")
    
    def has_errors(self) -> bool:
        """فحص وجود أخطاء"""
        return len(self.errors) > 0
    
    def has_warnings(self) -> bool:
        """فحص وجود تحذيرات"""
        return len(self.warnings) > 0
    
    def get_all_messages(self) -> List[str]:
        """الحصول على جميع الرسائل"""
        return self.errors + self.warnings
    
    def validate_employee_id(self, emp_id: str) -> bool:
        """التحقق من صحة الرقم الوظيفي"""
        if not emp_id or not emp_id.strip():
            self.add_error("الرقم الوظيفي", "مطلوب ولا يمكن أن يكون فارغاً")
            return False
        
        emp_id = emp_id.strip()
        
        # فحص الطول
        if len(emp_id) < 3:
            self.add_error("الرقم الوظيفي", "يجب أن يكون 3 أرقام على الأقل")
            return False
        
        if len(emp_id) > 10:
            self.add_error("الرقم الوظيفي", "يجب أن يكون 10 أرقام كحد أقصى")
            return False
        
        # فحص أن يحتوي على أرقام فقط
        if not emp_id.isdigit():
            self.add_error("الرقم الوظيفي", "يجب أن يحتوي على أرقام فقط")
            return False
        
        return True
    
    def validate_national_id(self, national_id: str) -> bool:
        """التحقق من صحة الرقم الوطني"""
        if not national_id or not national_id.strip():
            self.add_warning("الرقم الوطني", "مستحسن إدخال الرقم الوطني")
            return True

        national_id = national_id.strip()

        # فحص الطول (10 أو 12 رقم)
        if len(national_id) not in [10, 12]:
            self.add_error("الرقم الوطني", "يجب أن يكون 10 أو 12 رقم")
            return False

        # فحص أن يحتوي على أرقام فقط
        if not national_id.isdigit():
            self.add_error("الرقم الوطني", "يجب أن يحتوي على أرقام فقط")
            return False

        # فحص أن يبدأ برقم صحيح حسب الطول
        if len(national_id) == 10:
            # الرقم الوطني السعودي القديم (10 أرقام)
            if not national_id.startswith(('1', '2')):
                self.add_warning("الرقم الوطني", "الرقم الوطني السعودي يجب أن يبدأ برقم 1 أو 2")
        elif len(national_id) == 12:
            # الرقم الوطني الجديد (12 رقم)
            if not national_id.startswith(('1', '2', '3', '4', '5')):
                self.add_warning("الرقم الوطني", "الرقم الوطني الجديد يجب أن يبدأ برقم صحيح")

        return True
    
    def validate_name(self, name: str, field_name: str) -> bool:
        """التحقق من صحة الاسم"""
        if not name or not name.strip():
            self.add_error(field_name, "مطلوب ولا يمكن أن يكون فارغاً")
            return False
        
        name = name.strip()
        
        # فحص الطول
        if len(name) < 2:
            self.add_error(field_name, "يجب أن يكون حرفين على الأقل")
            return False
        
        if len(name) > 100:
            self.add_error(field_name, "يجب أن يكون 100 حرف كحد أقصى")
            return False
        
        # فحص الأحرف المسموحة (عربي وإنجليزي ومسافات)
        allowed_pattern = r'^[a-zA-Zأ-ي\s]+$'
        if not re.match(allowed_pattern, name):
            self.add_error(field_name, "يجب أن يحتوي على أحرف عربية أو إنجليزية فقط")
            return False
        
        return True
    
    def validate_date(self, date_str: str, field_name: str, required: bool = False) -> bool:
        """التحقق من صحة التاريخ"""
        if not date_str or not date_str.strip():
            if required:
                self.add_error(field_name, "مطلوب ولا يمكن أن يكون فارغاً")
                return False
            return True
        
        date_str = date_str.strip()
        
        # فحص تنسيق التاريخ
        date_formats = ["%Y-%m-%d", "%d/%m/%Y", "%d-%m-%Y"]
        parsed_date = None
        
        for date_format in date_formats:
            try:
                parsed_date = datetime.datetime.strptime(date_str, date_format)
                break
            except ValueError:
                continue
        
        if not parsed_date:
            self.add_error(field_name, "تنسيق التاريخ غير صحيح (استخدم YYYY-MM-DD)")
            return False
        
        # فحص أن التاريخ منطقي
        current_date = datetime.datetime.now()
        
        if "ميلاد" in field_name:
            # تاريخ الميلاد يجب أن يكون في الماضي
            if parsed_date > current_date:
                self.add_error(field_name, "تاريخ الميلاد لا يمكن أن يكون في المستقبل")
                return False
            
            # فحص العمر المنطقي (بين 18 و 70 سنة)
            age = (current_date - parsed_date).days / 365.25
            if age < 18:
                self.add_warning(field_name, "العمر أقل من 18 سنة")
            elif age > 70:
                self.add_warning(field_name, "العمر أكبر من 70 سنة")
        
        elif "تعيين" in field_name or "مباشرة" in field_name:
            # تاريخ التعيين يجب أن يكون منطقي
            if parsed_date > current_date:
                self.add_error(field_name, "تاريخ التعيين لا يمكن أن يكون في المستقبل")
                return False
            
            # فحص أن التاريخ ليس قديم جداً (أكثر من 50 سنة)
            years_ago = (current_date - parsed_date).days / 365.25
            if years_ago > 50:
                self.add_warning(field_name, "تاريخ التعيين قديم جداً (أكثر من 50 سنة)")
        
        return True
    
    def validate_phone(self, phone: str) -> bool:
        """التحقق من صحة رقم الهاتف"""
        if not phone or not phone.strip():
            self.add_warning("رقم الهاتف", "مستحسن إدخال رقم الهاتف")
            return True
        
        phone = phone.strip()
        
        # إزالة الرموز الشائعة
        phone = re.sub(r'[+\-\s\(\)]', '', phone)
        
        # فحص أن يحتوي على أرقام فقط
        if not phone.isdigit():
            self.add_error("رقم الهاتف", "يجب أن يحتوي على أرقام فقط")
            return False
        
        # فحص الطول (10 أرقام للسعودية)
        if len(phone) == 10 and phone.startswith('05'):
            return True
        elif len(phone) == 13 and phone.startswith('966'):
            return True
        elif len(phone) == 12 and phone.startswith('05'):
            return True
        else:
            self.add_warning("رقم الهاتف", "تنسيق رقم الهاتف قد يكون غير صحيح")
        
        return True
    
    def validate_account_number(self, account: str) -> bool:
        """التحقق من صحة رقم الحساب"""
        if not account or not account.strip():
            self.add_warning("رقم الحساب", "مستحسن إدخال رقم الحساب")
            return True
        
        account = account.strip()
        
        # فحص أن يحتوي على أرقام فقط
        if not account.isdigit():
            self.add_error("رقم الحساب", "يجب أن يحتوي على أرقام فقط")
            return False
        
        # فحص الطول المنطقي
        if len(account) < 10:
            self.add_warning("رقم الحساب", "رقم الحساب قصير جداً")
        elif len(account) > 24:
            self.add_warning("رقم الحساب", "رقم الحساب طويل جداً")
        
        return True
    
    def validate_job_title(self, job_title: str) -> bool:
        """التحقق من صحة المسمى الوظيفي"""
        if not job_title or not job_title.strip():
            self.add_error("المسمى الوظيفي", "مطلوب ولا يمكن أن يكون فارغاً")
            return False
        
        job_title = job_title.strip()
        
        # قائمة المسميات المقبولة
        valid_titles = ["موظف", "موظفة", "معلم", "معلمة"]
        
        if job_title not in valid_titles:
            self.add_warning("المسمى الوظيفي", f"المسمى غير مدرج في القائمة المعتمدة: {', '.join(valid_titles)}")
        
        return True
    
    def validate_employee_data(self, emp_data: Dict[str, Any]) -> bool:
        """التحقق الشامل من بيانات الموظف"""
        self.clear_messages()
        
        is_valid = True
        
        # التحقق من الحقول المطلوبة
        required_fields = ["الرقم الوظيفي", "الاسم العربي", "المسمى الوظيفي"]
        
        for field in required_fields:
            if field not in emp_data or not emp_data[field]:
                self.add_error(field, "حقل مطلوب")
                is_valid = False
        
        # التحقق من كل حقل
        if "الرقم الوظيفي" in emp_data:
            if not self.validate_employee_id(emp_data["الرقم الوظيفي"]):
                is_valid = False
        
        if "الرقم الوطني" in emp_data:
            if not self.validate_national_id(emp_data["الرقم الوطني"]):
                is_valid = False
        
        if "الاسم العربي" in emp_data:
            if not self.validate_name(emp_data["الاسم العربي"], "الاسم العربي"):
                is_valid = False
        
        if "الاسم الإنجليزي" in emp_data:
            if not self.validate_name(emp_data["الاسم الإنجليزي"], "الاسم الإنجليزي"):
                is_valid = False
        
        if "تاريخ الميلاد" in emp_data:
            if not self.validate_date(emp_data["تاريخ الميلاد"], "تاريخ الميلاد"):
                is_valid = False
        
        if "تاريخ التعيين" in emp_data:
            if not self.validate_date(emp_data["تاريخ التعيين"], "تاريخ التعيين"):
                is_valid = False
        
        if "تاريخ أول مباشرة" in emp_data:
            if not self.validate_date(emp_data["تاريخ أول مباشرة"], "تاريخ أول مباشرة"):
                is_valid = False
        
        if "رقم الهاتف" in emp_data:
            if not self.validate_phone(emp_data["رقم الهاتف"]):
                is_valid = False
        
        if "رقم الحساب" in emp_data:
            if not self.validate_account_number(emp_data["رقم الحساب"]):
                is_valid = False
        
        if "المسمى الوظيفي" in emp_data:
            if not self.validate_job_title(emp_data["المسمى الوظيفي"]):
                is_valid = False
        
        # التحقق من التواريخ المترابطة
        if "تاريخ الميلاد" in emp_data and "تاريخ التعيين" in emp_data:
            if emp_data["تاريخ الميلاد"] and emp_data["تاريخ التعيين"]:
                try:
                    birth_date = datetime.datetime.strptime(emp_data["تاريخ الميلاد"], "%Y-%m-%d")
                    hire_date = datetime.datetime.strptime(emp_data["تاريخ التعيين"], "%Y-%m-%d")
                    
                    age_at_hire = (hire_date - birth_date).days / 365.25
                    if age_at_hire < 18:
                        self.add_error("تاريخ التعيين", "عمر الموظف عند التعيين أقل من 18 سنة")
                        is_valid = False
                except:
                    pass
        
        return is_valid

class CalculationEngine:
    """محرك الحسابات المتقدم"""
    
    def __init__(self):
        """تهيئة محرك الحسابات"""
        self.validator = DataValidator()
    
    def calculate_service_years(self, start_date_str: str) -> float:
        """حساب سنوات الخدمة بدقة"""
        if not start_date_str:
            return 0.0
        
        try:
            # محاولة تحويل التاريخ بصيغ مختلفة
            start_date = None
            date_formats = ["%Y-%m-%d", "%d/%m/%Y", "%d-%m-%Y"]
            
            for date_format in date_formats:
                try:
                    start_date = datetime.datetime.strptime(str(start_date_str), date_format)
                    break
                except ValueError:
                    continue
            
            if not start_date:
                return 0.0
            
            # حساب الفرق بالأيام ثم تحويل لسنوات
            current_date = datetime.datetime.now()
            days_diff = (current_date - start_date).days
            
            # حساب دقيق للسنوات (مع مراعاة السنوات الكبيسة)
            years = days_diff / 365.25
            
            return round(years, 2)
        
        except Exception as e:
            print(f"خطأ في حساب سنوات الخدمة: {e}")
            return 0.0
    
    def calculate_leave_balance(self, service_years: float, job_title: str) -> int:
        """حساب رصيد الإجازات بناءً على المسمى الوظيفي"""
        if service_years <= 0:
            return 0
        
        job_title_lower = str(job_title).lower()
        
        # قائمة المسميات التي تستحق رصيد إجازات (موظفين فقط)
        employee_titles = ["موظف", "موظفة", "مدير", "مديرة", "محاسب", "محاسبة", 
                          "سكرتير", "سكرتيرة", "مشرف", "مشرفة", "أخصائي", "أخصائية"]
        
        # قائمة المسميات التي لا تستحق رصيد إجازات (معلمين)
        teacher_titles = ["معلم", "معلمة", "مدرس", "مدرسة", "أستاذ", "أستاذة", 
                         "محاضر", "محاضرة", "مدرب", "مدربة"]
        
        # فحص إذا كان المسمى يحتوي على كلمة معلم (أولوية)
        is_teacher = any(teacher_word in job_title_lower for teacher_word in 
                        ["معلم", "مدرس", "أستاذ", "محاضر", "مدرب"])
        
        # فحص إذا كان المسمى يحتوي على كلمة موظف
        is_employee = any(emp_word in job_title_lower for emp_word in 
                         ["موظف", "مدير", "محاسب", "سكرتير", "مشرف", "أخصائي"])
        
        if is_teacher:
            # المعلمون لا يحصلون على رصيد تلقائي
            return 0
        elif is_employee or job_title in employee_titles:
            # الموظفون يحصلون على 30 يوم لكل سنة
            balance = int(service_years * 30)
            return balance
        else:
            # في حالة عدم وضوح المسمى، اعتبره موظف (الافتراضي)
            balance = int(service_years * 30)
            return balance
    
    def calculate_age(self, birth_date_str: str) -> int:
        """حساب العمر بالسنوات"""
        if not birth_date_str:
            return 0
        
        try:
            birth_date = datetime.datetime.strptime(birth_date_str, "%Y-%m-%d")
            current_date = datetime.datetime.now()
            age = (current_date - birth_date).days / 365.25
            return int(age)
        except:
            return 0
    
    def calculate_retirement_date(self, birth_date_str: str, retirement_age: int = 60) -> str:
        """حساب تاريخ التقاعد"""
        if not birth_date_str:
            return ""
        
        try:
            birth_date = datetime.datetime.strptime(birth_date_str, "%Y-%m-%d")
            retirement_date = birth_date.replace(year=birth_date.year + retirement_age)
            return retirement_date.strftime("%Y-%m-%d")
        except:
            return ""
