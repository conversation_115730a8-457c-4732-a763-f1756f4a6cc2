#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الأخطاء والسجلات
Error Handling and Logging System
"""

import os
import datetime
import traceback
import json
from typing import Dict, List, Any, Optional

class ErrorHandler:
    """فئة إدارة الأخطاء والسجلات"""
    
    def __init__(self, log_dir: str = "logs"):
        """تهيئة نظام إدارة الأخطاء"""
        self.log_dir = log_dir
        self.ensure_log_directory()
        
        # ملفات السجلات
        today = datetime.datetime.now().strftime("%Y%m%d")
        self.error_log_file = os.path.join(log_dir, f"errors_{today}.log")
        self.activity_log_file = os.path.join(log_dir, f"activity_{today}.log")
        self.validation_log_file = os.path.join(log_dir, f"validation_{today}.log")
        
        # إحصائيات الأخطاء
        self.error_stats = {
            "total_errors": 0,
            "validation_errors": 0,
            "calculation_errors": 0,
            "file_errors": 0,
            "system_errors": 0
        }
    
    def ensure_log_directory(self):
        """التأكد من وجود مجلد السجلات"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
    
    def log_error(self, error_type: str, message: str, details: str = "", 
                  user: str = "", module: str = ""):
        """تسجيل خطأ في السجل"""
        try:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            error_entry = {
                "timestamp": timestamp,
                "type": error_type,
                "message": message,
                "details": details,
                "user": user,
                "module": module,
                "traceback": traceback.format_exc() if details else ""
            }
            
            # كتابة في ملف السجل
            with open(self.error_log_file, "a", encoding="utf-8") as f:
                f.write(f"[{timestamp}] {error_type}: {message}\n")
                if details:
                    f.write(f"  التفاصيل: {details}\n")
                if user:
                    f.write(f"  المستخدم: {user}\n")
                if module:
                    f.write(f"  الوحدة: {module}\n")
                f.write("-" * 80 + "\n")
            
            # تحديث الإحصائيات
            self.error_stats["total_errors"] += 1
            if "validation" in error_type.lower():
                self.error_stats["validation_errors"] += 1
            elif "calculation" in error_type.lower():
                self.error_stats["calculation_errors"] += 1
            elif "file" in error_type.lower():
                self.error_stats["file_errors"] += 1
            else:
                self.error_stats["system_errors"] += 1
            
            print(f"❌ خطأ مسجل: {error_type} - {message}")
            
        except Exception as e:
            print(f"❌ فشل في تسجيل الخطأ: {e}")
    
    def log_activity(self, action: str, details: str = "", user: str = "", 
                    module: str = "", success: bool = True):
        """تسجيل نشاط في السجل"""
        try:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            status = "✅ نجح" if success else "❌ فشل"
            
            with open(self.activity_log_file, "a", encoding="utf-8") as f:
                f.write(f"[{timestamp}] {status}: {action}\n")
                if details:
                    f.write(f"  التفاصيل: {details}\n")
                if user:
                    f.write(f"  المستخدم: {user}\n")
                if module:
                    f.write(f"  الوحدة: {module}\n")
                f.write("-" * 50 + "\n")
            
        except Exception as e:
            print(f"❌ فشل في تسجيل النشاط: {e}")
    
    def log_validation(self, field: str, value: str, result: bool, 
                      message: str = "", user: str = ""):
        """تسجيل نتائج التحقق من صحة البيانات"""
        try:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            status = "✅ صحيح" if result else "❌ خطأ"
            
            with open(self.validation_log_file, "a", encoding="utf-8") as f:
                f.write(f"[{timestamp}] {status}: {field} = '{value}'\n")
                if message:
                    f.write(f"  الرسالة: {message}\n")
                if user:
                    f.write(f"  المستخدم: {user}\n")
                f.write("-" * 40 + "\n")
            
        except Exception as e:
            print(f"❌ فشل في تسجيل التحقق: {e}")
    
    def get_error_stats(self) -> Dict[str, int]:
        """الحصول على إحصائيات الأخطاء"""
        return self.error_stats.copy()
    
    def get_recent_errors(self, limit: int = 10) -> List[str]:
        """الحصول على آخر الأخطاء"""
        try:
            if not os.path.exists(self.error_log_file):
                return []
            
            with open(self.error_log_file, "r", encoding="utf-8") as f:
                lines = f.readlines()
            
            # استخراج آخر الأخطاء
            recent_errors = []
            for line in reversed(lines):
                if line.startswith("[") and ":" in line:
                    recent_errors.append(line.strip())
                    if len(recent_errors) >= limit:
                        break
            
            return recent_errors
            
        except Exception as e:
            print(f"❌ فشل في قراءة الأخطاء الأخيرة: {e}")
            return []
    
    def clear_old_logs(self, days_to_keep: int = 30):
        """حذف السجلات القديمة"""
        try:
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days_to_keep)
            
            for filename in os.listdir(self.log_dir):
                if filename.endswith(".log"):
                    file_path = os.path.join(self.log_dir, filename)
                    file_time = datetime.datetime.fromtimestamp(os.path.getctime(file_path))
                    
                    if file_time < cutoff_date:
                        os.remove(file_path)
                        print(f"🗑️ تم حذف السجل القديم: {filename}")
            
        except Exception as e:
            print(f"❌ فشل في حذف السجلات القديمة: {e}")
    
    def generate_error_report(self) -> str:
        """إنشاء تقرير الأخطاء"""
        try:
            report = []
            report.append("📊 تقرير الأخطاء والسجلات")
            report.append("=" * 50)
            report.append(f"📅 التاريخ: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report.append("")
            
            # إحصائيات الأخطاء
            report.append("📈 إحصائيات الأخطاء:")
            for error_type, count in self.error_stats.items():
                report.append(f"  • {error_type}: {count}")
            report.append("")
            
            # آخر الأخطاء
            recent_errors = self.get_recent_errors(5)
            if recent_errors:
                report.append("🔍 آخر الأخطاء:")
                for error in recent_errors:
                    report.append(f"  • {error}")
            else:
                report.append("✅ لا توجد أخطاء حديثة")
            
            return "\n".join(report)
            
        except Exception as e:
            return f"❌ فشل في إنشاء تقرير الأخطاء: {e}"

class BackupManager:
    """مدير النسخ الاحتياطية"""
    
    def __init__(self, backup_dir: str = "backups"):
        """تهيئة مدير النسخ الاحتياطية"""
        self.backup_dir = backup_dir
        self.ensure_backup_directory()
        self.error_handler = ErrorHandler()
    
    def ensure_backup_directory(self):
        """التأكد من وجود مجلد النسخ الاحتياطية"""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
    
    def create_backup(self, source_file: str, backup_name: str = "") -> bool:
        """إنشاء نسخة احتياطية من ملف"""
        try:
            if not os.path.exists(source_file):
                self.error_handler.log_error("FILE_ERROR", 
                                           f"الملف المصدر غير موجود: {source_file}")
                return False
            
            # إنشاء اسم النسخة الاحتياطية
            if not backup_name:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = os.path.basename(source_file)
                name, ext = os.path.splitext(filename)
                backup_name = f"{name}_backup_{timestamp}{ext}"
            
            backup_path = os.path.join(self.backup_dir, backup_name)
            
            # نسخ الملف
            try:
                import shutil
                shutil.copy2(source_file, backup_path)
            except ImportError:
                # نسخ بسيط بدون shutil
                with open(source_file, 'rb') as src, open(backup_path, 'wb') as dst:
                    dst.write(src.read())
            
            self.error_handler.log_activity("BACKUP_CREATED", 
                                          f"تم إنشاء نسخة احتياطية: {backup_name}")
            print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
            return True
            
        except Exception as e:
            self.error_handler.log_error("BACKUP_ERROR", 
                                       f"فشل في إنشاء نسخة احتياطية: {str(e)}")
            return False
    
    def restore_backup(self, backup_name: str, target_file: str) -> bool:
        """استعادة نسخة احتياطية"""
        try:
            backup_path = os.path.join(self.backup_dir, backup_name)
            
            if not os.path.exists(backup_path):
                self.error_handler.log_error("BACKUP_ERROR", 
                                           f"النسخة الاحتياطية غير موجودة: {backup_name}")
                return False
            
            # استعادة الملف
            try:
                import shutil
                shutil.copy2(backup_path, target_file)
            except ImportError:
                # نسخ بسيط بدون shutil
                with open(backup_path, 'rb') as src, open(target_file, 'wb') as dst:
                    dst.write(src.read())
            
            self.error_handler.log_activity("BACKUP_RESTORED", 
                                          f"تم استعادة النسخة الاحتياطية: {backup_name}")
            print(f"✅ تم استعادة النسخة الاحتياطية: {backup_name}")
            return True
            
        except Exception as e:
            self.error_handler.log_error("RESTORE_ERROR", 
                                       f"فشل في استعادة النسخة الاحتياطية: {str(e)}")
            return False
    
    def list_backups(self) -> List[Dict[str, Any]]:
        """قائمة النسخ الاحتياطية المتاحة"""
        try:
            backups = []
            
            for filename in os.listdir(self.backup_dir):
                if filename.endswith(('.xlsx', '.json', '.txt')):
                    file_path = os.path.join(self.backup_dir, filename)
                    file_stats = os.stat(file_path)
                    
                    backups.append({
                        "name": filename,
                        "size": file_stats.st_size,
                        "created": datetime.datetime.fromtimestamp(file_stats.st_ctime),
                        "modified": datetime.datetime.fromtimestamp(file_stats.st_mtime)
                    })
            
            # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
            backups.sort(key=lambda x: x["created"], reverse=True)
            return backups
            
        except Exception as e:
            self.error_handler.log_error("BACKUP_LIST_ERROR", 
                                       f"فشل في قراءة قائمة النسخ الاحتياطية: {str(e)}")
            return []
    
    def cleanup_old_backups(self, days_to_keep: int = 30, max_backups: int = 10):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backups = self.list_backups()
            
            # حذف النسخ القديمة (أكثر من days_to_keep يوم)
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days_to_keep)
            deleted_count = 0
            
            for backup in backups:
                if backup["created"] < cutoff_date:
                    backup_path = os.path.join(self.backup_dir, backup["name"])
                    os.remove(backup_path)
                    deleted_count += 1
                    print(f"🗑️ تم حذف النسخة الاحتياطية القديمة: {backup['name']}")
            
            # الاحتفاظ بعدد محدود من النسخ الأحدث
            remaining_backups = [b for b in backups if b["created"] >= cutoff_date]
            if len(remaining_backups) > max_backups:
                excess_backups = remaining_backups[max_backups:]
                for backup in excess_backups:
                    backup_path = os.path.join(self.backup_dir, backup["name"])
                    os.remove(backup_path)
                    deleted_count += 1
                    print(f"🗑️ تم حذف النسخة الاحتياطية الزائدة: {backup['name']}")
            
            if deleted_count > 0:
                self.error_handler.log_activity("BACKUP_CLEANUP", 
                                              f"تم حذف {deleted_count} نسخة احتياطية قديمة")
            
            return deleted_count
            
        except Exception as e:
            self.error_handler.log_error("CLEANUP_ERROR", 
                                       f"فشل في تنظيف النسخ الاحتياطية: {str(e)}")
            return 0

# إنشاء مثيل عام لاستخدامه في جميع أنحاء النظام
error_handler = ErrorHandler()
backup_manager = BackupManager()
