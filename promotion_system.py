import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import os
from reference_data import get_bank_names, get_nationalities, get_qualifications, get_work_places

# استيراد أنظمة التحسين المتقدمة
try:
    from data_validation import DataValidator, CalculationEngine
    from error_handler import error_handler, backup_manager
    from performance_optimizer import performance_monitor, memory_optimizer
    ENHANCED_SYSTEMS_AVAILABLE = True
    print("✅ تم تحميل أنظمة التحسين المتقدمة لنظام الترقيات")
except ImportError as e:
    ENHANCED_SYSTEMS_AVAILABLE = False
    print(f"⚠️ لم يتم تحميل أنظمة التحسين: {e}")

try:
    from openpyxl import load_workbook, Workbook
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    messagebox.showerror("خطأ", "مكتبة openpyxl غير مثبتة\nيرجى تثبيتها باستخدام: pip install openpyxl")

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    print("تحذير: مكتبة python-docx غير مثبتة - لن تتمكن من إنشاء تقارير Word")

class PromotionSystem:
    def __init__(self, root, employees_data_file="employees_data.xlsx"):
        self.root = root
        self.root.title("⬆️ نظام الترقيات المحسن")
        self.root.geometry("1200x800")
        self.root.configure(bg="#f0f0f0")

        # ملف بيانات الموظفين
        self.employees_data_file = employees_data_file
        self.sheet_name = "الموظفين"

        # تهيئة أنظمة التحسين
        self.validator = None
        self.calculator = None
        if ENHANCED_SYSTEMS_AVAILABLE:
            try:
                self.validator = DataValidator()
                self.calculator = CalculationEngine()
                print("✅ تم تهيئة أنظمة التحسين لنظام الترقيات")
            except Exception as e:
                print(f"⚠️ فشل في تهيئة أنظمة التحسين: {e}")

        # قائمة الترقيات
        self.promotion_list = []
        self.promotion_stats = {}

        # تحميل بيانات الموظفين
        self.employees_data = self.load_employees_data()

        # إنشاء واجهة المستخدم المحسنة
        self.create_enhanced_ui()

        # حساب الترقيات المستحقة
        self.calculate_enhanced_promotions()

    def create_enhanced_ui(self):
        """إنشاء واجهة المستخدم المحسنة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg="#f0f0f0")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # عنوان النظام
        title_frame = tk.Frame(main_frame, bg="#2c3e50", relief=tk.RAISED, bd=2)
        title_frame.pack(fill=tk.X, pady=(0, 10))

        title_label = tk.Label(title_frame, text="⬆️ نظام الترقيات المحسن",
                              font=("Arial", 16, "bold"), fg="white", bg="#2c3e50")
        title_label.pack(pady=10)

        # إطار الإحصائيات السريعة
        stats_frame = tk.LabelFrame(main_frame, text="📊 إحصائيات سريعة",
                                   font=("Arial", 12, "bold"), bg="#f0f0f0")
        stats_frame.pack(fill=tk.X, pady=(0, 10))

        self.stats_inner_frame = tk.Frame(stats_frame, bg="#f0f0f0")
        self.stats_inner_frame.pack(fill=tk.X, padx=10, pady=5)

        # إطار التحكم والتصفية
        control_frame = tk.LabelFrame(main_frame, text="🔧 أدوات التحكم",
                                     font=("Arial", 12, "bold"), bg="#f0f0f0")
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # صف الأزرار الرئيسية
        buttons_frame = tk.Frame(control_frame, bg="#f0f0f0")
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)

        # أزرار العمليات
        self.create_control_buttons(buttons_frame)

        # صف التصفية
        filter_frame = tk.Frame(control_frame, bg="#f0f0f0")
        filter_frame.pack(fill=tk.X, padx=10, pady=5)

        self.create_filter_controls(filter_frame)

        # إطار الجدول
        table_frame = tk.LabelFrame(main_frame, text="📋 قائمة الترقيات",
                                   font=("Arial", 12, "bold"), bg="#f0f0f0")
        table_frame.pack(fill=tk.BOTH, expand=True)

        self.create_enhanced_table(table_frame)

    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons = [
            ("🔄 تحديث البيانات", self.refresh_data, "#3498db"),
            ("⬆️ تطبيق ترقية", self.apply_promotion, "#27ae60"),
            ("📊 تقرير مفصل", self.generate_detailed_report, "#e67e22"),
            ("📄 طباعة", self.print_promotions, "#9b59b6"),
            ("💾 تصدير Excel", self.export_to_excel, "#16a085"),
            ("🔍 مراقب النظام", self.show_system_monitor, "#8e44ad")
        ]

        for i, (text, command, color) in enumerate(buttons):
            btn = tk.Button(parent, text=text, command=command,
                           font=("Arial", 10, "bold"), bg=color, fg="white",
                           relief=tk.RAISED, bd=2, padx=15, pady=5,
                           activebackground=self.darken_color(color))
            btn.grid(row=0, column=i, padx=5, pady=5, sticky="ew")

        # تكوين الأعمدة للتوزيع المتساوي
        for i in range(len(buttons)):
            parent.grid_columnconfigure(i, weight=1)

    def create_filter_controls(self, parent):
        """إنشاء عناصر التصفية"""
        # تصفية حسب الحالة
        tk.Label(parent, text="تصفية حسب:", font=("Arial", 10, "bold"),
                bg="#f0f0f0").grid(row=0, column=0, padx=5, pady=5, sticky="w")

        self.filter_by = tk.StringVar(value="الكل")
        filter_combo = ttk.Combobox(parent, textvariable=self.filter_by,
                                   values=["الكل", "مستحق للترقية", "غير مستحق", "قريب من الاستحقاق"],
                                   state="readonly", width=15)
        filter_combo.grid(row=0, column=1, padx=5, pady=5)
        filter_combo.bind("<<ComboboxSelected>>", lambda e: self.apply_filter())

        # بحث بالاسم
        tk.Label(parent, text="البحث:", font=("Arial", 10, "bold"),
                bg="#f0f0f0").grid(row=0, column=2, padx=5, pady=5, sticky="w")

        self.search_var = tk.StringVar()
        search_entry = tk.Entry(parent, textvariable=self.search_var, width=20)
        search_entry.grid(row=0, column=3, padx=5, pady=5)
        search_entry.bind("<KeyRelease>", lambda e: self.apply_filter())

        # زر مسح التصفية
        clear_btn = tk.Button(parent, text="🗑️ مسح", command=self.clear_filters,
                             font=("Arial", 9), bg="#e74c3c", fg="white",
                             relief=tk.RAISED, bd=1, padx=10)
        clear_btn.grid(row=0, column=4, padx=5, pady=5)

    def darken_color(self, color):
        """تغميق اللون للتأثير البصري"""
        color_map = {
            "#3498db": "#2980b9",
            "#27ae60": "#229954",
            "#e67e22": "#d35400",
            "#9b59b6": "#8e44ad",
            "#16a085": "#138d75",
            "#8e44ad": "#7d3c98"
        }
        return color_map.get(color, color)

    def create_enhanced_table(self, parent):
        """إنشاء جدول الترقيات المحسن"""
        # إطار الجدول مع شريط التمرير
        table_container = tk.Frame(parent, bg="#f0f0f0")
        table_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # إنشاء Treeview
        columns = ("الرقم الوظيفي", "الاسم العربي", "المسمى الوظيفي", "الدرجة الحالية",
                  "تاريخ الدرجة", "سنوات في الدرجة", "تاريخ الاستحقاق", "الحالة", "الدرجة الجديدة")

        self.tree = ttk.Treeview(table_container, columns=columns, show="headings", height=15)

        # تكوين العناوين
        headers = {
            "الرقم الوظيفي": "الرقم الوظيفي",
            "الاسم العربي": "الاسم العربي",
            "المسمى الوظيفي": "المسمى الوظيفي",
            "الدرجة الحالية": "الدرجة الحالية",
            "تاريخ الدرجة": "تاريخ الدرجة الحالية",
            "سنوات في الدرجة": "سنوات في الدرجة",
            "تاريخ الاستحقاق": "تاريخ استحقاق الترقية",
            "الحالة": "حالة الترقية",
            "الدرجة الجديدة": "الدرجة الجديدة"
        }

        # تكوين عرض الأعمدة
        column_widths = {
            "الرقم الوظيفي": 100,
            "الاسم العربي": 150,
            "المسمى الوظيفي": 100,
            "الدرجة الحالية": 80,
            "تاريخ الدرجة": 100,
            "سنوات في الدرجة": 120,
            "تاريخ الاستحقاق": 120,
            "الحالة": 100,
            "الدرجة الجديدة": 100
        }

        for col in columns:
            self.tree.heading(col, text=headers[col], anchor="center")
            self.tree.column(col, width=column_widths[col], anchor="center")

        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_container, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=v_scrollbar.set)

        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_container, orient="horizontal", command=self.tree.xview)
        self.tree.configure(xscrollcommand=h_scrollbar.set)

        # تخطيط الجدول وأشرطة التمرير
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")

        # تكوين التمدد
        table_container.grid_rowconfigure(0, weight=1)
        table_container.grid_columnconfigure(0, weight=1)

        # ربط الأحداث
        self.tree.bind("<Double-1>", self.on_item_double_click)
        self.tree.bind("<Button-3>", self.show_context_menu)

    def calculate_enhanced_promotions(self):
        """حساب الترقيات المستحقة بنظام محسن"""
        if ENHANCED_SYSTEMS_AVAILABLE:
            error_handler.log_activity("PROMOTION_CALCULATION", "بدء حساب الترقيات المحسن", success=True)

        self.promotion_list = []
        self.promotion_stats = {
            'total_employees': 0,
            'eligible_for_promotion': 0,
            'not_eligible': 0,
            'near_eligibility': 0,
            'employees_by_grade': {},
            'average_years_in_grade': 0
        }

        if not self.employees_data:
            if ENHANCED_SYSTEMS_AVAILABLE:
                error_handler.log_error("PROMOTION_ERROR", "لا توجد بيانات موظفين", "قائمة الموظفين فارغة")
            return

        total_years = 0
        current_date = datetime.now()

        for emp in self.employees_data:
            try:
                # التحقق من البيانات الأساسية
                emp_id = emp.get("الرقم الوظيفي", "")
                name = emp.get("الاسم العربي", "")
                job_title = emp.get("المسمى الوظيفي", "")
                current_grade = emp.get("الدرجة الحالية", "")
                grade_date_str = emp.get("تاريخ الدرجة الحالية", "")

                if not all([emp_id, name, current_grade, grade_date_str]):
                    continue

                # تحويل تاريخ الدرجة الحالية
                grade_date = None
                for date_format in ["%Y-%m-%d", "%d/%m/%Y", "%d-%m-%Y"]:
                    try:
                        grade_date = datetime.strptime(str(grade_date_str), date_format)
                        break
                    except ValueError:
                        continue

                if not grade_date:
                    continue

                # حساب سنوات الخدمة في الدرجة الحالية
                if self.calculator:
                    years_in_grade = self.calculator.calculate_service_years(grade_date_str)
                else:
                    duration = current_date - grade_date
                    years_in_grade = duration.days / 365.25

                total_years += years_in_grade

                # حساب تاريخ استحقاق الترقية (4 سنوات)
                try:
                    promotion_due_date = grade_date.replace(year=grade_date.year + 4)
                except ValueError:
                    # في حالة 29 فبراير
                    promotion_due_date = grade_date.replace(year=grade_date.year + 4, day=28)

                # تحديد حالة الترقية
                status, new_grade = self.determine_promotion_status(current_grade, years_in_grade, job_title)

                # إضافة إلى الإحصائيات
                self.update_promotion_stats(current_grade, status)

                # إضافة إلى قائمة الترقيات
                promotion_data = {
                    "الرقم الوظيفي": emp_id,
                    "الاسم العربي": name,
                    "المسمى الوظيفي": job_title,
                    "الدرجة الحالية": current_grade,
                    "تاريخ الدرجة الحالية": grade_date_str,
                    "سنوات في الدرجة": round(years_in_grade, 1),
                    "تاريخ استحقاق الترقية": promotion_due_date.strftime("%Y-%m-%d"),
                    "حالة الترقية": status,
                    "الدرجة الجديدة": new_grade,
                    "أيام متبقية": (promotion_due_date - current_date).days if promotion_due_date > current_date else 0
                }

                self.promotion_list.append(promotion_data)

            except Exception as e:
                if ENHANCED_SYSTEMS_AVAILABLE:
                    error_handler.log_error("PROMOTION_CALC_ERROR", f"خطأ في حساب ترقية الموظف {emp.get('الاسم العربي', 'غير محدد')}", str(e))
                continue

        # حساب المتوسطات
        if self.promotion_list:
            self.promotion_stats['average_years_in_grade'] = round(total_years / len(self.promotion_list), 1)

        # تحديث الواجهة
        self.update_promotions_table()
        self.update_stats_display()

        if ENHANCED_SYSTEMS_AVAILABLE:
            error_handler.log_activity("PROMOTION_CALCULATION", f"تم حساب ترقيات {len(self.promotion_list)} موظف", success=True)

    def determine_promotion_status(self, current_grade, years_in_grade, job_title):
        """تحديد حالة الترقية والدرجة الجديدة"""
        try:
            current_grade_num = int(current_grade)
        except (ValueError, TypeError):
            return "بيانات غير صحيحة", ""

        # قواعد الترقية المحسنة
        if years_in_grade >= 4:
            # مستحق للترقية
            if current_grade_num >= 15:
                return "وصل للحد الأقصى", str(current_grade_num)
            else:
                new_grade = current_grade_num + 1
                return "مستحق للترقية", str(new_grade)
        elif years_in_grade >= 3:
            # قريب من الاستحقاق
            return "قريب من الاستحقاق", ""
        else:
            # غير مستحق
            return "غير مستحق", ""

    def update_promotion_stats(self, current_grade, status):
        """تحديث إحصائيات الترقيات"""
        self.promotion_stats['total_employees'] += 1

        if status == "مستحق للترقية":
            self.promotion_stats['eligible_for_promotion'] += 1
        elif status == "قريب من الاستحقاق":
            self.promotion_stats['near_eligibility'] += 1
        else:
            self.promotion_stats['not_eligible'] += 1

        # إحصائيات حسب الدرجة
        if current_grade in self.promotion_stats['employees_by_grade']:
            self.promotion_stats['employees_by_grade'][current_grade] += 1
        else:
            self.promotion_stats['employees_by_grade'][current_grade] = 1

    def update_stats_display(self):
        """تحديث عرض الإحصائيات"""
        # مسح الإحصائيات السابقة
        for widget in self.stats_inner_frame.winfo_children():
            widget.destroy()

        stats = self.promotion_stats

        # إنشاء بطاقات الإحصائيات
        stat_cards = [
            ("👥 إجمالي الموظفين", stats['total_employees'], "#3498db"),
            ("⬆️ مستحق للترقية", stats['eligible_for_promotion'], "#27ae60"),
            ("⏳ قريب من الاستحقاق", stats['near_eligibility'], "#f39c12"),
            ("❌ غير مستحق", stats['not_eligible'], "#e74c3c"),
            ("📊 متوسط السنوات", f"{stats['average_years_in_grade']} سنة", "#9b59b6")
        ]

        for i, (title, value, color) in enumerate(stat_cards):
            card_frame = tk.Frame(self.stats_inner_frame, bg=color, relief=tk.RAISED, bd=2)
            card_frame.grid(row=0, column=i, padx=5, pady=5, sticky="ew")

            title_label = tk.Label(card_frame, text=title, font=("Arial", 9, "bold"),
                                  fg="white", bg=color)
            title_label.pack(pady=(5, 0))

            value_label = tk.Label(card_frame, text=str(value), font=("Arial", 12, "bold"),
                                  fg="white", bg=color)
            value_label.pack(pady=(0, 5))

        # تكوين التوزيع المتساوي
        for i in range(len(stat_cards)):
            self.stats_inner_frame.grid_columnconfigure(i, weight=1)

    def update_promotions_table(self):
        """تحديث جدول الترقيات"""
        # مسح البيانات السابقة
        for item in self.tree.get_children():
            self.tree.delete(item)

        # إضافة البيانات الجديدة
        for promotion in self.promotion_list:
            # تحديد لون الصف حسب الحالة
            tags = []
            if promotion["حالة الترقية"] == "مستحق للترقية":
                tags = ["eligible"]
            elif promotion["حالة الترقية"] == "قريب من الاستحقاق":
                tags = ["near"]
            elif promotion["حالة الترقية"] == "وصل للحد الأقصى":
                tags = ["max"]

            values = (
                promotion["الرقم الوظيفي"],
                promotion["الاسم العربي"],
                promotion["المسمى الوظيفي"],
                promotion["الدرجة الحالية"],
                promotion["تاريخ الدرجة الحالية"],
                f"{promotion['سنوات في الدرجة']} سنة",
                promotion["تاريخ استحقاق الترقية"],
                promotion["حالة الترقية"],
                promotion["الدرجة الجديدة"]
            )

            self.tree.insert("", "end", values=values, tags=tags)

        # تكوين ألوان الصفوف
        self.tree.tag_configure("eligible", background="#d5f4e6")
        self.tree.tag_configure("near", background="#fff3cd")
        self.tree.tag_configure("max", background="#f8d7da")

    def apply_filter(self):
        """تطبيق التصفية على الجدول"""
        filter_value = self.filter_by.get()
        search_text = self.search_var.get().lower()

        # مسح الجدول
        for item in self.tree.get_children():
            self.tree.delete(item)

        # تطبيق التصفية
        for promotion in self.promotion_list:
            # تصفية حسب الحالة
            if filter_value != "الكل" and promotion["حالة الترقية"] != filter_value:
                continue

            # تصفية حسب البحث
            if search_text and search_text not in promotion["الاسم العربي"].lower():
                continue

            # إضافة الصف
            tags = []
            if promotion["حالة الترقية"] == "مستحق للترقية":
                tags = ["eligible"]
            elif promotion["حالة الترقية"] == "قريب من الاستحقاق":
                tags = ["near"]
            elif promotion["حالة الترقية"] == "وصل للحد الأقصى":
                tags = ["max"]

            values = (
                promotion["الرقم الوظيفي"],
                promotion["الاسم العربي"],
                promotion["المسمى الوظيفي"],
                promotion["الدرجة الحالية"],
                promotion["تاريخ الدرجة الحالية"],
                f"{promotion['سنوات في الدرجة']} سنة",
                promotion["تاريخ استحقاق الترقية"],
                promotion["حالة الترقية"],
                promotion["الدرجة الجديدة"]
            )

            self.tree.insert("", "end", values=values, tags=tags)

        # تكوين ألوان الصفوف
        self.tree.tag_configure("eligible", background="#d5f4e6")
        self.tree.tag_configure("near", background="#fff3cd")
        self.tree.tag_configure("max", background="#f8d7da")

    def clear_filters(self):
        """مسح جميع التصفيات"""
        self.filter_by.set("الكل")
        self.search_var.set("")
        self.update_promotions_table()

    def refresh_data(self):
        """تحديث البيانات"""
        if ENHANCED_SYSTEMS_AVAILABLE:
            error_handler.log_activity("PROMOTION_REFRESH", "بدء تحديث بيانات الترقيات", success=True)

        try:
            # إنشاء نسخة احتياطية
            if ENHANCED_SYSTEMS_AVAILABLE:
                backup_manager.create_backup(self.employees_data_file)

            # إعادة تحميل البيانات
            self.employees_data = self.load_employees_data()

            # إعادة حساب الترقيات
            self.calculate_enhanced_promotions()

            messagebox.showinfo("تحديث البيانات", "تم تحديث البيانات بنجاح")

            if ENHANCED_SYSTEMS_AVAILABLE:
                error_handler.log_activity("PROMOTION_REFRESH", "تم تحديث بيانات الترقيات بنجاح", success=True)

        except Exception as e:
            error_msg = f"فشل في تحديث البيانات: {e}"
            messagebox.showerror("خطأ", error_msg)

            if ENHANCED_SYSTEMS_AVAILABLE:
                error_handler.log_error("PROMOTION_REFRESH_ERROR", "فشل في تحديث بيانات الترقيات", str(e))

    def apply_promotion(self):
        """تطبيق ترقية للموظف المحدد"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار موظف من القائمة")
            return

        # الحصول على بيانات الموظف المحدد
        item_values = self.tree.item(selected_item[0])['values']
        emp_id = item_values[0]
        emp_name = item_values[1]
        current_grade = item_values[3]
        status = item_values[7]
        new_grade = item_values[8]

        if status != "مستحق للترقية":
            messagebox.showwarning("تحذير", f"الموظف {emp_name} غير مستحق للترقية حالياً")
            return

        # تأكيد الترقية
        confirm = messagebox.askyesno("تأكيد الترقية",
                                     f"هل تريد ترقية الموظف {emp_name}\n"
                                     f"من الدرجة {current_grade} إلى الدرجة {new_grade}؟")

        if confirm:
            try:
                # تطبيق الترقية في قاعدة البيانات
                self.update_employee_grade(emp_id, new_grade)

                messagebox.showinfo("نجح", f"تم ترقية الموظف {emp_name} بنجاح")

                # تحديث البيانات
                self.refresh_data()

                if ENHANCED_SYSTEMS_AVAILABLE:
                    error_handler.log_activity("PROMOTION_APPLIED",
                                             f"تم ترقية الموظف {emp_name} من الدرجة {current_grade} إلى {new_grade}",
                                             success=True)

            except Exception as e:
                error_msg = f"فشل في تطبيق الترقية: {e}"
                messagebox.showerror("خطأ", error_msg)

                if ENHANCED_SYSTEMS_AVAILABLE:
                    error_handler.log_error("PROMOTION_APPLY_ERROR", f"فشل في ترقية الموظف {emp_name}", str(e))

    def show_system_monitor(self):
        """عرض مراقب النظام"""
        try:
            from system_monitor import show_system_monitor
            show_system_monitor(self.root)
        except ImportError:
            messagebox.showinfo("مراقب النظام",
                              "مراقب النظام المحسن غير متاح\n"
                              "يرجى التأكد من وجود ملف system_monitor.py")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تشغيل مراقب النظام: {e}")

    def generate_detailed_report(self):
        """إنشاء تقرير مفصل للترقيات"""
        try:
            report_window = tk.Toplevel(self.root)
            report_window.title("📊 تقرير الترقيات المفصل")
            report_window.geometry("800x600")
            report_window.configure(bg="#f0f0f0")

            # إطار التقرير
            report_frame = tk.Frame(report_window, bg="#f0f0f0")
            report_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # عنوان التقرير
            title_label = tk.Label(report_frame, text="📊 تقرير الترقيات المفصل",
                                  font=("Arial", 16, "bold"), bg="#f0f0f0")
            title_label.pack(pady=(0, 20))

            # منطقة النص
            text_frame = tk.Frame(report_frame)
            text_frame.pack(fill=tk.BOTH, expand=True)

            text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Arial", 11))
            scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # إنشاء محتوى التقرير
            report_content = self.generate_report_content()
            text_widget.insert(tk.END, report_content)
            text_widget.config(state=tk.DISABLED)

            # أزرار التحكم
            buttons_frame = tk.Frame(report_frame, bg="#f0f0f0")
            buttons_frame.pack(fill=tk.X, pady=(20, 0))

            save_btn = tk.Button(buttons_frame, text="💾 حفظ التقرير",
                               command=lambda: self.save_report(report_content),
                               font=("Arial", 10, "bold"), bg="#27ae60", fg="white")
            save_btn.pack(side=tk.LEFT, padx=(0, 10))

            print_btn = tk.Button(buttons_frame, text="🖨️ طباعة",
                                command=lambda: self.print_report(report_content),
                                font=("Arial", 10, "bold"), bg="#3498db", fg="white")
            print_btn.pack(side=tk.LEFT, padx=(0, 10))

            close_btn = tk.Button(buttons_frame, text="❌ إغلاق",
                                command=report_window.destroy,
                                font=("Arial", 10, "bold"), bg="#e74c3c", fg="white")
            close_btn.pack(side=tk.RIGHT)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير: {e}")

    def generate_report_content(self):
        """إنشاء محتوى التقرير"""
        current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        content = f"""
📊 تقرير الترقيات المفصل
{'='*60}

📅 تاريخ التقرير: {current_date}
👥 إجمالي الموظفين: {self.promotion_stats['total_employees']}

📈 إحصائيات الترقيات:
{'='*40}
⬆️ مستحق للترقية: {self.promotion_stats['eligible_for_promotion']} موظف
⏳ قريب من الاستحقاق: {self.promotion_stats['near_eligibility']} موظف
❌ غير مستحق: {self.promotion_stats['not_eligible']} موظف
📊 متوسط السنوات في الدرجة: {self.promotion_stats['average_years_in_grade']} سنة

📋 توزيع الموظفين حسب الدرجة:
{'='*40}
"""

        # إضافة توزيع الدرجات
        for grade, count in sorted(self.promotion_stats['employees_by_grade'].items()):
            content += f"الدرجة {grade}: {count} موظف\n"

        content += f"\n📝 تفاصيل الترقيات:\n{'='*40}\n"

        # إضافة تفاصيل كل موظف
        for i, promotion in enumerate(self.promotion_list, 1):
            content += f"""
{i}. {promotion['الاسم العربي']} (رقم: {promotion['الرقم الوظيفي']})
   المسمى الوظيفي: {promotion['المسمى الوظيفي']}
   الدرجة الحالية: {promotion['الدرجة الحالية']}
   سنوات في الدرجة: {promotion['سنوات في الدرجة']} سنة
   تاريخ الاستحقاق: {promotion['تاريخ استحقاق الترقية']}
   الحالة: {promotion['حالة الترقية']}
   الدرجة الجديدة: {promotion['الدرجة الجديدة'] or 'غير محدد'}
   {'─'*50}
"""

        content += f"\n📊 ملخص التوصيات:\n{'='*40}\n"

        if self.promotion_stats['eligible_for_promotion'] > 0:
            content += f"• يوصى بترقية {self.promotion_stats['eligible_for_promotion']} موظف فوراً\n"

        if self.promotion_stats['near_eligibility'] > 0:
            content += f"• {self.promotion_stats['near_eligibility']} موظف سيستحق الترقية قريباً\n"

        content += f"\n📋 تم إنشاء هذا التقرير بواسطة نظام الترقيات المحسن\n"

        return content

    def save_report(self, content):
        """حفظ التقرير في ملف"""
        try:
            from tkinter import filedialog

            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                title="حفظ تقرير الترقيات"
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("نجح", f"تم حفظ التقرير في:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ التقرير: {e}")

    def print_report(self, content):
        """طباعة التقرير"""
        try:
            import tempfile
            import os

            # إنشاء ملف مؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
                f.write(content)
                temp_filename = f.name

            # فتح الملف للطباعة
            os.startfile(temp_filename, "print")

            messagebox.showinfo("طباعة", "تم إرسال التقرير للطباعة")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة التقرير: {e}")

    def print_promotions(self):
        """طباعة قائمة الترقيات"""
        try:
            from printing_system import PrintingSystem
            printer = PrintingSystem()

            # تحضير البيانات للطباعة
            print_data = []
            for promotion in self.promotion_list:
                if promotion["حالة الترقية"] == "مستحق للترقية":
                    print_data.append(promotion)

            if not print_data:
                messagebox.showwarning("تحذير", "لا توجد ترقيات مستحقة للطباعة")
                return

            # طباعة البيانات
            printer.print_promotions_list(print_data)

        except ImportError:
            messagebox.showinfo("طباعة", "نظام الطباعة غير متاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في الطباعة: {e}")

    def export_to_excel(self):
        """تصدير البيانات إلى Excel"""
        try:
            from tkinter import filedialog
            from openpyxl import Workbook
            from openpyxl.styles import Font, PatternFill, Alignment

            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="تصدير بيانات الترقيات"
            )

            if not filename:
                return

            # إنشاء ملف Excel
            wb = Workbook()
            ws = wb.active
            ws.title = "الترقيات"

            # العناوين
            headers = ["الرقم الوظيفي", "الاسم العربي", "المسمى الوظيفي", "الدرجة الحالية",
                      "تاريخ الدرجة الحالية", "سنوات في الدرجة", "تاريخ استحقاق الترقية",
                      "حالة الترقية", "الدرجة الجديدة"]

            # كتابة العناوين
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                cell.alignment = Alignment(horizontal="center")

            # كتابة البيانات
            for row, promotion in enumerate(self.promotion_list, 2):
                ws.cell(row=row, column=1, value=promotion["الرقم الوظيفي"])
                ws.cell(row=row, column=2, value=promotion["الاسم العربي"])
                ws.cell(row=row, column=3, value=promotion["المسمى الوظيفي"])
                ws.cell(row=row, column=4, value=promotion["الدرجة الحالية"])
                ws.cell(row=row, column=5, value=promotion["تاريخ الدرجة الحالية"])
                ws.cell(row=row, column=6, value=f"{promotion['سنوات في الدرجة']} سنة")
                ws.cell(row=row, column=7, value=promotion["تاريخ استحقاق الترقية"])
                ws.cell(row=row, column=8, value=promotion["حالة الترقية"])
                ws.cell(row=row, column=9, value=promotion["الدرجة الجديدة"])

                # تلوين الصفوف حسب الحالة
                if promotion["حالة الترقية"] == "مستحق للترقية":
                    fill = PatternFill(start_color="D5F4E6", end_color="D5F4E6", fill_type="solid")
                elif promotion["حالة الترقية"] == "قريب من الاستحقاق":
                    fill = PatternFill(start_color="FFF3CD", end_color="FFF3CD", fill_type="solid")
                else:
                    fill = PatternFill(start_color="F8D7DA", end_color="F8D7DA", fill_type="solid")

                for col in range(1, len(headers) + 1):
                    ws.cell(row=row, column=col).fill = fill

            # تعديل عرض الأعمدة
            for col in range(1, len(headers) + 1):
                ws.column_dimensions[chr(64 + col)].width = 15

            # حفظ الملف
            wb.save(filename)
            messagebox.showinfo("نجح", f"تم تصدير البيانات إلى:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير البيانات: {e}")

    def on_item_double_click(self, event):
        """معالجة النقر المزدوج على عنصر"""
        selected_item = self.tree.selection()
        if selected_item:
            # عرض تفاصيل الموظف
            self.show_employee_details(selected_item[0])

    def show_context_menu(self, event):
        """عرض القائمة السياقية"""
        try:
            # إنشاء القائمة السياقية
            context_menu = tk.Menu(self.root, tearoff=0)
            context_menu.add_command(label="⬆️ تطبيق ترقية", command=self.apply_promotion)
            context_menu.add_command(label="👁️ عرض التفاصيل", command=lambda: self.show_employee_details(self.tree.selection()[0]) if self.tree.selection() else None)
            context_menu.add_separator()
            context_menu.add_command(label="📋 نسخ الرقم الوظيفي", command=self.copy_employee_id)

            # عرض القائمة
            context_menu.tk_popup(event.x_root, event.y_root)

        except Exception as e:
            pass

    def show_employee_details(self, item):
        """عرض تفاصيل الموظف"""
        try:
            values = self.tree.item(item)['values']

            details_window = tk.Toplevel(self.root)
            details_window.title("👁️ تفاصيل الموظف")
            details_window.geometry("500x400")
            details_window.configure(bg="#f0f0f0")

            # إطار التفاصيل
            details_frame = tk.Frame(details_window, bg="#f0f0f0")
            details_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # العنوان
            title_label = tk.Label(details_frame, text=f"👁️ تفاصيل الموظف: {values[1]}",
                                  font=("Arial", 14, "bold"), bg="#f0f0f0")
            title_label.pack(pady=(0, 20))

            # التفاصيل
            details = [
                ("الرقم الوظيفي", values[0]),
                ("الاسم العربي", values[1]),
                ("المسمى الوظيفي", values[2]),
                ("الدرجة الحالية", values[3]),
                ("تاريخ الدرجة الحالية", values[4]),
                ("سنوات في الدرجة", values[5]),
                ("تاريخ استحقاق الترقية", values[6]),
                ("حالة الترقية", values[7]),
                ("الدرجة الجديدة", values[8] if values[8] else "غير محدد")
            ]

            for label, value in details:
                row_frame = tk.Frame(details_frame, bg="#f0f0f0")
                row_frame.pack(fill=tk.X, pady=5)

                tk.Label(row_frame, text=f"{label}:", font=("Arial", 10, "bold"),
                        bg="#f0f0f0", width=20, anchor="w").pack(side=tk.LEFT)
                tk.Label(row_frame, text=str(value), font=("Arial", 10),
                        bg="#f0f0f0", anchor="w").pack(side=tk.LEFT, padx=(10, 0))

            # زر الإغلاق
            close_btn = tk.Button(details_frame, text="❌ إغلاق",
                                command=details_window.destroy,
                                font=("Arial", 10, "bold"), bg="#e74c3c", fg="white")
            close_btn.pack(pady=(20, 0))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض التفاصيل: {e}")

    def copy_employee_id(self):
        """نسخ الرقم الوظيفي للحافظة"""
        try:
            selected_item = self.tree.selection()
            if selected_item:
                emp_id = self.tree.item(selected_item[0])['values'][0]
                self.root.clipboard_clear()
                self.root.clipboard_append(str(emp_id))
                messagebox.showinfo("نسخ", f"تم نسخ الرقم الوظيفي: {emp_id}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في النسخ: {e}")

    def load_employees_data(self):
        """تحميل بيانات الموظفين من ملف Excel"""
        try:
            if not OPENPYXL_AVAILABLE:
                return []

            if not os.path.exists(self.employees_data_file):
                return []

            wb = load_workbook(self.employees_data_file)
            if self.sheet_name not in wb.sheetnames:
                return []

            ws = wb[self.sheet_name]
            employees_data = []

            # قراءة العناوين من الصف الأول
            headers = []
            for cell in ws[1]:
                if cell.value:
                    headers.append(cell.value)
                else:
                    break

            # قراءة البيانات
            for row in ws.iter_rows(min_row=2, values_only=True):
                if row[0]:  # إذا كان هناك رقم وظيفي
                    employee = {}
                    for i, value in enumerate(row):
                        if i < len(headers):
                            employee[headers[i]] = value
                    employees_data.append(employee)

            return employees_data

        except Exception as e:
            if ENHANCED_SYSTEMS_AVAILABLE:
                error_handler.log_error("PROMOTION_LOAD_ERROR", "فشل في تحميل بيانات الموظفين", str(e))
            return []

    def update_employee_grade(self, emp_id, new_grade):
        """تحديث درجة الموظف في قاعدة البيانات"""
        try:
            if not OPENPYXL_AVAILABLE:
                raise Exception("مكتبة openpyxl غير متاحة")

            wb = load_workbook(self.employees_data_file)
            ws = wb[self.sheet_name]

            # البحث عن الموظف وتحديث درجته
            headers = [cell.value for cell in ws[1]]
            emp_id_col = headers.index("الرقم الوظيفي") + 1
            grade_col = headers.index("الدرجة الحالية") + 1
            grade_date_col = headers.index("تاريخ الدرجة الحالية") + 1

            for row in range(2, ws.max_row + 1):
                if str(ws.cell(row=row, column=emp_id_col).value) == str(emp_id):
                    # تحديث الدرجة
                    ws.cell(row=row, column=grade_col, value=new_grade)
                    # تحديث تاريخ الدرجة
                    ws.cell(row=row, column=grade_date_col, value=datetime.now().strftime("%Y-%m-%d"))
                    break

            # حفظ الملف
            wb.save(self.employees_data_file)

            if ENHANCED_SYSTEMS_AVAILABLE:
                error_handler.log_activity("PROMOTION_UPDATE", f"تم تحديث درجة الموظف {emp_id} إلى {new_grade}", success=True)

        except Exception as e:
            if ENHANCED_SYSTEMS_AVAILABLE:
                error_handler.log_error("PROMOTION_UPDATE_ERROR", f"فشل في تحديث درجة الموظف {emp_id}", str(e))
            raise e
    
    def load_employees_data(self):
        """تحميل بيانات الموظفين من ملف Excel"""
        try:
            wb = load_workbook(self.employees_data_file)
            # محاولة العثور على الورقة بأسماء مختلفة
            if "الموظفين" in wb.sheetnames:
                ws = wb["الموظفين"]
            elif "Employees" in wb.sheetnames:
                ws = wb["Employees"]
            else:
                ws = wb.active
            
            data = []
            headers = [cell.value for cell in ws[1]]
            
            for row in ws.iter_rows(min_row=2, values_only=True):
                if any(row):
                    data.append(dict(zip(headers, row)))
            
            return data
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل بيانات الموظفين: {str(e)}")
            return []
    
    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        # إطار رئيسي
        main_frame = tk.Frame(self.root, padx=10, pady=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # إطار التصفية
        filter_frame = tk.LabelFrame(main_frame, text="تصفية الموظفين", padx=5, pady=5)
        filter_frame.pack(fill=tk.X, pady=5)
        
        tk.Label(filter_frame, text="التصفية حسب:").grid(row=0, column=0, padx=5, sticky="e")
        
        self.filter_by = ttk.Combobox(filter_frame, values=[
            "الدرجة الحالية", "المسمى الوظيفي", "مكان العمل", "مستحق للترقية", "الكل"
        ], state="readonly", width=15)
        self.filter_by.grid(row=0, column=1, padx=5, sticky="w")
        self.filter_by.current(4)  # تحديد "الكل" افتراضيًا
        
        self.filter_entry = tk.Entry(filter_frame, width=30)
        self.filter_entry.grid(row=0, column=2, padx=5, sticky="w")
        
        filter_btn = tk.Button(filter_frame, text="تصفية", command=self.filter_employees)
        filter_btn.grid(row=0, column=3, padx=5)
        
        reset_btn = tk.Button(filter_frame, text="إعادة تعيين", command=self.reset_filter)
        reset_btn.grid(row=0, column=4, padx=5)
        
        # إطار جدول الموظفين المستحقين للترقية
        table_frame = tk.Frame(main_frame)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # شريط التمرير
        scroll_y = tk.Scrollbar(table_frame, orient=tk.VERTICAL)
        scroll_x = tk.Scrollbar(table_frame, orient=tk.HORIZONTAL)
        
        # إنشاء الجدول
        self.promotions_table = ttk.Treeview(
            table_frame, 
            yscrollcommand=scroll_y.set, 
            xscrollcommand=scroll_x.set,
            selectmode="browse"
        )
        
        scroll_y.config(command=self.promotions_table.yview)
        scroll_x.config(command=self.promotions_table.xview)
        
        # تعريف الأعمدة
        self.promotions_table["columns"] = (
            "الاسم العربي", "الرقم الوظيفي", "الدرجة الحالية",
            "تاريخ الدرجة الحالية", "تاريخ استحقاق الترقية",
            "مستحق للترقية", "الدرجة الجديدة"
        )
        
        # تنسيق الأعمدة
        self.promotions_table.column("#0", width=0, stretch=tk.NO)
        self.promotions_table.column("الاسم العربي", width=150, anchor="center")
        self.promotions_table.column("الرقم الوظيفي", width=100, anchor="center")
        self.promotions_table.column("الدرجة الحالية", width=100, anchor="center")
        self.promotions_table.column("تاريخ الدرجة الحالية", width=120, anchor="center")
        self.promotions_table.column("تاريخ استحقاق الترقية", width=140, anchor="center")
        self.promotions_table.column("مستحق للترقية", width=100, anchor="center")
        self.promotions_table.column("الدرجة الجديدة", width=100, anchor="center")
        
        # عناوين الأعمدة
        self.promotions_table.heading("#0", text="", anchor="center")
        self.promotions_table.heading("الاسم العربي", text="الاسم العربي", anchor="center")
        self.promotions_table.heading("الرقم الوظيفي", text="الرقم الوظيفي", anchor="center")
        self.promotions_table.heading("الدرجة الحالية", text="الدرجة الحالية", anchor="center")
        self.promotions_table.heading("تاريخ الدرجة الحالية", text="تاريخ الدرجة الحالية", anchor="center")
        self.promotions_table.heading("تاريخ استحقاق الترقية", text="تاريخ استحقاق الترقية", anchor="center")
        self.promotions_table.heading("مستحق للترقية", text="مستحق للترقية", anchor="center")
        self.promotions_table.heading("الدرجة الجديدة", text="الدرجة الجديدة", anchor="center")
        
        # وضع الجدول وشريط التمرير في الواجهة
        self.promotions_table.grid(row=0, column=0, sticky="nsew")
        scroll_y.grid(row=0, column=1, sticky="ns")
        scroll_x.grid(row=1, column=0, sticky="ew")
        
        # جعل الجدول قابل للتوسع
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=5)
        
        calculate_btn = tk.Button(buttons_frame, text="تحديث الحساب", command=self.calculate_promotions)
        calculate_btn.pack(side=tk.LEFT, padx=5)

        print_btn = tk.Button(buttons_frame, text="طباعة التقرير", command=self.print_report)
        print_btn.pack(side=tk.LEFT, padx=5)
    
    def calculate_promotions(self):
        """حساب الترقيات المستحقة للموظفين"""
        self.promotion_list = []
        
        for emp in self.employees_data:
            current_grade = emp.get("الدرجة الحالية", "")
            grade_date_str = emp.get("تاريخ الدرجة الحالية", "")
            
            if not current_grade or not grade_date_str:
                continue
                
            try:
                # تحويل تاريخ الدرجة الحالية إلى كائن تاريخ
                grade_date = datetime.strptime(grade_date_str, "%Y-%m-%d")
                
                # حساب المدة في الدرجة الحالية (بالسنوات)
                today = datetime.now()
                duration = today - grade_date
                years_in_grade = duration.days / 365.25

                # حساب تاريخ استحقاق الترقية (4 سنوات من تاريخ الدرجة الحالية)
                try:
                    from dateutil.relativedelta import relativedelta
                    promotion_due_date = grade_date + relativedelta(years=4)
                except ImportError:
                    # إذا لم تكن مكتبة dateutil متاحة، استخدم حساب بسيط
                    promotion_due_date = grade_date.replace(year=grade_date.year + 4)

                promotion_due_str = promotion_due_date.strftime("%Y-%m-%d")

                # تحديد إذا كان الموظف مستحق للترقية
                eligible = False
                new_grade = ""

                try:
                    current_grade_num = int(current_grade)

                    # حساب الترقية حسب القواعد المحددة
                    if current_grade_num < 9 and years_in_grade >= 4:
                        eligible = True
                        new_grade = str(current_grade_num + 1)
                    elif current_grade_num >= 9 and years_in_grade >= 4:
                        eligible = True
                        new_grade = str(current_grade_num + 1)
                except ValueError:
                    pass
                
                # إضافة الموظف إلى قائمة الترقيات
                self.promotion_list.append({
                    "الاسم العربي": emp.get("الاسم العربي", ""),
                    "الرقم الوظيفي": emp.get("الرقم الوظيفي", ""),
                    "الدرجة الحالية": current_grade,
                    "تاريخ الدرجة الحالية": grade_date_str,
                    "تاريخ استحقاق الترقية": promotion_due_str,
                    "مستحق للترقية": "نعم" if eligible else "لا",
                    "الدرجة الجديدة": new_grade if eligible else ""
                })
                
            except ValueError:
                continue
        
        # تحديث الجدول بالنتائج
        self.update_promotions_table()
    
    def update_promotions_table(self, data=None):
        """تحديث جدول الترقيات بالبيانات"""
        # مسح البيانات الحالية
        for item in self.promotions_table.get_children():
            self.promotions_table.delete(item)
        
        # استخدام البيانات المقدمة أو البيانات الكاملة
        display_data = data if data else self.promotion_list
        
        # إضافة البيانات إلى الجدول
        for promo in display_data:
            self.promotions_table.insert("", tk.END, values=(
                promo.get("الاسم العربي", ""),
                promo.get("الرقم الوظيفي", ""),
                promo.get("الدرجة الحالية", ""),
                promo.get("تاريخ الدرجة الحالية", ""),
                promo.get("تاريخ استحقاق الترقية", ""),
                promo.get("مستحق للترقية", ""),
                promo.get("الدرجة الجديدة", "")
            ))
    
    def filter_employees(self):
        """تصفية الموظفين حسب المعايير المحددة"""
        filter_criteria = self.filter_by.get()
        filter_term = self.filter_entry.get().strip().lower()
        
        if filter_criteria == "الكل":
            self.update_promotions_table()
            return

        # خيار "مستحق للترقية" لا يحتاج مصطلح بحث
        if filter_criteria == "مستحق للترقية":
            filtered_data = [promo for promo in self.promotion_list if promo.get("مستحق للترقية") == "نعم"]
            if not filtered_data:
                messagebox.showinfo("نتائج التصفية", "لا توجد ترقيات مستحقة")
            else:
                self.update_promotions_table(filtered_data)
            return

        if not filter_term:
            messagebox.showwarning("تحذير", "الرجاء إدخال مصطلح التصفية")
            return

        filtered_data = []
        for promo in self.promotion_list:
            if filter_criteria == "الدرجة الحالية":
                if filter_term in str(promo.get("الدرجة الحالية", "")).lower():
                    filtered_data.append(promo)
            elif filter_criteria == "المسمى الوظيفي":
                # نحتاج إلى البحث في بيانات الموظفين الأصلية
                emp_id = promo.get("الرقم الوظيفي", "")
                for emp in self.employees_data:
                    if emp.get("الرقم الوظيفي", "") == emp_id:
                        if filter_term in emp.get("المسمى الوظيفي", "").lower():
                            filtered_data.append(promo)
                        break
            elif filter_criteria == "مكان العمل":
                # نحتاج إلى البحث في بيانات الموظفين الأصلية
                emp_id = promo.get("الرقم الوظيفي", "")
                for emp in self.employees_data:
                    if emp.get("الرقم الوظيفي", "") == emp_id:
                        if filter_term in emp.get("مكان العمل الحالي", "").lower():
                            filtered_data.append(promo)
                        break
        
        if not filtered_data:
            messagebox.showinfo("نتائج التصفية", "لا توجد نتائج مطابقة للتصفية")
        else:
            self.update_promotions_table(filtered_data)
    
    def reset_filter(self):
        """إعادة تعيين التصفية وعرض جميع الموظفين"""
        self.filter_entry.delete(0, tk.END)
        self.filter_by.current(4)  # تحديد "الكل" (الآن في الموضع 4)
        self.update_promotions_table()
    

    def print_report(self):
        """طباعة تقرير الترقيات"""
        if not hasattr(self, 'promotion_list') or not self.promotion_list:
            messagebox.showwarning("تحذير", "لا توجد بيانات ترقيات للطباعة")
            return

        # إنشاء نافذة خيارات الطباعة
        self.show_promotion_print_options()

    def show_promotion_print_options(self):
        """عرض خيارات طباعة تقرير الترقيات"""
        print_window = tk.Toplevel(self.root)
        print_window.title("طباعة تقرير الترقيات")
        print_window.geometry("550x450")
        print_window.grab_set()

        # إطار الخيارات
        options_frame = tk.LabelFrame(print_window, text="خيارات طباعة تقرير الترقيات", padx=10, pady=10)
        options_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # نوع التقرير
        tk.Label(options_frame, text="تقرير الترقيات المستحقة",
                font=("Arial", 12, "bold")).pack(anchor="w", pady=5)

        # تصفية التقرير
        tk.Label(options_frame, text="تصفية التقرير:", font=("Arial", 12)).pack(anchor="w", pady=5)

        self.promotion_filter_var = tk.StringVar(master=print_window, value="all")

        filters = [
            ("جميع الموظفين", "all"),
            ("المستحقين للترقية فقط", "eligible"),
            ("غير المستحقين فقط", "not_eligible")
        ]

        for text, value in filters:
            tk.Radiobutton(options_frame, text=text, variable=self.promotion_filter_var, value=value).pack(anchor="w")

        # نوع الملف
        tk.Label(options_frame, text="تنسيق الطباعة:", font=("Arial", 12)).pack(anchor="w", pady=(20, 5))

        self.promo_format_var = tk.StringVar(master=print_window, value="pdf")

        formats = [
            ("PDF", "pdf", "ملف PDF للطباعة"),
            ("Word", "word", "مستند Word للطباعة"),
            ("كلاهما", "both", "إنشاء كلا التنسيقين")
        ]

        for text, value, desc in formats:
            frame = tk.Frame(options_frame)
            frame.pack(fill=tk.X, pady=2)

            rb = tk.Radiobutton(frame, text=text, variable=self.promo_format_var, value=value)
            rb.pack(side=tk.LEFT)

            tk.Label(frame, text=f"- {desc}", font=("Arial", 9), fg="gray").pack(side=tk.LEFT, padx=(10, 0))

        # خيارات إضافية
        tk.Label(options_frame, text="خيارات إضافية:", font=("Arial", 12)).pack(anchor="w", pady=(20, 5))

        self.promo_auto_open_var = tk.BooleanVar(master=print_window, value=True)
        tk.Checkbutton(options_frame, text="فتح الملف بعد الإنشاء",
                      variable=self.promo_auto_open_var).pack(anchor="w")

        self.promo_auto_print_var = tk.BooleanVar(master=print_window, value=False)
        tk.Checkbutton(options_frame, text="طباعة مباشرة",
                      variable=self.promo_auto_print_var).pack(anchor="w")

        # ملاحظة للمستخدم
        note_label = tk.Label(options_frame, text="ملاحظة: التقارير للعرض والطباعة فقط (لا يتم حفظها)",
                             font=("Arial", 9), fg="gray")
        note_label.pack(anchor="w", pady=(5, 0))

        # إحصائيات التقرير
        stats_frame = tk.LabelFrame(options_frame, text="إحصائيات التقرير", padx=5, pady=5)
        stats_frame.pack(fill=tk.X, pady=10)

        total_employees = len(self.promotion_list)
        eligible_count = len([p for p in self.promotion_list if p.get("مستحق للترقية") == "نعم"])

        stats_text = f"إجمالي الموظفين: {total_employees}\nالمستحقين للترقية: {eligible_count}\nغير المستحقين: {total_employees - eligible_count}"

        tk.Label(stats_frame, text=stats_text, font=("Arial", 10), justify=tk.LEFT).pack(anchor="w")

        # أزرار التحكم
        buttons_frame = tk.Frame(print_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        create_btn = tk.Button(buttons_frame, text="إنشاء وطباعة التقرير",
                              command=lambda: self.process_promotion_print(print_window),
                              bg="#FF9800", fg="white", font=("Arial", 11))
        create_btn.pack(side=tk.RIGHT, padx=5)

        cancel_btn = tk.Button(buttons_frame, text="إلغاء", command=print_window.destroy)
        cancel_btn.pack(side=tk.LEFT, padx=5)

    def process_promotion_print(self, window):
        """معالجة طباعة تقرير الترقيات"""
        try:
            from printing_system import PrintingSystem

            printer = PrintingSystem()
            print_format = self.promo_format_var.get()
            filter_type = self.promotion_filter_var.get()
            created_files = []

            # تصفية البيانات حسب الاختيار
            filtered_data = self.promotion_list.copy()

            if filter_type == "eligible":
                filtered_data = [p for p in self.promotion_list if p.get("مستحق للترقية") == "نعم"]
            elif filter_type == "not_eligible":
                filtered_data = [p for p in self.promotion_list if p.get("مستحق للترقية") == "لا"]

            if not filtered_data:
                messagebox.showwarning("تحذير", "لا توجد بيانات مطابقة للتصفية المحددة")
                return

            # إنشاء الملفات حسب التنسيق المحدد
            if print_format in ["pdf", "both"]:
                pdf_file = printer.create_promotions_report_pdf(filtered_data, "تقرير الترقيات المستحقة")
                if pdf_file:
                    created_files.append(("PDF", pdf_file))

            if print_format in ["word", "both"]:
                word_file = self.create_promotion_word_report(filtered_data)
                if word_file:
                    created_files.append(("Word", word_file))

            if not created_files:
                messagebox.showerror("خطأ", "فشل في إنشاء ملفات التقرير")
                return

            # معالجة الخيارات - عرض وطباعة فقط (بدون حفظ)
            for file_format, filepath in created_files:
                print(f"ℹ️ تم إنشاء تقرير الترقيات {file_format} للعرض والطباعة: {os.path.basename(filepath)}")

                # فتح الملف للعرض
                if self.promo_auto_open_var.get():
                    printer.open_file(filepath)

                # طباعة مباشرة إذا كان مطلوباً
                if self.promo_auto_print_var.get():
                    printer.print_file(filepath)

                # حذف الملف المؤقت بعد فترة قصيرة
                import threading
                def delayed_cleanup():
                    import time
                    time.sleep(5)  # انتظار أطول للسماح بالطباعة
                    try:
                        if os.path.exists(filepath):
                            os.remove(filepath)
                            print(f"🗑️ تم حذف ملف تقرير الترقيات المؤقت: {os.path.basename(filepath)}")
                    except:
                        pass

                threading.Thread(target=delayed_cleanup, daemon=True).start()

            # رسالة نجاح
            files_info = ", ".join([f[0] for f in created_files])
            messagebox.showinfo("نجاح", f"تم إنشاء تقرير الترقيات بنجاح: {files_info}")

            # إغلاق النافذة بأمان
            try:
                window.destroy()
            except:
                pass

        except ImportError:
            messagebox.showerror("خطأ", "نظام الطباعة غير متاح")
            try:
                window.destroy()
            except:
                pass
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء طباعة التقرير: {str(e)}")
            try:
                window.destroy()
            except:
                pass

    def create_promotion_word_report(self, promotion_data):
        """إنشاء تقرير ترقيات Word"""
        try:
            if not DOCX_AVAILABLE:
                messagebox.showerror("خطأ", "مكتبة python-docx غير مثبتة\nيرجى تثبيتها باستخدام: pip install python-docx")
                return None

            import tempfile
            from docx import Document
            from docx.shared import Inches
            from docx.enum.text import WD_ALIGN_PARAGRAPH
            from docx.oxml.ns import qn
            from docx.oxml import OxmlElement

            # إنشاء ملف مؤقت
            temp_dir = tempfile.mkdtemp()
            filename = f"promotions_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
            filepath = os.path.join(temp_dir, filename)

            # إنشاء مستند Word
            doc = Document()

            # إعداد اتجاه النص للعربية
            sections = doc.sections
            for section in sections:
                sectPr = section._sectPr
                bidi = OxmlElement('w:bidi')
                sectPr.append(bidi)

            # عنوان التقرير
            title = doc.add_heading('تقرير الترقيات المستحقة', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # تاريخ التقرير
            date_para = doc.add_paragraph(f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M")}')
            date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

            if promotion_data:
                # إضافة جدول
                table = doc.add_table(rows=1, cols=len(promotion_data[0]))
                table.style = 'Table Grid'

                # إضافة العناوين
                headers = list(promotion_data[0].keys())
                hdr_cells = table.rows[0].cells
                for i, header in enumerate(headers):
                    hdr_cells[i].text = header
                    # تنسيق العناوين
                    for paragraph in hdr_cells[i].paragraphs:
                        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
                        run.bold = True

                # إضافة البيانات
                for record in promotion_data:
                    row_cells = table.add_row().cells
                    for i, value in enumerate(record.values()):
                        row_cells[i].text = str(value) if value is not None else ""
                        # توسيط النص
                        for paragraph in row_cells[i].paragraphs:
                            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

                # تعديل عرض الأعمدة
                for row in table.rows:
                    for cell in row.cells:
                        cell.width = Inches(1.5)

            else:
                doc.add_paragraph('لا توجد بيانات ترقيات متاحة.')

            # إضافة فقرة ختامية
            doc.add_paragraph()
            footer = doc.add_paragraph('تم إنشاء هذا التقرير بواسطة نظام إدارة الموارد البشرية')
            footer.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # حفظ الملف
            doc.save(filepath)
            return filepath

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء تقرير Word: {str(e)}")
            return None

if __name__ == "__main__":
    root = tk.Tk()
    app = PromotionSystem(root)
    root.mainloop()