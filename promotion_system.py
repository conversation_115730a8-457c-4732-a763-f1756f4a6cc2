import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import os
from reference_data import get_bank_names, get_nationalities, get_qualifications, get_work_places

try:
    from openpyxl import load_workbook, Workbook
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    messagebox.showerror("خطأ", "مكتبة openpyxl غير مثبتة\nيرجى تثبيتها باستخدام: pip install openpyxl")

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    print("تحذير: مكتبة python-docx غير مثبتة - لن تتمكن من إنشاء تقارير Word")

class PromotionSystem:
    def __init__(self, root, employees_data_file="employees_data.xlsx"):
        self.root = root
        self.root.title("نظام الترقيات")
        self.root.geometry("1000x600")
        
        # ملف بيانات الموظفين
        self.employees_data_file = employees_data_file
        self.sheet_name = "الموظفين"
        
        # تحميل بيانات الموظفين
        self.employees_data = self.load_employees_data()
        
        # إنشاء واجهة المستخدم
        self.create_ui()
        
        # حساب الترقيات المستحقة
        self.calculate_promotions()
    
    def load_employees_data(self):
        """تحميل بيانات الموظفين من ملف Excel"""
        try:
            wb = load_workbook(self.employees_data_file)
            # محاولة العثور على الورقة بأسماء مختلفة
            if "الموظفين" in wb.sheetnames:
                ws = wb["الموظفين"]
            elif "Employees" in wb.sheetnames:
                ws = wb["Employees"]
            else:
                ws = wb.active
            
            data = []
            headers = [cell.value for cell in ws[1]]
            
            for row in ws.iter_rows(min_row=2, values_only=True):
                if any(row):
                    data.append(dict(zip(headers, row)))
            
            return data
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل بيانات الموظفين: {str(e)}")
            return []
    
    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        # إطار رئيسي
        main_frame = tk.Frame(self.root, padx=10, pady=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # إطار التصفية
        filter_frame = tk.LabelFrame(main_frame, text="تصفية الموظفين", padx=5, pady=5)
        filter_frame.pack(fill=tk.X, pady=5)
        
        tk.Label(filter_frame, text="التصفية حسب:").grid(row=0, column=0, padx=5, sticky="e")
        
        self.filter_by = ttk.Combobox(filter_frame, values=[
            "الدرجة الحالية", "المسمى الوظيفي", "مكان العمل", "مستحق للترقية", "الكل"
        ], state="readonly", width=15)
        self.filter_by.grid(row=0, column=1, padx=5, sticky="w")
        self.filter_by.current(4)  # تحديد "الكل" افتراضيًا
        
        self.filter_entry = tk.Entry(filter_frame, width=30)
        self.filter_entry.grid(row=0, column=2, padx=5, sticky="w")
        
        filter_btn = tk.Button(filter_frame, text="تصفية", command=self.filter_employees)
        filter_btn.grid(row=0, column=3, padx=5)
        
        reset_btn = tk.Button(filter_frame, text="إعادة تعيين", command=self.reset_filter)
        reset_btn.grid(row=0, column=4, padx=5)
        
        # إطار جدول الموظفين المستحقين للترقية
        table_frame = tk.Frame(main_frame)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # شريط التمرير
        scroll_y = tk.Scrollbar(table_frame, orient=tk.VERTICAL)
        scroll_x = tk.Scrollbar(table_frame, orient=tk.HORIZONTAL)
        
        # إنشاء الجدول
        self.promotions_table = ttk.Treeview(
            table_frame, 
            yscrollcommand=scroll_y.set, 
            xscrollcommand=scroll_x.set,
            selectmode="browse"
        )
        
        scroll_y.config(command=self.promotions_table.yview)
        scroll_x.config(command=self.promotions_table.xview)
        
        # تعريف الأعمدة
        self.promotions_table["columns"] = (
            "الاسم العربي", "الرقم الوظيفي", "الدرجة الحالية",
            "تاريخ الدرجة الحالية", "تاريخ استحقاق الترقية",
            "مستحق للترقية", "الدرجة الجديدة"
        )
        
        # تنسيق الأعمدة
        self.promotions_table.column("#0", width=0, stretch=tk.NO)
        self.promotions_table.column("الاسم العربي", width=150, anchor="center")
        self.promotions_table.column("الرقم الوظيفي", width=100, anchor="center")
        self.promotions_table.column("الدرجة الحالية", width=100, anchor="center")
        self.promotions_table.column("تاريخ الدرجة الحالية", width=120, anchor="center")
        self.promotions_table.column("تاريخ استحقاق الترقية", width=140, anchor="center")
        self.promotions_table.column("مستحق للترقية", width=100, anchor="center")
        self.promotions_table.column("الدرجة الجديدة", width=100, anchor="center")
        
        # عناوين الأعمدة
        self.promotions_table.heading("#0", text="", anchor="center")
        self.promotions_table.heading("الاسم العربي", text="الاسم العربي", anchor="center")
        self.promotions_table.heading("الرقم الوظيفي", text="الرقم الوظيفي", anchor="center")
        self.promotions_table.heading("الدرجة الحالية", text="الدرجة الحالية", anchor="center")
        self.promotions_table.heading("تاريخ الدرجة الحالية", text="تاريخ الدرجة الحالية", anchor="center")
        self.promotions_table.heading("تاريخ استحقاق الترقية", text="تاريخ استحقاق الترقية", anchor="center")
        self.promotions_table.heading("مستحق للترقية", text="مستحق للترقية", anchor="center")
        self.promotions_table.heading("الدرجة الجديدة", text="الدرجة الجديدة", anchor="center")
        
        # وضع الجدول وشريط التمرير في الواجهة
        self.promotions_table.grid(row=0, column=0, sticky="nsew")
        scroll_y.grid(row=0, column=1, sticky="ns")
        scroll_x.grid(row=1, column=0, sticky="ew")
        
        # جعل الجدول قابل للتوسع
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=5)
        
        calculate_btn = tk.Button(buttons_frame, text="تحديث الحساب", command=self.calculate_promotions)
        calculate_btn.pack(side=tk.LEFT, padx=5)

        print_btn = tk.Button(buttons_frame, text="طباعة التقرير", command=self.print_report)
        print_btn.pack(side=tk.LEFT, padx=5)
    
    def calculate_promotions(self):
        """حساب الترقيات المستحقة للموظفين"""
        self.promotion_list = []
        
        for emp in self.employees_data:
            current_grade = emp.get("الدرجة الحالية", "")
            grade_date_str = emp.get("تاريخ الدرجة الحالية", "")
            
            if not current_grade or not grade_date_str:
                continue
                
            try:
                # تحويل تاريخ الدرجة الحالية إلى كائن تاريخ
                grade_date = datetime.strptime(grade_date_str, "%Y-%m-%d")
                
                # حساب المدة في الدرجة الحالية (بالسنوات)
                today = datetime.now()
                duration = today - grade_date
                years_in_grade = duration.days / 365.25

                # حساب تاريخ استحقاق الترقية (4 سنوات من تاريخ الدرجة الحالية)
                try:
                    from dateutil.relativedelta import relativedelta
                    promotion_due_date = grade_date + relativedelta(years=4)
                except ImportError:
                    # إذا لم تكن مكتبة dateutil متاحة، استخدم حساب بسيط
                    promotion_due_date = grade_date.replace(year=grade_date.year + 4)

                promotion_due_str = promotion_due_date.strftime("%Y-%m-%d")

                # تحديد إذا كان الموظف مستحق للترقية
                eligible = False
                new_grade = ""

                try:
                    current_grade_num = int(current_grade)

                    # حساب الترقية حسب القواعد المحددة
                    if current_grade_num < 9 and years_in_grade >= 4:
                        eligible = True
                        new_grade = str(current_grade_num + 1)
                    elif current_grade_num >= 9 and years_in_grade >= 4:
                        eligible = True
                        new_grade = str(current_grade_num + 1)
                except ValueError:
                    pass
                
                # إضافة الموظف إلى قائمة الترقيات
                self.promotion_list.append({
                    "الاسم العربي": emp.get("الاسم العربي", ""),
                    "الرقم الوظيفي": emp.get("الرقم الوظيفي", ""),
                    "الدرجة الحالية": current_grade,
                    "تاريخ الدرجة الحالية": grade_date_str,
                    "تاريخ استحقاق الترقية": promotion_due_str,
                    "مستحق للترقية": "نعم" if eligible else "لا",
                    "الدرجة الجديدة": new_grade if eligible else ""
                })
                
            except ValueError:
                continue
        
        # تحديث الجدول بالنتائج
        self.update_promotions_table()
    
    def update_promotions_table(self, data=None):
        """تحديث جدول الترقيات بالبيانات"""
        # مسح البيانات الحالية
        for item in self.promotions_table.get_children():
            self.promotions_table.delete(item)
        
        # استخدام البيانات المقدمة أو البيانات الكاملة
        display_data = data if data else self.promotion_list
        
        # إضافة البيانات إلى الجدول
        for promo in display_data:
            self.promotions_table.insert("", tk.END, values=(
                promo.get("الاسم العربي", ""),
                promo.get("الرقم الوظيفي", ""),
                promo.get("الدرجة الحالية", ""),
                promo.get("تاريخ الدرجة الحالية", ""),
                promo.get("تاريخ استحقاق الترقية", ""),
                promo.get("مستحق للترقية", ""),
                promo.get("الدرجة الجديدة", "")
            ))
    
    def filter_employees(self):
        """تصفية الموظفين حسب المعايير المحددة"""
        filter_criteria = self.filter_by.get()
        filter_term = self.filter_entry.get().strip().lower()
        
        if filter_criteria == "الكل":
            self.update_promotions_table()
            return

        # خيار "مستحق للترقية" لا يحتاج مصطلح بحث
        if filter_criteria == "مستحق للترقية":
            filtered_data = [promo for promo in self.promotion_list if promo.get("مستحق للترقية") == "نعم"]
            if not filtered_data:
                messagebox.showinfo("نتائج التصفية", "لا توجد ترقيات مستحقة")
            else:
                self.update_promotions_table(filtered_data)
            return

        if not filter_term:
            messagebox.showwarning("تحذير", "الرجاء إدخال مصطلح التصفية")
            return

        filtered_data = []
        for promo in self.promotion_list:
            if filter_criteria == "الدرجة الحالية":
                if filter_term in str(promo.get("الدرجة الحالية", "")).lower():
                    filtered_data.append(promo)
            elif filter_criteria == "المسمى الوظيفي":
                # نحتاج إلى البحث في بيانات الموظفين الأصلية
                emp_id = promo.get("الرقم الوظيفي", "")
                for emp in self.employees_data:
                    if emp.get("الرقم الوظيفي", "") == emp_id:
                        if filter_term in emp.get("المسمى الوظيفي", "").lower():
                            filtered_data.append(promo)
                        break
            elif filter_criteria == "مكان العمل":
                # نحتاج إلى البحث في بيانات الموظفين الأصلية
                emp_id = promo.get("الرقم الوظيفي", "")
                for emp in self.employees_data:
                    if emp.get("الرقم الوظيفي", "") == emp_id:
                        if filter_term in emp.get("مكان العمل الحالي", "").lower():
                            filtered_data.append(promo)
                        break
        
        if not filtered_data:
            messagebox.showinfo("نتائج التصفية", "لا توجد نتائج مطابقة للتصفية")
        else:
            self.update_promotions_table(filtered_data)
    
    def reset_filter(self):
        """إعادة تعيين التصفية وعرض جميع الموظفين"""
        self.filter_entry.delete(0, tk.END)
        self.filter_by.current(4)  # تحديد "الكل" (الآن في الموضع 4)
        self.update_promotions_table()
    

    def print_report(self):
        """طباعة تقرير الترقيات"""
        if not hasattr(self, 'promotion_list') or not self.promotion_list:
            messagebox.showwarning("تحذير", "لا توجد بيانات ترقيات للطباعة")
            return

        # إنشاء نافذة خيارات الطباعة
        self.show_promotion_print_options()

    def show_promotion_print_options(self):
        """عرض خيارات طباعة تقرير الترقيات"""
        print_window = tk.Toplevel(self.root)
        print_window.title("طباعة تقرير الترقيات")
        print_window.geometry("550x450")
        print_window.grab_set()

        # إطار الخيارات
        options_frame = tk.LabelFrame(print_window, text="خيارات طباعة تقرير الترقيات", padx=10, pady=10)
        options_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # نوع التقرير
        tk.Label(options_frame, text="تقرير الترقيات المستحقة",
                font=("Arial", 12, "bold")).pack(anchor="w", pady=5)

        # تصفية التقرير
        tk.Label(options_frame, text="تصفية التقرير:", font=("Arial", 12)).pack(anchor="w", pady=5)

        self.promotion_filter_var = tk.StringVar(master=print_window, value="all")

        filters = [
            ("جميع الموظفين", "all"),
            ("المستحقين للترقية فقط", "eligible"),
            ("غير المستحقين فقط", "not_eligible")
        ]

        for text, value in filters:
            tk.Radiobutton(options_frame, text=text, variable=self.promotion_filter_var, value=value).pack(anchor="w")

        # نوع الملف
        tk.Label(options_frame, text="تنسيق الطباعة:", font=("Arial", 12)).pack(anchor="w", pady=(20, 5))

        self.promo_format_var = tk.StringVar(master=print_window, value="pdf")

        formats = [
            ("PDF", "pdf", "ملف PDF للطباعة"),
            ("Word", "word", "مستند Word للطباعة"),
            ("كلاهما", "both", "إنشاء كلا التنسيقين")
        ]

        for text, value, desc in formats:
            frame = tk.Frame(options_frame)
            frame.pack(fill=tk.X, pady=2)

            rb = tk.Radiobutton(frame, text=text, variable=self.promo_format_var, value=value)
            rb.pack(side=tk.LEFT)

            tk.Label(frame, text=f"- {desc}", font=("Arial", 9), fg="gray").pack(side=tk.LEFT, padx=(10, 0))

        # خيارات إضافية
        tk.Label(options_frame, text="خيارات إضافية:", font=("Arial", 12)).pack(anchor="w", pady=(20, 5))

        self.promo_auto_open_var = tk.BooleanVar(master=print_window, value=True)
        tk.Checkbutton(options_frame, text="فتح الملف بعد الإنشاء",
                      variable=self.promo_auto_open_var).pack(anchor="w")

        self.promo_auto_print_var = tk.BooleanVar(master=print_window, value=False)
        tk.Checkbutton(options_frame, text="طباعة مباشرة",
                      variable=self.promo_auto_print_var).pack(anchor="w")

        # ملاحظة للمستخدم
        note_label = tk.Label(options_frame, text="ملاحظة: التقارير للعرض والطباعة فقط (لا يتم حفظها)",
                             font=("Arial", 9), fg="gray")
        note_label.pack(anchor="w", pady=(5, 0))

        # إحصائيات التقرير
        stats_frame = tk.LabelFrame(options_frame, text="إحصائيات التقرير", padx=5, pady=5)
        stats_frame.pack(fill=tk.X, pady=10)

        total_employees = len(self.promotion_list)
        eligible_count = len([p for p in self.promotion_list if p.get("مستحق للترقية") == "نعم"])

        stats_text = f"إجمالي الموظفين: {total_employees}\nالمستحقين للترقية: {eligible_count}\nغير المستحقين: {total_employees - eligible_count}"

        tk.Label(stats_frame, text=stats_text, font=("Arial", 10), justify=tk.LEFT).pack(anchor="w")

        # أزرار التحكم
        buttons_frame = tk.Frame(print_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        create_btn = tk.Button(buttons_frame, text="إنشاء وطباعة التقرير",
                              command=lambda: self.process_promotion_print(print_window),
                              bg="#FF9800", fg="white", font=("Arial", 11))
        create_btn.pack(side=tk.RIGHT, padx=5)

        cancel_btn = tk.Button(buttons_frame, text="إلغاء", command=print_window.destroy)
        cancel_btn.pack(side=tk.LEFT, padx=5)

    def process_promotion_print(self, window):
        """معالجة طباعة تقرير الترقيات"""
        try:
            from printing_system import PrintingSystem

            printer = PrintingSystem()
            print_format = self.promo_format_var.get()
            filter_type = self.promotion_filter_var.get()
            created_files = []

            # تصفية البيانات حسب الاختيار
            filtered_data = self.promotion_list.copy()

            if filter_type == "eligible":
                filtered_data = [p for p in self.promotion_list if p.get("مستحق للترقية") == "نعم"]
            elif filter_type == "not_eligible":
                filtered_data = [p for p in self.promotion_list if p.get("مستحق للترقية") == "لا"]

            if not filtered_data:
                messagebox.showwarning("تحذير", "لا توجد بيانات مطابقة للتصفية المحددة")
                return

            # إنشاء الملفات حسب التنسيق المحدد
            if print_format in ["pdf", "both"]:
                pdf_file = printer.create_promotions_report_pdf(filtered_data, "تقرير الترقيات المستحقة")
                if pdf_file:
                    created_files.append(("PDF", pdf_file))

            if print_format in ["word", "both"]:
                word_file = self.create_promotion_word_report(filtered_data)
                if word_file:
                    created_files.append(("Word", word_file))

            if not created_files:
                messagebox.showerror("خطأ", "فشل في إنشاء ملفات التقرير")
                return

            # معالجة الخيارات - عرض وطباعة فقط (بدون حفظ)
            for file_format, filepath in created_files:
                print(f"ℹ️ تم إنشاء تقرير الترقيات {file_format} للعرض والطباعة: {os.path.basename(filepath)}")

                # فتح الملف للعرض
                if self.promo_auto_open_var.get():
                    printer.open_file(filepath)

                # طباعة مباشرة إذا كان مطلوباً
                if self.promo_auto_print_var.get():
                    printer.print_file(filepath)

                # حذف الملف المؤقت بعد فترة قصيرة
                import threading
                def delayed_cleanup():
                    import time
                    time.sleep(5)  # انتظار أطول للسماح بالطباعة
                    try:
                        if os.path.exists(filepath):
                            os.remove(filepath)
                            print(f"🗑️ تم حذف ملف تقرير الترقيات المؤقت: {os.path.basename(filepath)}")
                    except:
                        pass

                threading.Thread(target=delayed_cleanup, daemon=True).start()

            # رسالة نجاح
            files_info = ", ".join([f[0] for f in created_files])
            messagebox.showinfo("نجاح", f"تم إنشاء تقرير الترقيات بنجاح: {files_info}")

            # إغلاق النافذة بأمان
            try:
                window.destroy()
            except:
                pass

        except ImportError:
            messagebox.showerror("خطأ", "نظام الطباعة غير متاح")
            try:
                window.destroy()
            except:
                pass
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء طباعة التقرير: {str(e)}")
            try:
                window.destroy()
            except:
                pass

    def create_promotion_word_report(self, promotion_data):
        """إنشاء تقرير ترقيات Word"""
        try:
            if not DOCX_AVAILABLE:
                messagebox.showerror("خطأ", "مكتبة python-docx غير مثبتة\nيرجى تثبيتها باستخدام: pip install python-docx")
                return None

            import tempfile
            from docx import Document
            from docx.shared import Inches
            from docx.enum.text import WD_ALIGN_PARAGRAPH
            from docx.oxml.ns import qn
            from docx.oxml import OxmlElement

            # إنشاء ملف مؤقت
            temp_dir = tempfile.mkdtemp()
            filename = f"promotions_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
            filepath = os.path.join(temp_dir, filename)

            # إنشاء مستند Word
            doc = Document()

            # إعداد اتجاه النص للعربية
            sections = doc.sections
            for section in sections:
                sectPr = section._sectPr
                bidi = OxmlElement('w:bidi')
                sectPr.append(bidi)

            # عنوان التقرير
            title = doc.add_heading('تقرير الترقيات المستحقة', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # تاريخ التقرير
            date_para = doc.add_paragraph(f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M")}')
            date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

            if promotion_data:
                # إضافة جدول
                table = doc.add_table(rows=1, cols=len(promotion_data[0]))
                table.style = 'Table Grid'

                # إضافة العناوين
                headers = list(promotion_data[0].keys())
                hdr_cells = table.rows[0].cells
                for i, header in enumerate(headers):
                    hdr_cells[i].text = header
                    # تنسيق العناوين
                    for paragraph in hdr_cells[i].paragraphs:
                        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
                        run.bold = True

                # إضافة البيانات
                for record in promotion_data:
                    row_cells = table.add_row().cells
                    for i, value in enumerate(record.values()):
                        row_cells[i].text = str(value) if value is not None else ""
                        # توسيط النص
                        for paragraph in row_cells[i].paragraphs:
                            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

                # تعديل عرض الأعمدة
                for row in table.rows:
                    for cell in row.cells:
                        cell.width = Inches(1.5)

            else:
                doc.add_paragraph('لا توجد بيانات ترقيات متاحة.')

            # إضافة فقرة ختامية
            doc.add_paragraph()
            footer = doc.add_paragraph('تم إنشاء هذا التقرير بواسطة نظام إدارة الموارد البشرية')
            footer.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # حفظ الملف
            doc.save(filepath)
            return filepath

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء تقرير Word: {str(e)}")
            return None

if __name__ == "__main__":
    root = tk.Tk()
    app = PromotionSystem(root)
    root.mainloop()