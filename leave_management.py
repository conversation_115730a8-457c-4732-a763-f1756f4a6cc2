import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
from reference_data import get_bank_names, get_nationalities, get_qualifications, get_work_places
import os
import sys

# استيراد نظام رصيد الإجازات
try:
    from leave_balance_system import LeaveBalanceSystem
    BALANCE_SYSTEM_AVAILABLE = True
except ImportError:
    BALANCE_SYSTEM_AVAILABLE = False
    print("تحذير: نظام رصيد الإجازات غير متاح")

def get_resource_path(relative_path):
    """الحصول على المسار الصحيح للملفات"""
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

def is_running_as_executable():
    """فحص ما إذا كان التطبيق يعمل كملف تنفيذي"""
    return getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')

try:
    from tkcalendar import DateEntry
    TKCALENDAR_AVAILABLE = True
except ImportError:
    TKCALENDAR_AVAILABLE = False
    print("تحذير: مكتبة tkcalendar غير مثبتة - سيتم استخدام حقول نص للتواريخ")

try:
    from openpyxl import load_workbook, Workbook
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    messagebox.showerror("خطأ", "مكتبة openpyxl غير مثبتة\nيرجى تثبيتها باستخدام: pip install openpyxl")

class LeaveManagementSystem:
    def __init__(self, root, employees_data_file=None):
        self.root = root
        self.root.title("نظام إدارة الإجازات")
        self.root.geometry("1000x600")

        # ملفات البيانات
        if employees_data_file is None:
            employees_data_file = get_resource_path("employees_data.xlsx") if is_running_as_executable() else "employees_data.xlsx"
        self.employees_data_file = employees_data_file
        self.leaves_data_file = get_resource_path("leaves_data.xlsx") if is_running_as_executable() else "leaves_data.xlsx"
        self.leaves_sheet_name = "الإجازات"
        
        # أنواع الإجازات
        self.leave_types = [
            "إجازة سنوية", "إجازة مرضية", "إجازة عارضة", 
            "إجازة أمومة", "إجازة بدون أجر", "إجازة رسمية"
        ]
        
        # إنشاء ملف الإجازات إذا لم يكن موجودًا
        self.initialize_leaves_file()
        
        # تحميل بيانات الموظفين والإجازات
        self.employees = self.load_employees()
        self.leaves_data = self.load_leaves_data()
        
        # إنشاء واجهة المستخدم
        self.create_ui()
    
    def initialize_leaves_file(self):
        """إنشاء ملف Excel للإجازات إذا لم يكن موجودًا"""
        if not os.path.exists(self.leaves_data_file):
            wb = Workbook()
            ws = wb.active
            ws.title = self.leaves_sheet_name
            
            # عناوين الأعمدة
            headers = [
                "الرقم الوظيفي", "اسم الموظف", "نوع الإجازة", 
                "تاريخ البدء", "تاريخ الانتهاء", "عدد الأيام", 
                "الحالة", "ملاحظات"
            ]
            
            ws.append(headers)
            wb.save(self.leaves_data_file)
    
    def load_employees(self):
        """تحميل بيانات الموظفين من ملف الموظفين"""
        try:
            wb = load_workbook(self.employees_data_file)
            # محاولة العثور على الورقة بأسماء مختلفة
            if "الموظفين" in wb.sheetnames:
                ws = wb["الموظفين"]
            elif "Employees" in wb.sheetnames:
                ws = wb["Employees"]
            else:
                ws = wb.active
            
            employees = []
            headers = [cell.value for cell in ws[1]]
            
            for row in ws.iter_rows(min_row=2, values_only=True):
                if any(row):
                    emp = dict(zip(headers, row))
                    job_title = emp.get("المسمى الوظيفي", "")

                    # فلترة الموظفين فقط (موظف/موظفة) وليس المعلمين
                    if job_title in ["موظف", "موظفة"]:
                        employees.append({
                            "الرقم الوظيفي": emp.get("الرقم الوظيفي", ""),
                            "الاسم العربي": emp.get("الاسم العربي", ""),
                            "المسمى الوظيفي": job_title
                        })
            
            return employees
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل بيانات الموظفين: {str(e)}")
            return []
    
    def load_leaves_data(self):
        """تحميل بيانات الإجازات من ملف الإجازات"""
        try:
            wb = load_workbook(self.leaves_data_file)
            ws = wb[self.leaves_sheet_name]
            
            data = []
            headers = [cell.value for cell in ws[1]]
            
            for row in ws.iter_rows(min_row=2, values_only=True):
                if any(row):
                    data.append(dict(zip(headers, row)))
            
            return data
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل بيانات الإجازات: {str(e)}")
            return []
    
    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        # إطار رئيسي
        main_frame = tk.Frame(self.root, padx=10, pady=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # إطار إضافة إجازة
        add_leave_frame = tk.LabelFrame(main_frame, text="إضافة إجازة جديدة", padx=5, pady=5)
        add_leave_frame.pack(fill=tk.X, pady=5)
        
        # حقول إضافة إجازة
        tk.Label(add_leave_frame, text="الموظف:").grid(row=0, column=0, padx=5, sticky="e")

        self.employee_var = tk.StringVar(master=self.root)
        self.employee_combo = ttk.Combobox(
            add_leave_frame,
            textvariable=self.employee_var,
            values=[f"{emp['الرقم الوظيفي']} - {emp['الاسم العربي']}" for emp in self.employees],
            state="readonly",
            width=40
        )
        self.employee_combo.grid(row=0, column=1, padx=5, sticky="w")

        tk.Label(add_leave_frame, text="نوع الإجازة:").grid(row=0, column=2, padx=5, sticky="e")

        self.leave_type_var = tk.StringVar(master=self.root)
        self.leave_type_combo = ttk.Combobox(
            add_leave_frame,
            textvariable=self.leave_type_var,
            values=self.leave_types,
            state="readonly",
            width=20
        )
        self.leave_type_combo.grid(row=0, column=3, padx=5, sticky="w")
        self.leave_type_combo.current(0)
        
        tk.Label(add_leave_frame, text="تاريخ البدء:").grid(row=1, column=0, padx=5, sticky="e")

        if TKCALENDAR_AVAILABLE:
            self.start_date_entry = DateEntry(
                add_leave_frame,
                date_pattern="yyyy-mm-dd",
                width=12,
                background="darkblue",
                foreground="white",
                borderwidth=2
            )
        else:
            self.start_date_entry = tk.Entry(add_leave_frame, width=15)
            self.start_date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))

        self.start_date_entry.grid(row=1, column=1, padx=5, sticky="w")

        tk.Label(add_leave_frame, text="تاريخ الانتهاء:").grid(row=1, column=2, padx=5, sticky="e")

        if TKCALENDAR_AVAILABLE:
            self.end_date_entry = DateEntry(
                add_leave_frame,
                date_pattern="yyyy-mm-dd",
                width=12,
                background="darkblue",
                foreground="white",
                borderwidth=2
            )
        else:
            self.end_date_entry = tk.Entry(add_leave_frame, width=15)
            tomorrow = datetime.now() + timedelta(days=1)
            self.end_date_entry.insert(0, tomorrow.strftime("%Y-%m-%d"))

        self.end_date_entry.grid(row=1, column=3, padx=5, sticky="w")
        
        tk.Label(add_leave_frame, text="ملاحظات:").grid(row=2, column=0, padx=5, sticky="e")
        self.notes_entry = tk.Entry(add_leave_frame, width=50)
        self.notes_entry.grid(row=2, column=1, columnspan=3, padx=5, sticky="w")
        
        add_btn = tk.Button(add_leave_frame, text="إضافة إجازة", command=self.add_leave)
        add_btn.grid(row=3, column=3, padx=5, pady=5, sticky="e")
        
        # إطار البحث والتصفية
        filter_frame = tk.LabelFrame(main_frame, text="بحث وتصفية الإجازات", padx=5, pady=5)
        filter_frame.pack(fill=tk.X, pady=5)
        
        tk.Label(filter_frame, text="بحث حسب:").grid(row=0, column=0, padx=5, sticky="e")
        
        self.filter_by = ttk.Combobox(filter_frame, values=[
            "اسم الموظف", "الرقم الوظيفي", "نوع الإجازة", "الحالة"
        ], state="readonly", width=15)
        self.filter_by.grid(row=0, column=1, padx=5, sticky="w")
        self.filter_by.current(0)
        
        self.filter_entry = tk.Entry(filter_frame, width=30)
        self.filter_entry.grid(row=0, column=2, padx=5, sticky="w")
        
        search_btn = tk.Button(filter_frame, text="بحث", command=self.filter_leaves)
        search_btn.grid(row=0, column=3, padx=5)
        
        reset_btn = tk.Button(filter_frame, text="إعادة تعيين", command=self.reset_filter)
        reset_btn.grid(row=0, column=4, padx=5)
        
        # إطار جدول الإجازات
        table_frame = tk.Frame(main_frame)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # شريط التمرير
        scroll_y = tk.Scrollbar(table_frame, orient=tk.VERTICAL)
        scroll_x = tk.Scrollbar(table_frame, orient=tk.HORIZONTAL)
        
        # إنشاء الجدول
        self.leaves_table = ttk.Treeview(
            table_frame, 
            yscrollcommand=scroll_y.set, 
            xscrollcommand=scroll_x.set,
            selectmode="browse"
        )
        
        scroll_y.config(command=self.leaves_table.yview)
        scroll_x.config(command=self.leaves_table.xview)
        
        # تعريف الأعمدة
        self.leaves_table["columns"] = (
            "الرقم الوظيفي", "اسم الموظف", "نوع الإجازة", 
            "تاريخ البدء", "تاريخ الانتهاء", "عدد الأيام", "الحالة"
        )
        
        # تنسيق الأعمدة
        self.leaves_table.column("#0", width=0, stretch=tk.NO)
        self.leaves_table.column("الرقم الوظيفي", width=100, anchor="center")
        self.leaves_table.column("اسم الموظف", width=150, anchor="center")
        self.leaves_table.column("نوع الإجازة", width=120, anchor="center")
        self.leaves_table.column("تاريخ البدء", width=100, anchor="center")
        self.leaves_table.column("تاريخ الانتهاء", width=100, anchor="center")
        self.leaves_table.column("عدد الأيام", width=80, anchor="center")
        self.leaves_table.column("الحالة", width=80, anchor="center")
        
        # عناوين الأعمدة
        self.leaves_table.heading("#0", text="", anchor="center")
        self.leaves_table.heading("الرقم الوظيفي", text="الرقم الوظيفي", anchor="center")
        self.leaves_table.heading("اسم الموظف", text="اسم الموظف", anchor="center")
        self.leaves_table.heading("نوع الإجازة", text="نوع الإجازة", anchor="center")
        self.leaves_table.heading("تاريخ البدء", text="تاريخ البدء", anchor="center")
        self.leaves_table.heading("تاريخ الانتهاء", text="تاريخ الانتهاء", anchor="center")
        self.leaves_table.heading("عدد الأيام", text="عدد الأيام", anchor="center")
        self.leaves_table.heading("الحالة", text="الحالة", anchor="center")
        
        # وضع الجدول وشريط التمرير في الواجهة
        self.leaves_table.grid(row=0, column=0, sticky="nsew")
        scroll_y.grid(row=0, column=1, sticky="ns")
        scroll_x.grid(row=1, column=0, sticky="ew")
        
        # جعل الجدول قابل للتوسع
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=5)
        
        delete_btn = tk.Button(buttons_frame, text="حذف الإجازة", command=self.delete_leave)
        delete_btn.pack(side=tk.LEFT, padx=5)
        
        approve_btn = tk.Button(buttons_frame, text="موافقة", command=lambda: self.change_leave_status("موافق"))
        approve_btn.pack(side=tk.LEFT, padx=5)
        
        reject_btn = tk.Button(buttons_frame, text="رفض", command=lambda: self.change_leave_status("مرفوض"))
        reject_btn.pack(side=tk.LEFT, padx=5)
        
        export_btn = tk.Button(buttons_frame, text="تصدير إلى Excel", command=self.export_leaves)
        export_btn.pack(side=tk.LEFT, padx=5)

        # زر عرض أرصدة الإجازات (إذا كان النظام متاح)
        if BALANCE_SYSTEM_AVAILABLE:
            balance_btn = tk.Button(buttons_frame, text="🏖️ أرصدة الإجازات",
                                   command=self.open_balance_system,
                                   bg="#9b59b6", fg="white")
            balance_btn.pack(side=tk.LEFT, padx=5)

        # زر الإغلاق
        close_btn = tk.Button(buttons_frame, text="❌ إغلاق",
                             command=self.close_window,
                             font=("Arial", 10, "bold"), bg="#e74c3c", fg="white",
                             relief=tk.RAISED, bd=2, padx=15, pady=5)
        close_btn.pack(side=tk.RIGHT, padx=5)

        # تحديث الجدول بالبيانات
        self.update_leaves_table()
    
    def update_leaves_table(self, data=None):
        """تحديث جدول الإجازات بالبيانات"""
        # مسح البيانات الحالية
        for item in self.leaves_table.get_children():
            self.leaves_table.delete(item)
        
        # استخدام البيانات المقدمة أو البيانات الكاملة
        display_data = data if data else self.leaves_data
        
        # إضافة البيانات إلى الجدول
        for leave in display_data:
            self.leaves_table.insert("", tk.END, values=(
                leave.get("الرقم الوظيفي", ""),
                leave.get("اسم الموظف", ""),
                leave.get("نوع الإجازة", ""),
                leave.get("تاريخ البدء", ""),
                leave.get("تاريخ الانتهاء", ""),
                leave.get("عدد الأيام", ""),
                leave.get("الحالة", "قيد الانتظار")
            ))
    
    def add_leave(self):
        """إضافة إجازة جديدة مع فحص الرصيد"""
        # التحقق من اختيار موظف
        if not self.employee_var.get():
            messagebox.showwarning("تحذير", "الرجاء اختيار موظف")
            return

        # التحقق من تواريخ الإجازة
        try:
            if TKCALENDAR_AVAILABLE:
                start_date = self.start_date_entry.get_date()
                end_date = self.end_date_entry.get_date()
            else:
                start_date_str = self.start_date_entry.get()
                end_date_str = self.end_date_entry.get()
                start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
                end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
        except ValueError:
            messagebox.showerror("خطأ", "تنسيق التاريخ غير صحيح. استخدم التنسيق: YYYY-MM-DD")
            return

        if end_date < start_date:
            messagebox.showwarning("تحذير", "تاريخ الانتهاء يجب أن يكون بعد تاريخ البدء")
            return

        # حساب عدد أيام الإجازة
        delta = end_date - start_date
        days = delta.days + 1  # تضمين يوم البدء

        # جمع بيانات الإجازة
        emp_id, emp_name = self.employee_var.get().split(" - ", 1)

        # فحص الرصيد إذا كان نظام الرصيد متاح
        if BALANCE_SYSTEM_AVAILABLE:
            balance_check = self.check_leave_balance(emp_id, days)
            if not balance_check["success"]:
                # عرض رسالة تحذير مع خيار المتابعة
                response = messagebox.askyesnocancel(
                    "تحذير الرصيد",
                    f"{balance_check['message']}\n\n"
                    f"هل تريد المتابعة رغم ذلك؟\n"
                    f"نعم: متابعة الإضافة\n"
                    f"لا: عرض تفاصيل الرصيد\n"
                    f"إلغاء: إلغاء العملية"
                )

                if response is None:  # إلغاء
                    return
                elif response is False:  # عرض تفاصيل الرصيد
                    self.show_employee_balance_details(emp_id, emp_name)
                    return
                # إذا كان True، سيتم المتابعة

        leave_data = {
            "الرقم الوظيفي": emp_id,
            "اسم الموظف": emp_name,
            "نوع الإجازة": self.leave_type_var.get(),
            "تاريخ البدء": start_date.strftime("%Y-%m-%d"),
            "تاريخ الانتهاء": end_date.strftime("%Y-%m-%d"),
            "عدد الأيام": days,
            "الحالة": "قيد الانتظار",
            "ملاحظات": self.notes_entry.get()
        }

        # إضافة الإجازة إلى البيانات
        self.leaves_data.append(leave_data)

        # حفظ البيانات في ملف Excel
        self.save_leaves_data()

        # تحديث الجدول
        self.update_leaves_table()

        # إعادة تعيين الحقول
        self.notes_entry.delete(0, tk.END)

        messagebox.showinfo("نجاح", "تم إضافة الإجازة بنجاح")

    def check_leave_balance(self, emp_id, requested_days):
        """فحص رصيد الإجازة للموظف"""
        try:
            # إنشاء نسخة مؤقتة من نظام الرصيد للفحص
            temp_balance_system = LeaveBalanceSystem(tk.Tk())
            temp_balance_system.root.withdraw()  # إخفاء النافذة

            # حساب الرصيد الحالي
            emp_data = None
            for emp in temp_balance_system.employees_data:
                if str(emp.get("الرقم الوظيفي", "")) == str(emp_id):
                    emp_data = emp
                    break

            if not emp_data:
                return {"success": False, "message": "بيانات الموظف غير موجودة في نظام الرصيد"}

            service_years = temp_balance_system.calculate_service_years(emp_data.get("تاريخ أول مباشرة", ""))
            job_title = emp_data.get("المسمى الوظيفي", "") or emp_data.get("الوظيفة", "") or emp_data.get("المنصب", "")
            auto_balance = temp_balance_system.calculate_automatic_balance(service_years, job_title)
            manual_balance = temp_balance_system.get_manual_balance(emp_id)
            total_balance = auto_balance + manual_balance
            used_leaves = temp_balance_system.calculate_used_leaves(emp_id)
            remaining_balance = total_balance - used_leaves

            # إغلاق النافذة المؤقتة
            temp_balance_system.root.destroy()

            if remaining_balance >= requested_days:
                return {
                    "success": True,
                    "message": f"الرصيد كافي. المتبقي: {remaining_balance} يوم",
                    "remaining": remaining_balance,
                    "total": total_balance,
                    "used": used_leaves
                }
            else:
                return {
                    "success": False,
                    "message": f"الرصيد غير كافي!\nالمطلوب: {requested_days} يوم\nالمتبقي: {remaining_balance} يوم\nالنقص: {requested_days - remaining_balance} يوم",
                    "remaining": remaining_balance,
                    "total": total_balance,
                    "used": used_leaves,
                    "shortage": requested_days - remaining_balance
                }

        except Exception as e:
            return {"success": False, "message": f"خطأ في فحص الرصيد: {str(e)}"}

    def show_employee_balance_details(self, emp_id, emp_name):
        """عرض تفاصيل رصيد الموظف"""
        try:
            # إنشاء نافذة تفاصيل الرصيد بحجم أكبر
            balance_window = tk.Toplevel(self.root)
            balance_window.title(f"تفاصيل رصيد الإجازة - {emp_name}")
            balance_window.geometry("700x550")
            balance_window.configure(bg="#f0f0f0")
            balance_window.resizable(True, True)
            balance_window.minsize(600, 450)

            # جعل النافذة في المقدمة
            balance_window.transient(self.root)
            balance_window.grab_set()

            # إطار رئيسي
            main_frame = tk.Frame(balance_window, bg="#f0f0f0", padx=20, pady=20)
            main_frame.pack(fill=tk.BOTH, expand=True)

            # عنوان النافذة
            title_label = tk.Label(main_frame, text=f"🏖️ تفاصيل رصيد الإجازة",
                                  font=("Arial", 16, "bold"), fg="#2c3e50", bg="#f0f0f0")
            title_label.pack(pady=(0, 20))

            # معلومات الموظف
            emp_frame = tk.LabelFrame(main_frame, text="معلومات الموظف",
                                    font=("Arial", 12, "bold"), fg="#2c3e50",
                                    padx=15, pady=15, bg="#ffffff")
            emp_frame.pack(fill=tk.X, pady=(0, 15))

            tk.Label(emp_frame, text=f"الرقم الوظيفي: {emp_id}",
                    font=("Arial", 11), bg="#ffffff").pack(anchor="w", pady=2)
            tk.Label(emp_frame, text=f"اسم الموظف: {emp_name}",
                    font=("Arial", 11), bg="#ffffff").pack(anchor="w", pady=2)

            # حساب تفاصيل الرصيد
            if BALANCE_SYSTEM_AVAILABLE:
                temp_balance_system = LeaveBalanceSystem(tk.Tk())
                temp_balance_system.root.withdraw()

                emp_data = None
                for emp in temp_balance_system.employees_data:
                    if str(emp.get("الرقم الوظيفي", "")) == str(emp_id):
                        emp_data = emp
                        break

                if emp_data:
                    service_years = temp_balance_system.calculate_service_years(emp_data.get("تاريخ أول مباشرة", ""))
                    job_title = emp_data.get("المسمى الوظيفي", "") or emp_data.get("الوظيفة", "") or emp_data.get("المنصب", "")
                    auto_balance = temp_balance_system.calculate_automatic_balance(service_years, job_title)
                    manual_balance = temp_balance_system.get_manual_balance(emp_id)
                    total_balance = auto_balance + manual_balance
                    used_leaves = temp_balance_system.calculate_used_leaves(emp_id)
                    remaining_balance = total_balance - used_leaves

                    # إطار تفاصيل الرصيد
                    balance_frame = tk.LabelFrame(main_frame, text="تفاصيل الرصيد",
                                                font=("Arial", 12, "bold"), fg="#2c3e50",
                                                padx=15, pady=15, bg="#ffffff")
                    balance_frame.pack(fill=tk.X, pady=(0, 15))

                    balance_details = [
                        ("سنوات الخدمة", f"{service_years} سنة"),
                        ("الرصيد التلقائي", f"{auto_balance} يوم"),
                        ("الرصيد اليدوي المضاف", f"{manual_balance} يوم"),
                        ("إجمالي الرصيد", f"{total_balance} يوم"),
                        ("الإجازات المأخوذة", f"{used_leaves} يوم"),
                        ("الرصيد المتبقي", f"{remaining_balance} يوم")
                    ]

                    for label, value in balance_details:
                        detail_frame = tk.Frame(balance_frame, bg="#ffffff")
                        detail_frame.pack(fill=tk.X, pady=2)

                        tk.Label(detail_frame, text=f"{label}:", font=("Arial", 10, "bold"),
                                bg="#ffffff").pack(side=tk.LEFT)

                        color = "#e74c3c" if "المتبقي" in label else "#2c3e50"
                        tk.Label(detail_frame, text=value, font=("Arial", 10),
                                fg=color, bg="#ffffff").pack(side=tk.RIGHT)

                temp_balance_system.root.destroy()

            # زر إغلاق
            close_btn = tk.Button(main_frame, text="إغلاق",
                                 command=balance_window.destroy,
                                 font=("Arial", 12, "bold"), bg="#95a5a6", fg="white",
                                 relief=tk.RAISED, bd=2, padx=20, pady=8)
            close_btn.pack(pady=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في عرض تفاصيل الرصيد: {str(e)}")

    def open_balance_system(self):
        """فتح نظام إدارة رصيد الإجازات"""
        try:
            if BALANCE_SYSTEM_AVAILABLE:
                # إنشاء نافذة جديدة لنظام الرصيد
                balance_window = tk.Toplevel(self.root)
                balance_app = LeaveBalanceSystem(balance_window)
            else:
                messagebox.showerror("خطأ", "نظام رصيد الإجازات غير متاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في فتح نظام الرصيد: {str(e)}")

    def close_window(self):
        """إغلاق نافذة نظام إدارة الإجازات"""
        try:
            if messagebox.askyesno("إغلاق النظام", "هل أنت متأكد من إغلاق نظام إدارة الإجازات؟"):
                self.root.destroy()
        except Exception as e:
            print(f"خطأ في إغلاق نظام إدارة الإجازات: {e}")
            self.root.destroy()
    
    def delete_leave(self):
        """حذف الإجازة المحددة"""
        selected_item = self.leaves_table.focus()
        if not selected_item:
            messagebox.showwarning("تحذير", "الرجاء تحديد إجازة لحذفها")
            return
        
        # الحصول على بيانات الإجازة المحددة
        selected_values = self.leaves_table.item(selected_item)["values"]
        emp_name = selected_values[1]
        leave_type = selected_values[2]
        start_date = selected_values[3]
        
        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف", 
                             f"هل أنت متأكد من حذف إجازة {leave_type} للموظف {emp_name} بتاريخ {start_date}؟"):
            # البحث عن الإجازة في البيانات وحذفها
            for i, leave in enumerate(self.leaves_data):
                if (leave["اسم الموظف"] == emp_name and 
                    leave["نوع الإجازة"] == leave_type and 
                    leave["تاريخ البدء"] == start_date):
                    del self.leaves_data[i]
                    break
            
            # حفظ البيانات في ملف Excel
            self.save_leaves_data()
            
            # تحديث الجدول
            self.update_leaves_table()
            
            messagebox.showinfo("نجاح", "تم حذف الإجازة بنجاح")
    
    def change_leave_status(self, new_status):
        """تغيير حالة الإجازة المحددة"""
        selected_item = self.leaves_table.focus()
        if not selected_item:
            messagebox.showwarning("تحذير", "الرجاء تحديد إجازة لتغيير حالتها")
            return
        
        # الحصول على بيانات الإجازة المحددة
        selected_values = self.leaves_table.item(selected_item)["values"]
        emp_name = selected_values[1]
        leave_type = selected_values[2]
        start_date = selected_values[3]
        
        # البحث عن الإجازة في البيانات وتحديث حالتها
        for leave in self.leaves_data:
            if (leave["اسم الموظف"] == emp_name and 
                leave["نوع الإجازة"] == leave_type and 
                leave["تاريخ البدء"] == start_date):
                leave["الحالة"] = new_status
                break
        
        # حفظ البيانات في ملف Excel
        self.save_leaves_data()
        
        # تحديث الجدول
        self.update_leaves_table()
        
        messagebox.showinfo("نجاح", f"تم تغيير حالة الإجازة إلى {new_status}")
    
    def filter_leaves(self):
        """تصفية الإجازات حسب المعايير المحددة"""
        filter_criteria = self.filter_by.get()
        filter_term = self.filter_entry.get().strip().lower()
        
        if not filter_term:
            messagebox.showwarning("تحذير", "الرجاء إدخال مصطلح البحث")
            return
        
        filtered_data = []
        for leave in self.leaves_data:
            if filter_criteria == "اسم الموظف":
                if filter_term in leave.get("اسم الموظف", "").lower():
                    filtered_data.append(leave)
            elif filter_criteria == "الرقم الوظيفي":
                if filter_term in str(leave.get("الرقم الوظيفي", "")).lower():
                    filtered_data.append(leave)
            elif filter_criteria == "نوع الإجازة":
                if filter_term in leave.get("نوع الإجازة", "").lower():
                    filtered_data.append(leave)
            elif filter_criteria == "الحالة":
                if filter_term in leave.get("الحالة", "").lower():
                    filtered_data.append(leave)
        
        if not filtered_data:
            messagebox.showinfo("نتائج البحث", "لا توجد نتائج مطابقة للبحث")
        else:
            self.update_leaves_table(filtered_data)
    
    def reset_filter(self):
        """إعادة تعيين التصفية وعرض جميع الإجازات"""
        self.filter_entry.delete(0, tk.END)
        self.update_leaves_table()
    
    def export_leaves(self):
        """تصدير بيانات الإجازات إلى ملف Excel جديد"""
        try:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_file = f"leaves_export_{timestamp}.xlsx"
            
            wb = Workbook()
            ws = wb.active
            ws.title = "Leaves"
            
            # كتابة العناوين
            if self.leaves_data:
                headers = list(self.leaves_data[0].keys())
                ws.append(headers)
                
                # كتابة البيانات
                for leave in self.leaves_data:
                    ws.append(list(leave.values()))
            
            wb.save(export_file)
            messagebox.showinfo("نجاح", f"تم تصدير البيانات إلى ملف: {export_file}")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")
    
    def save_leaves_data(self):
        """حفظ بيانات الإجازات في ملف Excel"""
        try:
            wb = load_workbook(self.leaves_data_file)
            
            # إذا كانت الورقة موجودة، احذفها
            if self.leaves_sheet_name in wb.sheetnames:
                del wb[self.leaves_sheet_name]
            
            # إنشاء ورقة جديدة
            ws = wb.create_sheet(self.leaves_sheet_name)
            
            # كتابة العناوين
            if self.leaves_data:
                headers = list(self.leaves_data[0].keys())
                ws.append(headers)
                
                # كتابة البيانات
                for leave in self.leaves_data:
                    ws.append(list(leave.values()))
            
            wb.save(self.leaves_data_file)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات في ملف Excel: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = LeaveManagementSystem(root)
    root.mainloop()