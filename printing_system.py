#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الطباعة المتقدم لمنظومة إدارة الموارد البشرية
يدعم طباعة النماذج والتقارير بصيغ مختلفة
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import subprocess
import platform
from datetime import datetime
import tempfile

try:
    from reportlab.lib.pagesizes import A4, letter
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch, cm
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
    from reportlab.lib import colors
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError as e:
    REPORTLAB_AVAILABLE = False
    # لا نطبع رسالة تحذير هنا - سيتم التعامل معها في الوظائف المحددة

try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False
    # لا نطبع رسالة تحذير هنا - المكتبات اختيارية

try:
    from docx import Document
    from docx.shared import Inches, Pt
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.enum.table import WD_TABLE_ALIGNMENT
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    # لا نطبع رسالة تحذير هنا - سيتم التعامل معها في الوظائف المحددة

class PrintingSystem:
    """نظام الطباعة المتقدم"""
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
        self.setup_fonts()
        # تسجيل دالة التنظيف عند إغلاق البرنامج
        import atexit
        atexit.register(self.cleanup)

    def format_arabic_text(self, text):
        """تنسيق النص العربي للعرض الصحيح"""
        if ARABIC_SUPPORT and text:
            try:
                reshaped_text = arabic_reshaper.reshape(str(text))
                return get_display(reshaped_text)
            except:
                return str(text)
        return str(text)
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        if REPORTLAB_AVAILABLE:
            try:
                # محاولة تسجيل خط عربي
                font_path = self.find_arabic_font()
                if font_path:
                    pdfmetrics.registerFont(TTFont('Arabic', font_path))
                    self.arabic_font = 'Arabic'
                else:
                    self.arabic_font = 'Helvetica'
            except:
                self.arabic_font = 'Helvetica'
        else:
            self.arabic_font = 'Arial'
    
    def find_arabic_font(self):
        """البحث عن خط عربي في النظام"""
        system = platform.system()
        
        if system == "Windows":
            font_paths = [
                "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/tahoma.ttf",
                "C:/Windows/Fonts/calibri.ttf"
            ]
        elif system == "Darwin":  # macOS
            font_paths = [
                "/System/Library/Fonts/Arial.ttf",
                "/Library/Fonts/Arial.ttf"
            ]
        else:  # Linux
            font_paths = [
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf"
            ]
        
        for path in font_paths:
            if os.path.exists(path):
                return path
        return None
    
    def create_employee_certificate_pdf(self, employee_data, cert_type="work"):
        """إنشاء شهادة موظف بصيغة PDF"""
        if not REPORTLAB_AVAILABLE:
            messagebox.showerror("خطأ",
                "مكتبة reportlab غير مثبتة\n\n"
                "لتثبيتها:\n"
                "pip install reportlab\n\n"
                "أو استخدم تنسيق Word بدلاً من PDF")
            return None
        
        try:
            # إصلاح النص العربي
            def fix_arabic_text(text):
                """إصلاح النص العربي للعرض الصحيح في PDF"""
                try:
                    # محاولة استخدام مكتبة arabic_reshaper و bidi
                    try:
                        import arabic_reshaper
                        from bidi.algorithm import get_display

                        # إعادة تشكيل النص العربي
                        reshaped_text = arabic_reshaper.reshape(str(text))
                        # تطبيق خوارزمية bidi للاتجاه الصحيح
                        bidi_text = get_display(reshaped_text)
                        return bidi_text
                    except ImportError:
                        # إذا لم تكن المكتبات متاحة، استخدم النص كما هو
                        return str(text)
                except:
                    return str(text)

            filename = f"{cert_type}_{employee_data.get('الرقم الوظيفي', 'unknown')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            filepath = os.path.join(self.temp_dir, filename)

            doc = SimpleDocTemplate(filepath, pagesize=A4, rightMargin=2*cm, leftMargin=2*cm,
                                  topMargin=2*cm, bottomMargin=2*cm)

            # إنشاء المحتوى
            story = []
            styles = getSampleStyleSheet()

            # إنشاء أنماط مخصصة
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1,  # وسط
                fontName=self.arabic_font
            )

            normal_style = ParagraphStyle(
                'CustomNormal',
                parent=styles['Normal'],
                fontSize=12,
                spaceAfter=12,
                fontName=self.arabic_font,
                alignment=1
            )
            
            # العنوان مع إصلاح النص العربي
            if cert_type == "work":
                title = fix_arabic_text("شهادة عمل")
            elif cert_type == "salary":
                title = fix_arabic_text("شهادة راتب")
            else:
                title = fix_arabic_text("شهادة موظف")

            story.append(Paragraph(title, title_style))
            story.append(Spacer(1, 20))

            # محتوى الشهادة مع إصلاح النص العربي
            content_lines = [
                fix_arabic_text(f"نشهد بأن السيد/ة: {employee_data.get('الاسم العربي', '')}"),
                "",
                fix_arabic_text(f"الرقم الوظيفي: {employee_data.get('الرقم الوظيفي', '')}"),
                "",
                fix_arabic_text(f"الرقم الوطني: {employee_data.get('الرقم الوطني', '')}"),
                "",
                fix_arabic_text(f"المسمى الوظيفي: {employee_data.get('المسمى الوظيفي', '')}"),
                "",
                fix_arabic_text(f"الدرجة الوظيفية: {employee_data.get('الدرجة الحالية', '')}"),
                "",
                fix_arabic_text(f"تاريخ التعيين: {employee_data.get('تاريخ التعيين', '')}"),
                "",
                fix_arabic_text(f"مكان العمل: {employee_data.get('مكان العمل الحالي', '')}"),
                "",
                fix_arabic_text("يعمل لدينا وعلى رأس عمله حتى تاريخه."),
                "",
                fix_arabic_text("وقد أعطيت له هذه الشهادة بناءً على طلبه دون أدنى مسؤولية علينا."),
                "",
                fix_arabic_text(f"التاريخ: {datetime.now().strftime('%Y-%m-%d')}")
            ]

            for line in content_lines:
                if line:
                    story.append(Paragraph(line, normal_style))
                else:
                    story.append(Spacer(1, 6))
            
            story.append(Spacer(1, 40))

            # التوقيع مع إصلاح النص العربي
            signature_style = ParagraphStyle(
                'Signature',
                parent=styles['Normal'],
                fontSize=12,
                fontName=self.arabic_font,
                alignment=2  # يمين
            )

            story.append(Paragraph(fix_arabic_text("إدارة الموارد البشرية"), signature_style))
            story.append(Spacer(1, 20))
            story.append(Paragraph(fix_arabic_text("التوقيع: _______________"), signature_style))
            
            # بناء المستند
            doc.build(story)
            
            return filepath
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء الشهادة: {str(e)}")
            return None
    
    def create_employee_certificate_word(self, employee_data, cert_type="work"):
        """إنشاء شهادة موظف بصيغة Word"""
        if not DOCX_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة python-docx غير مثبتة")
            return None
        
        try:
            filename = f"{cert_type}_{employee_data.get('الرقم الوظيفي', 'unknown')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
            filepath = os.path.join(self.temp_dir, filename)
            
            doc = Document()
            
            # إعداد الصفحة
            section = doc.sections[0]
            section.page_height = Inches(11.69)  # A4
            section.page_width = Inches(8.27)
            section.left_margin = Inches(1)
            section.right_margin = Inches(1)
            section.top_margin = Inches(1)
            section.bottom_margin = Inches(1)
            
            # العنوان
            if cert_type == "work":
                title = "شهادة عمل"
            elif cert_type == "salary":
                title = "شهادة راتب"
            else:
                title = "شهادة موظف"
            
            title_paragraph = doc.add_heading(title, level=1)
            title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # إضافة مسافة
            doc.add_paragraph()
            
            # محتوى الشهادة
            content_lines = [
                f"نشهد بأن السيد/ة: {employee_data.get('الاسم العربي', '')}",
                "",
                f"الرقم الوظيفي: {employee_data.get('الرقم الوظيفي', '')}",
                "",
                f"الرقم الوطني: {employee_data.get('الرقم الوطني', '')}",
                "",
                f"المسمى الوظيفي: {employee_data.get('المسمى الوظيفي', '')}",
                "",
                f"الدرجة الوظيفية: {employee_data.get('الدرجة الحالية', '')}",
                "",
                f"تاريخ التعيين: {employee_data.get('تاريخ التعيين', '')}",
                "",
                f"مكان العمل: {employee_data.get('مكان العمل الحالي', '')}",
                "",
                "يعمل لدينا وعلى رأس عمله حتى تاريخه.",
                "",
                "وقد أعطيت له هذه الشهادة بناءً على طلبه دون أدنى مسؤولية علينا.",
                "",
                f"التاريخ: {datetime.now().strftime('%Y-%m-%d')}"
            ]
            
            for line in content_lines:
                p = doc.add_paragraph(line)
                p.alignment = WD_ALIGN_PARAGRAPH.CENTER
                if line:  # إذا لم يكن السطر فارغاً
                    run = p.runs[0]
                    run.font.size = Pt(14)
            
            # إضافة مسافات
            for _ in range(3):
                doc.add_paragraph()
            
            # التوقيع
            signature_p = doc.add_paragraph("إدارة الموارد البشرية")
            signature_p.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            run = signature_p.runs[0]
            run.font.size = Pt(12)
            run.bold = True
            
            doc.add_paragraph()
            
            signature_line_p = doc.add_paragraph("التوقيع: _______________")
            signature_line_p.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            
            # حفظ المستند مؤقتاً
            doc.save(filepath)

            # سؤال المستخدم عن الحفظ
            suggested_name = f"شهادة_عمل_{employee_data.get('الرقم الوظيفي', 'موظف')}_{datetime.now().strftime('%Y%m%d')}.docx"

            if self.ask_save_document("شهادة العمل", suggested_name):
                saved_path = self.save_file_as(filepath, suggested_name)
                if saved_path:
                    # فتح الملف المحفوظ
                    self.open_file(saved_path)
                    # حذف الملف المؤقت
                    self.delete_temp_file(filepath)
                    return saved_path

            # إذا لم يحفظ، فقط افتح الملف المؤقت ثم احذفه
            self.open_file(filepath)
            # حذف الملف المؤقت بعد فترة قصيرة للسماح بفتحه
            import threading
            def delayed_delete():
                import time
                time.sleep(2)  # انتظار ثانيتين لفتح الملف
                self.delete_temp_file(filepath)

            threading.Thread(target=delayed_delete, daemon=True).start()
            return None  # لا نعيد مسار الملف المؤقت
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء الشهادة: {str(e)}")
            return None
    
    def create_leave_report_pdf(self, leaves_data, title="تقرير الإجازات"):
        """إنشاء تقرير إجازات بصيغة PDF مع النص العربي المصحح"""
        if not REPORTLAB_AVAILABLE:
            messagebox.showerror("خطأ",
                "مكتبة reportlab غير مثبتة\n\n"
                "لتثبيتها:\n"
                "pip install reportlab\n\n"
                "أو استخدم تصدير Excel بدلاً من PDF")
            return None

        try:
            # إصلاح النص العربي
            def fix_arabic_text(text):
                """إصلاح النص العربي للعرض الصحيح في PDF"""
                try:
                    try:
                        import arabic_reshaper
                        from bidi.algorithm import get_display

                        # إعادة تشكيل النص العربي
                        reshaped_text = arabic_reshaper.reshape(str(text))
                        # تطبيق خوارزمية bidi للاتجاه الصحيح
                        bidi_text = get_display(reshaped_text)
                        return bidi_text
                    except ImportError:
                        # إذا لم تكن المكتبات متاحة، استخدم النص كما هو
                        return str(text)
                except:
                    return str(text)

            filename = f"leaves_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            filepath = os.path.join(self.temp_dir, filename)

            doc = SimpleDocTemplate(filepath, pagesize=A4, rightMargin=1*cm, leftMargin=1*cm,
                                  topMargin=2*cm, bottomMargin=2*cm)

            story = []
            styles = getSampleStyleSheet()

            # عنوان التقرير مع إصلاح النص العربي
            title_style = ParagraphStyle(
                'ReportTitle',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=30,
                alignment=1,
                fontName=self.arabic_font
            )

            story.append(Paragraph(fix_arabic_text(title), title_style))
            story.append(Paragraph(fix_arabic_text(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d')}"), styles['Normal']))
            story.append(Spacer(1, 20))
            
            # إنشاء جدول البيانات
            if leaves_data:
                # عناوين الجدول مع إصلاح النص العربي
                headers = [
                    fix_arabic_text("الرقم الوظيفي"),
                    fix_arabic_text("اسم الموظف"),
                    fix_arabic_text("نوع الإجازة"),
                    fix_arabic_text("تاريخ البدء"),
                    fix_arabic_text("تاريخ الانتهاء"),
                    fix_arabic_text("عدد الأيام"),
                    fix_arabic_text("الحالة")
                ]

                # بيانات الجدول مع إصلاح النص العربي
                table_data = [headers]
                for leave in leaves_data:
                    row = [
                        fix_arabic_text(str(leave.get("الرقم الوظيفي", ""))),
                        fix_arabic_text(str(leave.get("اسم الموظف", ""))),
                        fix_arabic_text(str(leave.get("نوع الإجازة", ""))),
                        fix_arabic_text(str(leave.get("تاريخ البدء", ""))),
                        fix_arabic_text(str(leave.get("تاريخ الانتهاء", ""))),
                        fix_arabic_text(str(leave.get("عدد الأيام", ""))),
                        fix_arabic_text(str(leave.get("الحالة", "")))
                    ]
                    table_data.append(row)
                
                # إنشاء الجدول
                table = Table(table_data, repeatRows=1)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), self.arabic_font),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('FONTNAME', (0, 1), (-1, -1), self.arabic_font),
                    ('FONTSIZE', (0, 1), (-1, -1), 8),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                story.append(table)
            else:
                story.append(Paragraph(fix_arabic_text("لا توجد بيانات لعرضها"), styles['Normal']))
            
            # بناء المستند
            doc.build(story)

            # سؤال المستخدم عن الحفظ
            suggested_name = f"تقرير_الإجازات_{datetime.now().strftime('%Y%m%d')}.pdf"

            if self.ask_save_document("تقرير الإجازات", suggested_name):
                saved_path = self.save_file_as(filepath, suggested_name)
                if saved_path:
                    # فتح الملف المحفوظ
                    self.open_file(saved_path)
                    # حذف الملف المؤقت
                    self.delete_temp_file(filepath)
                    return saved_path

            # إذا لم يحفظ، فقط افتح الملف المؤقت ثم احذفه
            self.open_file(filepath)
            # حذف الملف المؤقت بعد فترة قصيرة
            import threading
            def delayed_delete():
                import time
                time.sleep(2)
                self.delete_temp_file(filepath)

            threading.Thread(target=delayed_delete, daemon=True).start()
            return None
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء التقرير: {str(e)}")
            return None

    def create_promotions_report_pdf(self, promotions_data, title="تقرير الترقيات"):
        """إنشاء تقرير ترقيات بصيغة PDF مع النص العربي المصحح"""
        if not REPORTLAB_AVAILABLE:
            messagebox.showerror("خطأ",
                "مكتبة reportlab غير مثبتة\n\n"
                "لتثبيتها:\n"
                "pip install reportlab\n\n"
                "أو استخدم تصدير Excel بدلاً من PDF")
            return None

        try:
            # إصلاح النص العربي
            def fix_arabic_text(text):
                """إصلاح النص العربي للعرض الصحيح في PDF"""
                try:
                    try:
                        import arabic_reshaper
                        from bidi.algorithm import get_display

                        # إعادة تشكيل النص العربي
                        reshaped_text = arabic_reshaper.reshape(str(text))
                        # تطبيق خوارزمية bidi للاتجاه الصحيح
                        bidi_text = get_display(reshaped_text)
                        return bidi_text
                    except ImportError:
                        # إذا لم تكن المكتبات متاحة، استخدم النص كما هو
                        return str(text)
                except:
                    return str(text)

            filename = f"promotions_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            filepath = os.path.join(self.temp_dir, filename)

            doc = SimpleDocTemplate(filepath, pagesize=A4, rightMargin=1*cm, leftMargin=1*cm,
                                  topMargin=2*cm, bottomMargin=2*cm)

            story = []
            styles = getSampleStyleSheet()

            # عنوان التقرير مع إصلاح النص العربي
            title_style = ParagraphStyle(
                'ReportTitle',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=30,
                alignment=1,
                fontName=self.arabic_font
            )

            story.append(Paragraph(fix_arabic_text(title), title_style))
            story.append(Paragraph(fix_arabic_text(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d')}"), styles['Normal']))
            story.append(Spacer(1, 20))

            # إنشاء جدول البيانات
            if promotions_data:
                # عناوين الجدول مع إصلاح النص العربي
                headers = [
                    fix_arabic_text("الرقم الوظيفي"),
                    fix_arabic_text("الاسم العربي"),
                    fix_arabic_text("الدرجة الحالية"),
                    fix_arabic_text("تاريخ الدرجة الحالية"),
                    fix_arabic_text("تاريخ استحقاق الترقية"),
                    fix_arabic_text("مستحق للترقية"),
                    fix_arabic_text("الدرجة الجديدة")
                ]

                # بيانات الجدول مع إصلاح النص العربي
                table_data = [headers]
                for promotion in promotions_data:
                    row = [
                        fix_arabic_text(str(promotion.get("الرقم الوظيفي", ""))),
                        fix_arabic_text(str(promotion.get("الاسم العربي", ""))),
                        fix_arabic_text(str(promotion.get("الدرجة الحالية", ""))),
                        fix_arabic_text(str(promotion.get("تاريخ الدرجة الحالية", ""))),
                        fix_arabic_text(str(promotion.get("تاريخ استحقاق الترقية", ""))),
                        fix_arabic_text(str(promotion.get("مستحق للترقية", ""))),
                        fix_arabic_text(str(promotion.get("الدرجة الجديدة", "")))
                    ]
                    table_data.append(row)

                # إنشاء الجدول
                table = Table(table_data, repeatRows=1)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), self.arabic_font),
                    ('FONTSIZE', (0, 0), (-1, 0), 9),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('FONTNAME', (0, 1), (-1, -1), self.arabic_font),
                    ('FONTSIZE', (0, 1), (-1, -1), 8),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                story.append(table)
            else:
                story.append(Paragraph(fix_arabic_text("لا توجد بيانات ترقيات لعرضها"), styles['Normal']))

            # بناء المستند
            doc.build(story)

            # سؤال المستخدم عن الحفظ
            suggested_name = f"تقرير_الترقيات_{datetime.now().strftime('%Y%m%d')}.pdf"

            if self.ask_save_document("تقرير الترقيات", suggested_name):
                saved_path = self.save_file_as(filepath, suggested_name)
                if saved_path:
                    # فتح الملف المحفوظ
                    self.open_file(saved_path)
                    # حذف الملف المؤقت
                    self.delete_temp_file(filepath)
                    return saved_path

            # إذا لم يحفظ، فقط افتح الملف المؤقت ثم احذفه
            self.open_file(filepath)
            # حذف الملف المؤقت بعد فترة قصيرة
            import threading
            def delayed_delete():
                import time
                time.sleep(2)
                self.delete_temp_file(filepath)

            threading.Thread(target=delayed_delete, daemon=True).start()
            return None

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء تقرير الترقيات: {str(e)}")
            return None

    def print_file(self, filepath):
        """طباعة ملف"""
        if not os.path.exists(filepath):
            messagebox.showerror("خطأ", "الملف غير موجود")
            return False
        
        try:
            system = platform.system()
            
            if system == "Windows":
                # طباعة على Windows
                os.startfile(filepath, "print")
            elif system == "Darwin":  # macOS
                # طباعة على macOS
                subprocess.run(["lpr", filepath])
            else:  # Linux
                # طباعة على Linux
                subprocess.run(["lp", filepath])
            
            return True
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الطباعة: {str(e)}")
            return False
    
    def open_file(self, filepath):
        """فتح ملف بالبرنامج الافتراضي"""
        if not os.path.exists(filepath):
            messagebox.showerror("خطأ", "الملف غير موجود")
            return False
        
        try:
            system = platform.system()
            
            if system == "Windows":
                os.startfile(filepath)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", filepath])
            else:  # Linux
                subprocess.run(["xdg-open", filepath])
            
            return True
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء فتح الملف: {str(e)}")
            return False
    
    def ask_save_document(self, document_type="مستند", suggested_name=None):
        """سؤال المستخدم عما إذا كان يريد حفظ المستند"""
        response = messagebox.askyesno(
            "حفظ المستند",
            f"هل تريد حفظ {document_type} في مكان معين على جهازك؟\n\n"
            "إذا اخترت 'لا' سيتم إنشاء المستند مؤقتاً وفتحه فقط."
        )
        return response

    def save_file_as(self, filepath, suggested_name=None):
        """حفظ الملف في موقع يختاره المستخدم"""
        if not os.path.exists(filepath):
            messagebox.showerror("خطأ", "الملف غير موجود")
            return None

        try:
            # تحديد نوع الملف
            file_ext = os.path.splitext(filepath)[1].lower()

            if file_ext == '.pdf':
                filetypes = [("ملفات PDF", "*.pdf"), ("جميع الملفات", "*.*")]
                title = "حفظ ملف PDF"
            elif file_ext == '.docx':
                filetypes = [("مستندات Word", "*.docx"), ("جميع الملفات", "*.*")]
                title = "حفظ مستند Word"
            elif file_ext == '.xlsx':
                filetypes = [("ملفات Excel", "*.xlsx"), ("جميع الملفات", "*.*")]
                title = "حفظ ملف Excel"
            else:
                filetypes = [("جميع الملفات", "*.*")]
                title = "حفظ الملف"

            # اختيار مكان الحفظ
            save_path = filedialog.asksaveasfilename(
                title=title,
                defaultextension=file_ext,
                filetypes=filetypes,
                initialfilename=suggested_name or os.path.basename(filepath)
            )

            if save_path:
                # نسخ الملف
                import shutil
                shutil.copy2(filepath, save_path)
                messagebox.showinfo("تم الحفظ", f"تم حفظ الملف بنجاح في:\n{save_path}")
                return save_path

            return None

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ الملف: {str(e)}")
            return None
    
    def delete_temp_file(self, filepath):
        """حذف ملف مؤقت فوري"""
        try:
            if os.path.exists(filepath):
                os.remove(filepath)
                print(f"🗑️ تم حذف الملف المؤقت: {os.path.basename(filepath)}")
        except Exception as e:
            print(f"⚠️ تعذر حذف الملف المؤقت: {e}")

    def cleanup_all_temp_files(self):
        """حذف جميع الملفات المؤقتة في المجلد"""
        try:
            if os.path.exists(self.temp_dir):
                import shutil
                file_count = len(os.listdir(self.temp_dir))
                shutil.rmtree(self.temp_dir, ignore_errors=True)
                if file_count > 0:
                    print(f"🗑️ تم حذف {file_count} ملف مؤقت")
        except Exception as e:
            print(f"⚠️ تعذر حذف المجلد المؤقت: {e}")

    def cleanup(self):
        """تنظيف الملفات المؤقتة"""
        self.cleanup_all_temp_files()

    def __del__(self):
        """تنظيف عند حذف الكائن"""
        try:
            self.cleanup()
        except:
            pass
