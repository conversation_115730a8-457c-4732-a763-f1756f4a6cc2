#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح رسالة البحث
Test Search Message Fix
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_search_message_fix():
    """اختبار إصلاح رسالة البحث"""
    print("🔍 اختبار إصلاح رسالة البحث")
    print("=" * 60)
    
    try:
        # استيراد النظام
        print("📦 استيراد نظام إدارة الموظفين...")
        import employee_management
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة الاختبار...")
        root = tk.Tk()
        root.title("اختبار إصلاح رسالة البحث")
        root.geometry("1200x800")
        
        # إنشاء النظام
        print("🔧 إنشاء نظام إدارة الموظفين...")
        current_user = {"username": "tester", "name": "مختبر النظام"}
        emp_system = employee_management.EmployeeManagementSystem(root, current_user)
        print("✅ تم إنشاء النظام بنجاح")
        
        # اختبارات مختلفة
        test_cases = [
            ("", "نص فارغ"),
            ("   ", "مسافات فقط"),
            ("أحمد", "نص عادي"),
            ("  أحمد  ", "نص مع مسافات في البداية والنهاية"),
            ("أحمد   محمد", "نص مع مسافات متعددة"),
            ("Ahmed", "نص إنجليزي"),
            ("123", "أرقام"),
            ("موظف", "مسمى وظيفي")
        ]
        
        print("\n🧪 اختبار حالات مختلفة:")
        
        for i, (test_text, description) in enumerate(test_cases, 1):
            print(f"\n📝 اختبار {i}: {description}")
            print(f"   النص: '{test_text}' (طول: {len(test_text)})")
            
            try:
                # تعيين النص
                emp_system.search_var.set(test_text)
                
                # حفظ الدوال الأصلية
                original_showwarning = messagebox.showwarning
                original_showerror = messagebox.showerror
                
                # متغير لتتبع الرسائل
                messages = []
                
                def mock_showwarning(title, message):
                    messages.append(f"تحذير: {message}")
                    print(f"     📢 رسالة تحذير: {message}")
                
                def mock_showerror(title, message):
                    messages.append(f"خطأ: {message}")
                    print(f"     📢 رسالة خطأ: {message}")
                
                # استبدال الدوال مؤقتاً
                messagebox.showwarning = mock_showwarning
                messagebox.showerror = mock_showerror
                
                # تشغيل البحث
                print("     🔍 تشغيل البحث...")
                emp_system.perform_search()
                
                # استعادة الدوال الأصلية
                messagebox.showwarning = original_showwarning
                messagebox.showerror = original_showerror
                
                # تحليل النتيجة
                if messages:
                    result = f"❌ ظهرت رسالة: {messages[0]}"
                    if "فارغ" in test_text or test_text.strip() == "":
                        result += " ✅ (متوقع)"
                    else:
                        result += " ❌ (غير متوقع!)"
                else:
                    result = "✅ تم البحث بدون رسائل خطأ"
                    if test_text.strip() != "":
                        result += " ✅ (متوقع)"
                    else:
                        result += " ❌ (غير متوقع!)"
                
                print(f"     النتيجة: {result}")
                
            except Exception as e:
                print(f"     ❌ خطأ في الاختبار: {e}")
        
        # إنشاء واجهة تفاعلية للاختبار
        create_interactive_test_interface(root, emp_system)
        
        print("\n🎉 انتهى الاختبار - الواجهة التفاعلية جاهزة")
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def create_interactive_test_interface(root, emp_system):
    """إنشاء واجهة تفاعلية للاختبار"""
    
    # إطار الاختبار
    test_frame = tk.Frame(root, bg="#d4edda")
    test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
    
    # عنوان
    title_label = tk.Label(test_frame, text="🔍 اختبار إصلاح رسالة البحث", 
                          bg="#d4edda", fg="#155724", font=("Arial", 14, "bold"))
    title_label.pack(pady=5)
    
    # تعليمات
    instructions_text = """
الآن يمكنك اختبار البحث بنفسك:
1. اكتب نص في حقل البحث أعلاه
2. اضغط زر "🔍 بحث" أو استخدم الأزرار أدناه
3. لاحظ الرسائل في وحدة التحكم
    """
    instructions_label = tk.Label(test_frame, text=instructions_text, bg="#d4edda", 
                                 fg="#155724", font=("Arial", 10), justify=tk.LEFT)
    instructions_label.pack(pady=5)
    
    # متغير لعرض النتائج
    result_var = tk.StringVar()
    result_label = tk.Label(test_frame, textvariable=result_var, bg="#d4edda", 
                           fg="#155724", font=("Arial", 10, "bold"))
    result_label.pack(pady=5)
    
    # أزرار الاختبار السريع
    buttons_frame = tk.Frame(test_frame, bg="#d4edda")
    buttons_frame.pack(pady=10)
    
    def test_with_text(text, desc):
        emp_system.search_var.set(text)
        result_var.set(f"تم تعيين النص: '{text}' - {desc}")
        print(f"🧪 تم تعيين نص الاختبار: {desc} - '{text}'")
    
    def test_current_search():
        current_text = emp_system.search_var.get()
        print(f"\n🔍 اختبار النص الحالي: '{current_text}'")
        
        # حفظ الدوال الأصلية
        original_showwarning = messagebox.showwarning
        original_showerror = messagebox.showerror
        
        messages = []
        
        def mock_showwarning(title, message):
            messages.append(f"تحذير: {message}")
            print(f"📢 رسالة تحذير: {message}")
        
        def mock_showerror(title, message):
            messages.append(f"خطأ: {message}")
            print(f"📢 رسالة خطأ: {message}")
        
        messagebox.showwarning = mock_showwarning
        messagebox.showerror = mock_showerror
        
        try:
            emp_system.perform_search()
            
            if messages:
                result_var.set(f"❌ ظهرت رسالة: {messages[0]}")
            else:
                result_var.set("✅ تم البحث بنجاح بدون رسائل خطأ")
                
        except Exception as e:
            result_var.set(f"❌ خطأ: {e}")
        finally:
            messagebox.showwarning = original_showwarning
            messagebox.showerror = original_showerror
    
    def show_debug_info():
        current_text = emp_system.search_var.get()
        print(f"\n🔬 معلومات التشخيص:")
        print(f"   النص الحالي: '{current_text}'")
        print(f"   طول النص: {len(current_text)}")
        print(f"   رموز ASCII: {[ord(c) for c in current_text]}")
        
        # تنظيف النص
        cleaned = current_text.strip()
        cleaned = ' '.join(cleaned.split())
        print(f"   النص بعد التنظيف: '{cleaned}'")
        print(f"   طول النص المنظف: {len(cleaned)}")
        
        result_var.set(f"معلومات التشخيص في وحدة التحكم")
    
    # أزرار اختبار النصوص
    tk.Label(buttons_frame, text="اختبار نصوص:", bg="#d4edda", 
            font=("Arial", 10, "bold")).pack(side=tk.LEFT)
    
    test_texts = [
        ("", "فارغ"),
        ("   ", "مسافات"),
        ("أحمد", "عادي"),
        ("  أحمد  ", "مسافات زائدة"),
        ("أحمد   محمد", "مسافات متعددة")
    ]
    
    for text, desc in test_texts:
        btn = tk.Button(buttons_frame, text=desc,
                       command=lambda t=text, d=desc: test_with_text(t, d),
                       bg="#28a745", fg="white", font=("Arial", 8))
        btn.pack(side=tk.LEFT, padx=2)
    
    # أزرار الوظائف
    functions_frame = tk.Frame(test_frame, bg="#d4edda")
    functions_frame.pack(pady=5)
    
    tk.Label(functions_frame, text="وظائف:", bg="#d4edda", 
            font=("Arial", 10, "bold")).pack(side=tk.LEFT)
    
    tk.Button(functions_frame, text="🔍 اختبار البحث الحالي",
             command=test_current_search,
             bg="#007bff", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(functions_frame, text="🔬 معلومات التشخيص",
             command=show_debug_info,
             bg="#6f42c1", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(functions_frame, text="🧹 تنظيف النص",
             command=lambda: emp_system.clean_search_text(),
             bg="#20c997", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(functions_frame, text="📝 فحص النص",
             command=lambda: emp_system.debug_search_text(),
             bg="#fd7e14", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    # زر إغلاق
    tk.Button(test_frame, text="❌ إغلاق الاختبار", 
             command=root.destroy,
             bg="#dc3545", fg="white", 
             font=("Arial", 12, "bold")).pack(pady=10)

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار إصلاح رسالة البحث")
    print("=" * 60)
    
    test_search_message_fix()

if __name__ == "__main__":
    main()
