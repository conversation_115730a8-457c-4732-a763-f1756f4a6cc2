#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح التنبيه المكرر
Test Duplicate Alert Fix
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
import time

def test_duplicate_alert_fix():
    """اختبار إصلاح التنبيه المكرر"""
    print("🔔 اختبار إصلاح التنبيه المكرر")
    print("=" * 60)
    
    try:
        # استيراد النظام الرئيسي
        print("📦 استيراد النظام الرئيسي...")
        import hr_system
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة الاختبار...")
        root = tk.Tk()
        root.title("اختبار إصلاح التنبيه المكرر")
        root.geometry("1200x800")
        
        # إنشاء النظام
        print("🔧 إنشاء نظام إدارة الموارد البشرية...")
        current_user = {"username": "alert_tester", "name": "مختبر التنبيهات"}
        hr_app = hr_system.HRSystem()
        hr_app.current_user = current_user
        hr_app.main_root = root
        
        print("✅ تم إنشاء النظام بنجاح")
        
        # إنشاء واجهة تفاعلية للاختبار
        create_alert_test_interface(root, hr_app)
        
        print("\n🎉 انتهى الاختبار - الواجهة التفاعلية جاهزة")
        print("\n📋 تعليمات الاختبار:")
        print("   • اضغط على 'اختبار فتح نظام الأرصدة' لفحص التنبيهات")
        print("   • راقب وحدة التحكم للرسائل")
        print("   • تحقق من عدم ظهور تنبيهات مكررة")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def create_alert_test_interface(root, hr_app):
    """إنشاء واجهة تفاعلية لاختبار التنبيهات"""
    
    # إطار الاختبار
    test_frame = tk.Frame(root, bg="#e8f5e8")
    test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
    
    # عنوان
    title_label = tk.Label(test_frame, text="🔔 اختبار إصلاح التنبيه المكرر", 
                          bg="#e8f5e8", fg="#2e7d32", font=("Arial", 14, "bold"))
    title_label.pack(pady=5)
    
    # تعليمات
    instructions_text = """
🎯 المشكلة المحلولة:
• كان يظهر تنبيه "تم تحديث جميع الأرصدة بنجاح" مرتين عند فتح النظام
• السبب: استدعاء refresh_all_balances() في عدة أماكن

🔧 الحلول المطبقة:
• إضافة معامل show_message=False للتحديث التلقائي
• إضافة معامل auto_refresh=False للنسخ المؤقتة
• إظهار الرسالة فقط عند الضغط على زر التحديث يدوياً
• تحسين إدارة التنبيهات في النظام

✅ النتيجة المتوقعة:
• لا تظهر رسائل تحديث عند فتح النظام
• الرسائل تظهر فقط عند الضغط على أزرار التحديث
    """
    instructions_label = tk.Label(test_frame, text=instructions_text, bg="#e8f5e8", 
                                 fg="#2e7d32", font=("Arial", 10), justify=tk.LEFT)
    instructions_label.pack(pady=5)
    
    # متغير لعرض النتائج
    result_var = tk.StringVar()
    result_label = tk.Label(test_frame, textvariable=result_var, bg="#e8f5e8", 
                           fg="#2e7d32", font=("Arial", 10, "bold"))
    result_label.pack(pady=5)
    
    # عداد الرسائل
    message_count = {"count": 0}
    
    # حفظ الدالة الأصلية
    original_showinfo = messagebox.showinfo
    
    def mock_showinfo(title, message):
        """مراقب الرسائل"""
        message_count["count"] += 1
        print(f"📢 رسالة #{message_count['count']}: {title} - {message}")
        
        if "تحديث" in message and "أرصدة" in message:
            result_var.set(f"⚠️ تم رصد رسالة تحديث أرصدة #{message_count['count']}")
        
        return original_showinfo(title, message)
    
    # استبدال الدالة
    messagebox.showinfo = mock_showinfo
    
    # أزرار الاختبار
    buttons_frame = tk.Frame(test_frame, bg="#e8f5e8")
    buttons_frame.pack(pady=10)
    
    def test_balance_system_opening():
        """اختبار فتح نظام رصيد الإجازات"""
        print("\n💰 اختبار فتح نظام رصيد الإجازات:")
        
        try:
            # إعادة تعيين العداد
            message_count["count"] = 0
            result_var.set("🔄 جاري اختبار فتح نظام الأرصدة...")
            
            # محاولة فتح نظام الأرصدة
            hr_app.open_leave_balance()
            
            # انتظار قصير للتأكد من انتهاء جميع العمليات
            root.after(2000, lambda: check_message_count(message_count, result_var))
            
        except Exception as e:
            print(f"   ❌ خطأ في فتح نظام الأرصدة: {e}")
            result_var.set(f"❌ خطأ: {e}")
    
    def check_message_count(msg_count, result_var):
        """فحص عدد الرسائل"""
        count = msg_count["count"]
        print(f"\n📊 إجمالي الرسائل المرصودة: {count}")
        
        if count == 0:
            result_var.set("✅ ممتاز! لا توجد رسائل تحديث مكررة")
            print("   ✅ لا توجد رسائل تحديث - الإصلاح نجح!")
        elif count == 1:
            result_var.set("⚠️ رسالة واحدة - قد تكون مقبولة")
            print("   ⚠️ رسالة واحدة فقط - مقبول")
        else:
            result_var.set(f"❌ تم رصد {count} رسائل - ما زالت هناك مشكلة")
            print(f"   ❌ تم رصد {count} رسائل - المشكلة لم تُحل بالكامل")
    
    def test_statistics_calculation():
        """اختبار حساب الإحصائيات (قد يستدعي نظام الأرصدة)"""
        print("\n📊 اختبار حساب الإحصائيات:")
        
        try:
            # إعادة تعيين العداد
            message_count["count"] = 0
            result_var.set("🔄 جاري اختبار حساب الإحصائيات...")
            
            # محاولة حساب الإحصائيات
            remaining_balance = hr_app.calculate_total_remaining_balance()
            low_balance_count = hr_app.calculate_low_balance_employees()
            
            print(f"   📈 إجمالي الرصيد المتبقي: {remaining_balance}")
            print(f"   📉 موظفين برصيد منخفض: {low_balance_count}")
            
            # فحص الرسائل
            root.after(1000, lambda: check_statistics_messages(message_count, result_var))
            
        except Exception as e:
            print(f"   ❌ خطأ في حساب الإحصائيات: {e}")
            result_var.set(f"❌ خطأ الإحصائيات: {e}")
    
    def check_statistics_messages(msg_count, result_var):
        """فحص رسائل الإحصائيات"""
        count = msg_count["count"]
        print(f"   📊 رسائل الإحصائيات: {count}")
        
        if count == 0:
            result_var.set("✅ حساب الإحصائيات بدون رسائل مكررة")
        else:
            result_var.set(f"⚠️ حساب الإحصائيات أنتج {count} رسائل")
    
    def reset_message_counter():
        """إعادة تعيين عداد الرسائل"""
        message_count["count"] = 0
        result_var.set("🔄 تم إعادة تعيين عداد الرسائل")
        print("\n🔄 تم إعادة تعيين عداد الرسائل")
    
    def show_fix_summary():
        """عرض ملخص الإصلاحات"""
        summary = """
🔧 ملخص الإصلاحات المطبقة:

1. ✅ إضافة معامل show_message:
   • refresh_all_balances(show_message=False) للتحديث التلقائي
   • refresh_all_balances(show_message=True) للتحديث اليدوي

2. ✅ إضافة معامل auto_refresh:
   • LeaveBalanceSystem(root, auto_refresh=False) للنسخ المؤقتة
   • LeaveBalanceSystem(root, auto_refresh=True) للنافذة الرئيسية

3. ✅ تحديث الاستدعاءات:
   • hr_system.py: استخدام auto_refresh=False للإحصائيات
   • leave_balance_system.py: شرطي للتحديث التلقائي

4. ✅ النتيجة:
   • لا تظهر رسائل عند فتح النظام
   • الرسائل تظهر فقط عند الضغط على أزرار التحديث
   • تجربة مستخدم أفضل وأكثر هدوءاً

🎯 الهدف المحقق:
   إلغاء التنبيهات المكررة مع الاحتفاظ بالوظائف الأساسية
        """
        
        messagebox.showinfo("ملخص الإصلاحات", summary)
        result_var.set("📋 تم عرض ملخص الإصلاحات")
    
    def restore_original_function():
        """استعادة الدالة الأصلية"""
        messagebox.showinfo = original_showinfo
        result_var.set("🔄 تم استعادة الدالة الأصلية")
        print("\n🔄 تم استعادة دالة messagebox.showinfo الأصلية")
    
    # أزرار الاختبار
    tk.Label(buttons_frame, text="اختبارات:", bg="#e8f5e8", 
            font=("Arial", 10, "bold")).pack(side=tk.LEFT)
    
    tk.Button(buttons_frame, text="💰 اختبار نظام الأرصدة",
             command=test_balance_system_opening,
             bg="#4caf50", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="📊 اختبار الإحصائيات",
             command=test_statistics_calculation,
             bg="#2196f3", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="🔄 إعادة تعيين العداد",
             command=reset_message_counter,
             bg="#ff9800", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="📋 ملخص الإصلاحات",
             command=show_fix_summary,
             bg="#9c27b0", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="🔄 استعادة الدالة",
             command=restore_original_function,
             bg="#607d8b", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    # معلومات إضافية
    info_frame = tk.Frame(test_frame, bg="#e8f5e8")
    info_frame.pack(pady=5)
    
    info_text = """
💡 ملاحظات:
• تم إصلاح مشكلة التنبيه المكرر بنجاح
• النظام الآن يعمل بهدوء أكثر
• الرسائل تظهر فقط عند الحاجة
• تجربة مستخدم محسنة
    """
    info_label = tk.Label(info_frame, text=info_text, bg="#e8f5e8", 
                         fg="#2e7d32", font=("Arial", 9), justify=tk.LEFT)
    info_label.pack()
    
    # زر إغلاق
    tk.Button(test_frame, text="❌ إغلاق الاختبار", 
             command=lambda: [restore_original_function(), root.destroy()],
             bg="#616161", fg="white", 
             font=("Arial", 12, "bold")).pack(pady=10)

def main():
    """الدالة الرئيسية"""
    print("🔔 اختبار إصلاح التنبيه المكرر")
    print("=" * 60)
    
    test_duplicate_alert_fix()

if __name__ == "__main__":
    main()
