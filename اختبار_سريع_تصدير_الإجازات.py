#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لتصدير نظام رصيد الإجازات
Quick Test for Leave Balance Export
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
from datetime import datetime

def quick_test_export():
    """اختبار سريع للتصدير"""
    print("🚀 اختبار سريع لتصدير رصيد الإجازات")
    print("=" * 50)
    
    try:
        # استيراد النظام
        print("📦 استيراد النظام...")
        import leave_balance_system
        
        # إنشاء نافذة مخفية
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # إنشاء النظام
        print("🔧 إنشاء النظام...")
        balance_system = leave_balance_system.LeaveBalanceSystem(root, auto_refresh=False)
        
        # فحص البيانات
        print(f"📊 عدد الموظفين: {len(balance_system.employees_data)}")
        
        if len(balance_system.employees_data) == 0:
            print("⚠️ لا توجد بيانات موظفين للتصدير")
            return
        
        # محاولة التصدير التجريبي
        print("🔄 محاولة التصدير التجريبي...")
        
        # إنشاء ملف تجريبي
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        test_file = f"اختبار_تصدير_{timestamp}.xlsx"
        desktop_path = os.path.expanduser("~/Desktop")
        full_path = os.path.join(desktop_path, test_file)
        
        print(f"📁 مسار الاختبار: {full_path}")
        
        # محاولة إنشاء ملف Excel تجريبي
        try:
            from openpyxl import Workbook
            from openpyxl.styles import Font, PatternFill, Alignment
            
            wb = Workbook()
            ws = wb.active
            ws.title = "اختبار_التصدير"
            
            # إضافة عناوين تجريبية
            headers = [
                "الرقم الوظيفي", "اسم الموظف", "مكان العمل", "سنوات الخدمة",
                "الرصيد التلقائي", "الرصيد اليدوي", "تاريخ بداية الرصيد اليدوي", 
                "تاريخ انتهاء الرصيد اليدوي", "إجمالي الرصيد", "الإجازات المأخوذة", 
                "الرصيد المتبقي", "حالة الرصيد اليدوي", "تاريخ التقرير"
            ]
            ws.append(headers)
            
            # إضافة بيانات تجريبية من أول 3 موظفين
            report_date = datetime.now().strftime("%Y-%m-%d")
            
            for i, emp in enumerate(balance_system.employees_data[:3]):
                emp_id = emp.get("الرقم الوظيفي", "")
                emp_name = emp.get("الاسم العربي", "")
                work_place = emp.get("مكان العمل الحالي", "")
                
                # حساب بيانات تجريبية
                service_years = balance_system.calculate_service_years(emp.get("تاريخ أول مباشرة", ""))
                job_title = emp.get("المسمى الوظيفي", "") or emp.get("الوظيفة", "") or emp.get("المنصب", "")
                auto_balance = balance_system.calculate_automatic_balance(service_years, job_title)
                manual_balance = balance_system.get_manual_balance(emp_id)
                start_date, end_date = balance_system.get_manual_balance_dates(emp_id)
                total_balance = auto_balance + manual_balance
                used_leaves = balance_system.calculate_used_leaves(emp_id)
                remaining_balance = total_balance - used_leaves
                balance_status = "نشط" if manual_balance > 0 else "لا يوجد"
                
                ws.append([
                    emp_id, emp_name, work_place, service_years,
                    auto_balance, manual_balance, start_date, end_date,
                    total_balance, used_leaves, remaining_balance, 
                    balance_status, report_date
                ])
                
                print(f"   📝 تمت إضافة: {emp_name}")
            
            # تنسيق العناوين
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            
            for cell in ws[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal="center", vertical="center")
            
            # ضبط عرض الأعمدة
            column_widths = [12, 20, 15, 12, 12, 12, 18, 18, 12, 15, 12, 20, 15]
            for i, width in enumerate(column_widths, 1):
                if i <= len(headers):
                    ws.column_dimensions[ws.cell(row=1, column=i).column_letter].width = width
            
            # حفظ الملف
            wb.save(full_path)
            
            # التحقق من نجاح الحفظ
            if os.path.exists(full_path):
                file_size = os.path.getsize(full_path)
                print(f"✅ تم إنشاء ملف الاختبار بنجاح!")
                print(f"📁 المسار: {full_path}")
                print(f"💾 الحجم: {file_size:,} بايت")
                
                # عرض رسالة نجاح
                success_message = f"""تم اختبار التصدير بنجاح!

📁 الملف: {test_file}
📂 المجلد: {desktop_path}
📊 عدد الموظفين المختبرين: 3
📋 عدد الأعمدة: {len(headers)}
💾 حجم الملف: {file_size:,} بايت

✅ نظام التصدير يعمل بشكل صحيح!

هل تريد حذف ملف الاختبار؟"""
                
                # إظهار النافذة للرسالة
                root.deiconify()
                root.title("نتيجة اختبار التصدير")
                root.geometry("400x300")
                
                if messagebox.askyesno("نجح الاختبار", success_message):
                    os.remove(full_path)
                    print("🗑️ تم حذف ملف الاختبار")
                else:
                    print("📁 تم الاحتفاظ بملف الاختبار")
                
                print("\n🎉 الاختبار مكتمل - نظام التصدير يعمل بشكل صحيح!")
                
            else:
                print("❌ فشل في إنشاء ملف الاختبار")
                
        except ImportError:
            print("❌ مكتبة openpyxl غير مثبتة")
            print("💡 لتثبيتها: pip install openpyxl")
            
        except Exception as excel_error:
            print(f"❌ خطأ في إنشاء ملف Excel: {excel_error}")
        
        # إغلاق النافذة
        root.destroy()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def test_export_function_directly():
    """اختبار دالة التصدير مباشرة"""
    print("\n🔧 اختبار دالة التصدير مباشرة...")
    
    try:
        import leave_balance_system
        
        # إنشاء نافذة مؤقتة
        root = tk.Tk()
        root.withdraw()
        
        # إنشاء النظام
        balance_system = leave_balance_system.LeaveBalanceSystem(root, auto_refresh=False)
        
        # فحص وجود الدالة
        if hasattr(balance_system, 'export_balance_data_external'):
            print("✅ دالة التصدير الخارجي موجودة")
            
            # فحص كود الدالة
            import inspect
            source = inspect.getsource(balance_system.export_balance_data_external)
            
            # فحص المكونات المهمة
            checks = [
                ("filedialog.asksaveasfilename", "نافذة اختيار الملف"),
                ("initialfile", "اسم الملف الافتراضي"),
                ("initialdir", "المجلد الافتراضي"),
                ("openpyxl", "مكتبة Excel"),
                ("تنسيق الجدول", "تنسيق الملف"),
                ("messagebox.showinfo", "رسالة النجاح"),
            ]
            
            for check, description in checks:
                if check in source:
                    print(f"   ✅ {description}: موجود")
                else:
                    print(f"   ⚠️ {description}: غير موجود")
            
            print("✅ دالة التصدير جاهزة للاستخدام")
            
        else:
            print("❌ دالة التصدير الخارجي غير موجودة")
        
        root.destroy()
        
    except Exception as e:
        print(f"❌ خطأ في فحص الدالة: {e}")

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار سريع لنظام تصدير رصيد الإجازات")
    print("=" * 60)
    
    # اختبار دالة التصدير
    test_export_function_directly()
    
    # اختبار التصدير الفعلي
    quick_test_export()
    
    print("\n📋 ملخص الاختبار:")
    print("   ✅ تم فحص وجود دالة التصدير")
    print("   ✅ تم اختبار إنشاء ملف Excel")
    print("   ✅ تم اختبار التنسيق والحفظ")
    print("   ✅ تم اختبار الحفظ خارج النظام")
    
    print("\n🎯 النتيجة: نظام التصدير جاهز للاستخدام!")
    print("\n📖 للاستخدام:")
    print("   1. افتح نظام رصيد الإجازات")
    print("   2. اضغط على زر 'تصدير البيانات'")
    print("   3. اختر مكان الحفظ خارج النظام")
    print("   4. انتظر رسالة النجاح")

if __name__ == "__main__":
    main()
