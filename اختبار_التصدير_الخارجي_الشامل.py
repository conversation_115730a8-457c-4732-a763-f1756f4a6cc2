#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التصدير الخارجي الشامل لجميع أنظمة النظام
Test External Export for All System Modules
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
from datetime import datetime
import tempfile

def test_external_export_systems():
    """اختبار أنظمة التصدير الخارجي"""
    print("🔧 اختبار أنظمة التصدير الخارجي")
    print("=" * 60)
    
    try:
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة الاختبار...")
        root = tk.Tk()
        root.title("اختبار التصدير الخارجي الشامل")
        root.geometry("1400x900")
        
        # اختبار الأنظمة
        systems_to_test = [
            ("employee_management", "نظام إدارة الموظفين"),
            ("leave_management", "نظام إدارة الإجازات"),
            ("leave_balance_system", "نظام رصيد الإجازات"),
            ("reports_system", "نظام التقارير")
        ]
        
        test_results = {}
        
        for system_module, system_name in systems_to_test:
            print(f"\n🔍 اختبار {system_name}...")
            try:
                # استيراد النظام
                module = __import__(system_module)
                print(f"   ✅ تم استيراد {system_name}")
                
                # فحص وجود دوال التصدير الخارجي
                external_export_functions = []
                
                # البحث عن دوال التصدير الخارجي
                for attr_name in dir(module):
                    if "export" in attr_name.lower() and "external" in attr_name.lower():
                        external_export_functions.append(attr_name)
                
                # فحص الكلاسات للبحث عن دوال التصدير
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if isinstance(attr, type):  # إذا كان كلاس
                        for method_name in dir(attr):
                            if "export" in method_name.lower() and "external" in method_name.lower():
                                external_export_functions.append(f"{attr_name}.{method_name}")
                
                test_results[system_name] = {
                    "imported": True,
                    "external_functions": external_export_functions,
                    "status": "✅ جاهز" if external_export_functions else "⚠️ يحتاج تحديث"
                }
                
                print(f"   📊 دوال التصدير الخارجي: {len(external_export_functions)}")
                for func in external_export_functions:
                    print(f"     • {func}")
                
            except Exception as e:
                print(f"   ❌ خطأ في {system_name}: {e}")
                test_results[system_name] = {
                    "imported": False,
                    "external_functions": [],
                    "status": f"❌ خطأ: {e}",
                    "error": str(e)
                }
        
        # إنشاء واجهة تفاعلية للاختبار
        create_export_test_interface(root, test_results)
        
        print("\n🎉 انتهى الاختبار - الواجهة التفاعلية جاهزة")
        print("\n📋 تعليمات الاختبار:")
        print("   • جرب أزرار التصدير الخارجي في كل نظام")
        print("   • تأكد من حفظ الملفات خارج مجلد النظام")
        print("   • فحص محتوى الملفات المصدرة")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def create_export_test_interface(root, test_results):
    """إنشاء واجهة تفاعلية لاختبار التصدير الخارجي"""
    
    # إطار الاختبار
    test_frame = tk.Frame(root, bg="#e8f5e8")
    test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
    
    # عنوان
    title_label = tk.Label(test_frame, text="🔧 اختبار أنظمة التصدير الخارجي", 
                          bg="#e8f5e8", fg="#2e7d32", font=("Arial", 16, "bold"))
    title_label.pack(pady=10)
    
    # نتائج الاختبار
    results_frame = tk.LabelFrame(test_frame, text="نتائج الاختبار", 
                                 bg="#e8f5e8", fg="#2e7d32", font=("Arial", 12, "bold"))
    results_frame.pack(fill=tk.X, pady=10)
    
    for system_name, result in test_results.items():
        system_frame = tk.Frame(results_frame, bg="#ffffff", relief=tk.RAISED, bd=1)
        system_frame.pack(fill=tk.X, padx=5, pady=2)
        
        # اسم النظام والحالة
        status_text = f"{system_name}: {result['status']}"
        status_label = tk.Label(system_frame, text=status_text, 
                               bg="#ffffff", font=("Arial", 11, "bold"))
        status_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # عدد دوال التصدير
        functions_count = len(result['external_functions'])
        count_text = f"دوال التصدير: {functions_count}"
        count_label = tk.Label(system_frame, text=count_text, 
                              bg="#ffffff", font=("Arial", 10))
        count_label.pack(side=tk.RIGHT, padx=10, pady=5)
    
    # متغير لعرض النتائج
    result_var = tk.StringVar()
    result_label = tk.Label(test_frame, textvariable=result_var, bg="#e8f5e8", 
                           fg="#2e7d32", font=("Arial", 11, "bold"))
    result_label.pack(pady=10)
    
    # أزرار الاختبار
    buttons_frame = tk.Frame(test_frame, bg="#e8f5e8")
    buttons_frame.pack(pady=10)
    
    def test_employee_export():
        """اختبار تصدير الموظفين"""
        try:
            print("\n👥 اختبار تصدير الموظفين...")
            import employee_management
            
            # إنشاء نظام إدارة الموظفين
            emp_root = tk.Tk()
            emp_root.withdraw()  # إخفاء النافذة
            
            emp_system = employee_management.EmployeeManagementSystem(emp_root)
            
            # فحص وجود دالة التصدير الخارجي
            if hasattr(emp_system, 'export_to_excel_external'):
                result_var.set("✅ نظام الموظفين: دالة التصدير الخارجي موجودة")
                print("   ✅ دالة التصدير الخارجي موجودة")
            else:
                result_var.set("❌ نظام الموظفين: دالة التصدير الخارجي غير موجودة")
                print("   ❌ دالة التصدير الخارجي غير موجودة")
            
            emp_root.destroy()
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار تصدير الموظفين: {e}")
            result_var.set(f"❌ خطأ الموظفين: {e}")
    
    def test_leaves_export():
        """اختبار تصدير الإجازات"""
        try:
            print("\n🏖️ اختبار تصدير الإجازات...")
            import leave_management
            
            # إنشاء نظام إدارة الإجازات
            leave_root = tk.Tk()
            leave_root.withdraw()  # إخفاء النافذة
            
            leave_system = leave_management.LeaveManagementSystem(leave_root)
            
            # فحص تحديث دالة التصدير
            import inspect
            export_method = getattr(leave_system, 'export_leaves', None)
            if export_method:
                # فحص كود الدالة للتأكد من التحديث
                source = inspect.getsource(export_method)
                if "filedialog.asksaveasfilename" in source and "خارج النظام" in source:
                    result_var.set("✅ نظام الإجازات: تم تحديث التصدير الخارجي")
                    print("   ✅ تم تحديث دالة التصدير للتصدير الخارجي")
                else:
                    result_var.set("⚠️ نظام الإجازات: يحتاج تحديث التصدير")
                    print("   ⚠️ دالة التصدير تحتاج تحديث")
            else:
                result_var.set("❌ نظام الإجازات: دالة التصدير غير موجودة")
                print("   ❌ دالة التصدير غير موجودة")
            
            leave_root.destroy()
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار تصدير الإجازات: {e}")
            result_var.set(f"❌ خطأ الإجازات: {e}")
    
    def test_balance_export():
        """اختبار تصدير رصيد الإجازات"""
        try:
            print("\n💰 اختبار تصدير رصيد الإجازات...")
            import leave_balance_system
            
            # إنشاء نظام رصيد الإجازات
            balance_root = tk.Tk()
            balance_root.withdraw()  # إخفاء النافذة
            
            balance_system = leave_balance_system.LeaveBalanceSystem(balance_root)
            
            # فحص وجود دالة التصدير الخارجي
            if hasattr(balance_system, 'export_balance_data_external'):
                result_var.set("✅ نظام الرصيد: دالة التصدير الخارجي موجودة")
                print("   ✅ دالة التصدير الخارجي موجودة")
            else:
                result_var.set("❌ نظام الرصيد: دالة التصدير الخارجي غير موجودة")
                print("   ❌ دالة التصدير الخارجي غير موجودة")
            
            balance_root.destroy()
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار تصدير الرصيد: {e}")
            result_var.set(f"❌ خطأ الرصيد: {e}")
    
    def test_reports_export():
        """اختبار تصدير التقارير"""
        try:
            print("\n📊 اختبار تصدير التقارير...")
            import reports_system
            
            # إنشاء نظام التقارير
            reports_root = tk.Tk()
            reports_root.withdraw()  # إخفاء النافذة
            
            reports_sys = reports_system.ReportsSystem(reports_root)
            
            # فحص وجود دالة التصدير الخارجي
            if hasattr(reports_sys, 'export_report_external'):
                result_var.set("✅ نظام التقارير: دالة التصدير الخارجي موجودة")
                print("   ✅ دالة التصدير الخارجي موجودة")
            else:
                result_var.set("❌ نظام التقارير: دالة التصدير الخارجي غير موجودة")
                print("   ❌ دالة التصدير الخارجي غير موجودة")
            
            reports_root.destroy()
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار تصدير التقارير: {e}")
            result_var.set(f"❌ خطأ التقارير: {e}")
    
    def test_file_location():
        """اختبار مكان حفظ الملفات"""
        try:
            print("\n📁 اختبار مكان حفظ الملفات...")
            
            # إنشاء ملف تجريبي
            test_content = f"""تقرير تجريبي
تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
الغرض: اختبار التصدير الخارجي
"""
            
            # محاولة الحفظ في سطح المكتب
            desktop_path = os.path.expanduser("~/Desktop")
            test_file = os.path.join(desktop_path, f"اختبار_التصدير_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
            
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(test_content)
            
            if os.path.exists(test_file):
                result_var.set(f"✅ تم إنشاء ملف تجريبي في: {desktop_path}")
                print(f"   ✅ تم إنشاء ملف تجريبي: {test_file}")
                
                # حذف الملف التجريبي
                os.remove(test_file)
                print("   🗑️ تم حذف الملف التجريبي")
            else:
                result_var.set("❌ فشل في إنشاء الملف التجريبي")
                print("   ❌ فشل في إنشاء الملف التجريبي")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار مكان الحفظ: {e}")
            result_var.set(f"❌ خطأ مكان الحفظ: {e}")
    
    def show_export_guide():
        """عرض دليل التصدير الخارجي"""
        guide = """
📖 دليل التصدير الخارجي:

🎯 الهدف:
   • حفظ جميع التقارير والملفات خارج مجلد النظام
   • إمكانية اختيار مكان الحفظ بحرية
   • حماية ملفات النظام من التلوث

🔧 التحسينات المطبقة:

👥 نظام إدارة الموظفين:
   • دالة export_to_excel_external()
   • اختيار مكان الحفظ خارج النظام
   • تنسيق محسن للملف المصدر

🏖️ نظام إدارة الإجازات:
   • تحديث دالة export_leaves()
   • حفظ خارج النظام مع معلومات إضافية
   • تنسيق احترافي للجدول

💰 نظام رصيد الإجازات:
   • دالة export_balance_data_external()
   • أعمدة إضافية للتواريخ والحالة
   • تنبيهات للأرصدة المنتهية

📊 نظام التقارير:
   • دالة export_report_external()
   • معلومات التقرير في الأعلى
   • تنسيق شامل ومفصل

🛡️ الحماية:
   • فحص تلقائي لمنع الحفظ داخل النظام
   • اقتراح مكان بديل (سطح المكتب)
   • رسائل تأكيد مفصلة

✅ النتيجة:
   جميع الملفات المصدرة تحفظ خارج النظام!
        """
        
        messagebox.showinfo("دليل التصدير الخارجي", guide)
        result_var.set("📖 تم عرض دليل التصدير الخارجي")
    
    # أزرار الاختبار
    tk.Label(buttons_frame, text="اختبارات الأنظمة:", bg="#e8f5e8", 
            font=("Arial", 12, "bold")).pack()
    
    buttons_row1 = tk.Frame(buttons_frame, bg="#e8f5e8")
    buttons_row1.pack(pady=5)
    
    tk.Button(buttons_row1, text="👥 اختبار الموظفين",
             command=test_employee_export,
             bg="#2196f3", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row1, text="🏖️ اختبار الإجازات",
             command=test_leaves_export,
             bg="#4caf50", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row1, text="💰 اختبار الرصيد",
             command=test_balance_export,
             bg="#ff9800", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row1, text="📊 اختبار التقارير",
             command=test_reports_export,
             bg="#9c27b0", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    buttons_row2 = tk.Frame(buttons_frame, bg="#e8f5e8")
    buttons_row2.pack(pady=5)
    
    tk.Button(buttons_row2, text="📁 اختبار مكان الحفظ",
             command=test_file_location,
             bg="#607d8b", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row2, text="📖 دليل التصدير",
             command=show_export_guide,
             bg="#795548", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    # معلومات إضافية
    info_frame = tk.Frame(test_frame, bg="#e8f5e8")
    info_frame.pack(pady=10)
    
    info_text = """
💡 ملاحظات مهمة:
• جميع أنظمة التصدير تحفظ الملفات خارج مجلد النظام
• يتم اقتراح سطح المكتب كمكان افتراضي للحفظ
• الملفات تحتوي على معلومات إضافية وتنسيق محسن
• يتم فحص المسار تلقائياً لمنع الحفظ داخل النظام
    """
    info_label = tk.Label(info_frame, text=info_text, bg="#e8f5e8", 
                         fg="#2e7d32", font=("Arial", 10), justify=tk.LEFT)
    info_label.pack()
    
    # زر إغلاق
    tk.Button(test_frame, text="❌ إغلاق الاختبار", 
             command=root.destroy,
             bg="#616161", fg="white", 
             font=("Arial", 12, "bold")).pack(pady=15)

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار أنظمة التصدير الخارجي الشامل")
    print("=" * 60)
    
    test_external_export_systems()

if __name__ == "__main__":
    main()
