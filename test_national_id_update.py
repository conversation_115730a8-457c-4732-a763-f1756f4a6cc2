#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحديث الرقم الوطني الجديد (12 رقم)
Test National ID Update (12 digits)
"""

import sys
import datetime

def test_national_id_validation():
    """اختبار التحقق من الرقم الوطني المحدث"""
    print("🧪 اختبار التحقق من الرقم الوطني المحدث")
    print("=" * 60)
    
    try:
        from data_validation import DataValidator
        validator = DataValidator()
        
        # حالات اختبار الرقم الوطني
        test_cases = [
            # الرقم الوطني القديم (10 أرقام)
            ("1234567890", True, "رقم وطني سعودي قديم (10 أرقام) - صحيح"),
            ("2987654321", True, "رقم وطني سعودي قديم يبدأ بـ 2 - صحيح"),
            ("3123456789", True, "رقم وطني قديم يبدأ بـ 3 - تحذير لكن مقبول"),
            
            # الرقم الوطني الجديد (12 رقم)
            ("123456789012", True, "رقم وطني جديد (12 رقم) - صحيح"),
            ("298765432109", True, "رقم وطني جديد يبدأ بـ 2 - صحيح"),
            ("345678901234", True, "رقم وطني جديد يبدأ بـ 3 - صحيح"),
            ("456789012345", True, "رقم وطني جديد يبدأ بـ 4 - صحيح"),
            ("567890123456", True, "رقم وطني جديد يبدأ بـ 5 - صحيح"),
            
            # حالات خاطئة
            ("12345", False, "رقم قصير (5 أرقام) - خطأ"),
            ("123456789", False, "رقم قصير (9 أرقام) - خطأ"),
            ("12345678901", False, "رقم متوسط (11 رقم) - خطأ"),
            ("1234567890123", False, "رقم طويل (13 رقم) - خطأ"),
            ("12345678901234", False, "رقم طويل جداً (14 رقم) - خطأ"),
            
            # حالات تحتوي على أحرف
            ("123456789a", False, "رقم يحتوي على حرف - خطأ"),
            ("12345678901b", False, "رقم 12 رقم مع حرف - خطأ"),
            ("abcdefghij", False, "أحرف فقط - خطأ"),
            
            # حالات خاصة
            ("", True, "رقم فارغ - تحذير لكن مقبول"),
            ("   ", True, "مسافات فقط - تحذير لكن مقبول"),
            ("0123456789", True, "رقم يبدأ بصفر (10 أرقام) - تحذير"),
            ("012345678901", True, "رقم يبدأ بصفر (12 رقم) - تحذير"),
        ]
        
        passed = 0
        failed = 0
        total = len(test_cases)
        
        print(f"🔍 تشغيل {total} اختبار للرقم الوطني...")
        print()
        
        for i, (national_id, expected, description) in enumerate(test_cases, 1):
            validator.clear_messages()
            result = validator.validate_national_id(national_id)
            
            if result == expected:
                print(f"✅ اختبار {i:2d}: {description}")
                passed += 1
            else:
                print(f"❌ اختبار {i:2d}: {description}")
                print(f"    المتوقع: {expected}, الفعلي: {result}")
                if validator.get_all_messages():
                    for msg in validator.get_all_messages():
                        print(f"    رسالة: {msg}")
                failed += 1
        
        print()
        print("=" * 60)
        print(f"📊 نتائج الاختبار:")
        print(f"   إجمالي الاختبارات: {total}")
        print(f"   نجح: {passed}")
        print(f"   فشل: {failed}")
        print(f"   معدل النجاح: {(passed/total)*100:.1f}%")
        
        if failed == 0:
            print("🎉 جميع الاختبارات نجحت! الرقم الوطني الجديد مدعوم بالكامل")
        else:
            print(f"⚠️ {failed} اختبار فشل - يحتاج مراجعة")
        
        return passed, failed, total
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد نظام التحقق: {e}")
        return 0, 1, 1
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبار: {e}")
        return 0, 1, 1

def test_integration_with_employee_system():
    """اختبار التكامل مع نظام إدارة الموظفين"""
    print("\n🔗 اختبار التكامل مع نظام إدارة الموظفين")
    print("=" * 60)
    
    try:
        # محاولة استيراد نظام إدارة الموظفين
        import employee_management
        print("✅ تم استيراد نظام إدارة الموظفين بنجاح")
        
        # التحقق من وجود التحقق المحسن
        try:
            from data_validation import DataValidator
            validator = DataValidator()
            
            # اختبار أرقام وطنية مختلفة
            test_ids = [
                "1234567890",    # 10 أرقام
                "123456789012",  # 12 رقم
            ]
            
            for national_id in test_ids:
                validator.clear_messages()
                result = validator.validate_national_id(national_id)
                print(f"✅ الرقم الوطني {national_id} ({len(national_id)} رقم): {'صحيح' if result else 'خطأ'}")
            
            return True
            
        except Exception as e:
            print(f"⚠️ نظام التحقق المحسن غير متاح: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ فشل في استيراد نظام إدارة الموظفين: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 اختبار تحديث الرقم الوطني الجديد")
    print(f"📅 التاريخ: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 إصدار Python: {sys.version}")
    print()
    
    # اختبار التحقق من الرقم الوطني
    passed, failed, total = test_national_id_validation()
    
    # اختبار التكامل
    integration_success = test_integration_with_employee_system()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("📋 الملخص النهائي:")
    print(f"   🧪 اختبارات التحقق: {passed}/{total} نجح")
    print(f"   🔗 اختبار التكامل: {'نجح' if integration_success else 'فشل'}")
    
    if failed == 0 and integration_success:
        print("\n🎉 تم تحديث النظام بنجاح لدعم الرقم الوطني الجديد (12 رقم)!")
        print("✅ النظام يدعم الآن:")
        print("   • الرقم الوطني القديم (10 أرقام)")
        print("   • الرقم الوطني الجديد (12 رقم)")
        print("   • التحقق الذكي من صحة الأرقام")
        print("   • رسائل تحذير وخطأ واضحة")
    else:
        print("\n⚠️ يحتاج النظام إلى مراجعة إضافية")
    
    print("\n🏁 انتهى الاختبار")

if __name__ == "__main__":
    main()
