#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لحفظ التاريخ
Simple Date Saving Test
"""

import tkinter as tk
from tkinter import messagebox
from datetime import datetime

def test_simple_date_saving():
    """اختبار بسيط لحفظ التاريخ"""
    print("💾 اختبار بسيط لحفظ التاريخ")
    print("=" * 50)
    
    try:
        # استيراد قسم الإجازات
        print("📦 استيراد قسم الإجازات...")
        import leave_department_system
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.title("اختبار بسيط - حفظ التاريخ")
        root.geometry("600x400")
        root.configure(bg="#f0f8ff")
        
        # إنشاء قسم الإجازات
        leave_dept = leave_department_system.LeaveDepartmentSystem(root)
        
        # إطار الاختبار
        test_frame = tk.Frame(root, bg="#f0f8ff")
        test_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # عنوان
        title_label = tk.Label(test_frame, text="💾 اختبار حفظ التاريخ", 
                              bg="#f0f8ff", fg="#2c3e50", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # متغير التاريخ
        date_var = tk.StringVar()
        
        # إطار التاريخ
        date_frame = tk.LabelFrame(test_frame, text="اختبار التاريخ", 
                                  font=("Arial", 12, "bold"), bg="#f0f8ff")
        date_frame.pack(fill=tk.X, pady=20)
        
        # صف التاريخ
        row_frame = tk.Frame(date_frame, bg="#f0f8ff")
        row_frame.pack(pady=15, padx=15)
        
        tk.Label(row_frame, text="التاريخ المختار:", font=("Arial", 12, "bold"), 
                bg="#f0f8ff").pack(side=tk.LEFT, padx=5)
        
        date_entry = tk.Entry(row_frame, textvariable=date_var, width=15, 
                             font=("Arial", 12), state="readonly", bg="white")
        date_entry.pack(side=tk.LEFT, padx=10)
        
        # زر اختيار التاريخ
        def open_date_selector():
            print(f"\n🔍 فتح نافذة اختيار التاريخ...")
            print(f"📅 القيمة قبل الاختيار: '{date_var.get()}'")
            
            try:
                leave_dept.select_date(date_var)
                print(f"✅ تم استدعاء دالة اختيار التاريخ")
            except Exception as e:
                print(f"❌ خطأ في فتح نافذة التاريخ: {e}")
        
        select_btn = tk.Button(row_frame, text="📅 اختيار التاريخ", 
                              command=open_date_selector,
                              bg="#3498db", fg="white", font=("Arial", 12, "bold"), 
                              padx=15, pady=8)
        select_btn.pack(side=tk.LEFT, padx=10)
        
        # مراقب التغيير
        def on_date_change(*args):
            new_value = date_var.get()
            print(f"🔄 تغيير التاريخ: '{new_value}'")
            
            if new_value:
                try:
                    datetime.strptime(new_value, "%Y-%m-%d")
                    status_label.config(text="✅ تاريخ صحيح", fg="#27ae60")
                    print(f"✅ التاريخ صحيح: {new_value}")
                except:
                    status_label.config(text="❌ تاريخ خاطئ", fg="#e74c3c")
                    print(f"❌ التاريخ خاطئ: {new_value}")
            else:
                status_label.config(text="⏳ لم يُحدد", fg="#95a5a6")
                print(f"⏳ التاريخ فارغ")
        
        date_var.trace('w', on_date_change)
        
        # تسمية الحالة
        status_label = tk.Label(row_frame, text="⏳ لم يُحدد", 
                               font=("Arial", 11), bg="#f0f8ff", fg="#95a5a6")
        status_label.pack(side=tk.LEFT, padx=10)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(test_frame, bg="#f0f8ff")
        buttons_frame.pack(pady=20)
        
        def test_current_date():
            """اختبار التاريخ الحالي"""
            today = datetime.now().strftime("%Y-%m-%d")
            date_var.set(today)
            print(f"📅 تم تعيين التاريخ الحالي: {today}")
        
        def clear_date():
            """مسح التاريخ"""
            date_var.set("")
            print(f"🗑️ تم مسح التاريخ")
        
        def show_date_info():
            """عرض معلومات التاريخ"""
            current_date = date_var.get()
            if current_date:
                try:
                    date_obj = datetime.strptime(current_date, "%Y-%m-%d")
                    weekday_names = ["الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت", "الأحد"]
                    weekday = weekday_names[date_obj.weekday()]
                    
                    info = f"""
📅 معلومات التاريخ:

التاريخ: {current_date}
يوم الأسبوع: {weekday}
السنة: {date_obj.year}
الشهر: {date_obj.month}
اليوم: {date_obj.day}

✅ التاريخ صحيح ومحفوظ بنجاح!
                    """
                    messagebox.showinfo("معلومات التاريخ", info)
                except:
                    messagebox.showerror("خطأ", "التاريخ غير صحيح")
            else:
                messagebox.showwarning("تحذير", "لم يتم اختيار تاريخ")
        
        tk.Button(buttons_frame, text="📅 التاريخ الحالي", command=test_current_date,
                 bg="#27ae60", fg="white", font=("Arial", 11, "bold"), padx=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="🗑️ مسح", command=clear_date,
                 bg="#e74c3c", fg="white", font=("Arial", 11, "bold"), padx=15).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="ℹ️ معلومات", command=show_date_info,
                 bg="#9b59b6", fg="white", font=("Arial", 11, "bold"), padx=15).pack(side=tk.LEFT, padx=5)
        
        # إطار التعليمات
        instructions_frame = tk.LabelFrame(test_frame, text="التعليمات", 
                                          font=("Arial", 11, "bold"), bg="#f0f8ff")
        instructions_frame.pack(fill=tk.X, pady=20)
        
        instructions_text = """
📋 كيفية الاختبار:

1. اضغط على "📅 اختيار التاريخ" لفتح نافذة اختيار التاريخ
2. اختر السنة والشهر واليوم من القوائم المنسدلة
3. راجع المعاينة في النافذة
4. اضغط "✅ تأكيد الاختيار" لحفظ التاريخ
5. يجب أن يظهر التاريخ في الحقل أعلاه
6. يجب أن تظهر رسالة تأكيد
7. استخدم الأزرار الأخرى للاختبار

🎯 ما يجب أن يحدث:
• ظهور التاريخ في الحقل بعد الحفظ
• تغيير حالة التاريخ إلى "✅ تاريخ صحيح"
• طباعة رسائل في وحدة التحكم
• إغلاق نافذة اختيار التاريخ
        """
        
        instructions_label = tk.Label(instructions_frame, text=instructions_text, 
                                     font=("Arial", 10), bg="#f0f8ff", fg="#2c3e50", 
                                     justify=tk.LEFT)
        instructions_label.pack(padx=15, pady=10)
        
        # زر إغلاق
        tk.Button(test_frame, text="❌ إغلاق", command=root.destroy,
                 bg="#95a5a6", fg="white", font=("Arial", 12, "bold"), 
                 padx=20, pady=10).pack(pady=20)
        
        print("\n🎯 الاختبار جاهز!")
        print("📋 اضغط على 'اختيار التاريخ' لبدء الاختبار")
        
        # تشغيل النافذة
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    test_simple_date_saving()

if __name__ == "__main__":
    main()
