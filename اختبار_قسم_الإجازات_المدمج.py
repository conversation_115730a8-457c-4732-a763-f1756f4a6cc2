#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار قسم الإجازات المدمج في النظام الرئيسي
Test Integrated Leave Department in Main System
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
from datetime import datetime

def test_integrated_leave_department():
    """اختبار قسم الإجازات المدمج"""
    print("🏢 اختبار قسم الإجازات المدمج في النظام الرئيسي")
    print("=" * 70)
    
    try:
        # اختبار استيراد قسم الإجازات
        print("📦 اختبار استيراد قسم الإجازات...")
        import leave_department_system
        print("✅ تم استيراد قسم الإجازات بنجاح")
        
        # اختبار استيراد النظام الرئيسي
        print("📦 اختبار استيراد النظام الرئيسي...")
        import hr_system
        print("✅ تم استيراد النظام الرئيسي بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة الاختبار...")
        root = tk.Tk()
        root.title("اختبار قسم الإجازات المدمج")
        root.geometry("1200x800")
        root.configure(bg="#f0f8ff")
        
        # إنشاء النظام الرئيسي (محاكاة)
        print("🔧 إنشاء النظام الرئيسي...")
        hr_main = hr_system.HRMainSystem()
        hr_main.current_user = {"username": "admin", "role": "admin", "name": "مدير النظام"}
        hr_main.main_root = root
        hr_main.open_windows = {}
        
        # اختبار قسم الإجازات
        print("🏖️ اختبار قسم الإجازات...")
        leave_dept = leave_department_system.LeaveDepartmentSystem(root)
        print("✅ تم إنشاء قسم الإجازات بنجاح")
        
        # فحص المكونات
        print("\n🔍 فحص مكونات قسم الإجازات:")
        
        # فحص البيانات
        print(f"   👥 عدد الموظفين: {len(leave_dept.employees_data)}")
        print(f"   🏖️ عدد الإجازات: {len(leave_dept.leaves_data)}")
        print(f"   💰 عدد الأرصدة: {len(leave_dept.balance_data)}")
        
        # فحص الدوال الرئيسية
        main_functions = [
            ("get_employee_type", "تحديد نوع الموظف"),
            ("calculate_working_days", "حساب أيام العمل"),
            ("get_employee_balance", "الحصول على الرصيد"),
            ("submit_leave_request", "تقديم طلب إجازة"),
            ("approve_selected_leave", "الموافقة على الإجازة"),
            ("reject_selected_leave", "رفض الإجازة"),
            ("check_leave_alerts", "فحص التنبيهات"),
            ("generate_comprehensive_report", "التقرير الشامل"),
            ("show_leave_department", "عرض نافذة القسم")
        ]
        
        for func_name, description in main_functions:
            if hasattr(leave_dept, func_name):
                print(f"   ✅ {description}: موجودة")
            else:
                print(f"   ❌ {description}: غير موجودة")
        
        # فحص دالة فتح القسم في النظام الرئيسي
        print(f"\n🔍 فحص التكامل مع النظام الرئيسي:")
        if hasattr(hr_main, 'open_leave_department'):
            print(f"   ✅ دالة فتح قسم الإجازات: موجودة")
        else:
            print(f"   ❌ دالة فتح قسم الإجازات: غير موجودة")
        
        # إنشاء واجهة تفاعلية للاختبار
        create_integrated_test_interface(root, hr_main, leave_dept)
        
        print("\n🎉 انتهى الاختبار - الواجهة التفاعلية جاهزة")
        print("\n📋 تعليمات الاختبار:")
        print("   • اختبر فتح قسم الإجازات من النظام الرئيسي")
        print("   • جرب جميع وظائف القسم")
        print("   • تأكد من التكامل السليم")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def create_integrated_test_interface(root, hr_main, leave_dept):
    """إنشاء واجهة تفاعلية لاختبار التكامل"""
    
    # إطار الاختبار
    test_frame = tk.Frame(root, bg="#e8f4fd")
    test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
    
    # عنوان
    title_label = tk.Label(test_frame, text="🔧 اختبار قسم الإجازات المدمج", 
                          bg="#e8f4fd", fg="#1565c0", font=("Arial", 14, "bold"))
    title_label.pack(pady=5)
    
    # معلومات النظام
    info_frame = tk.LabelFrame(test_frame, text="معلومات النظام", 
                              bg="#e8f4fd", fg="#1565c0", font=("Arial", 12, "bold"))
    info_frame.pack(fill=tk.X, pady=5)
    
    # إحصائيات
    stats_text = f"""
🏢 قسم الإجازات المدمج:

📊 البيانات المحملة:
• عدد الموظفين: {len(leave_dept.employees_data)}
• عدد الإجازات المسجلة: {len(leave_dept.leaves_data)}
• عدد أرصدة الإجازات: {len(leave_dept.balance_data)}

🔧 المميزات الرئيسية:
• تصنيف المستخدمين (موظف/معلم)
• حساب أيام العمل الفعلية
• خصم تلقائي للموظفين
• منح يدوي للمعلمين
• تنبيهات قبل انتهاء الإجازة
• تقارير شاملة ومفصلة

🔗 التكامل مع النظام الرئيسي:
• دمج كامل مع واجهة النظام
• إدارة النوافذ المتقدمة
• نظام صلاحيات موحد
    """
    
    stats_label = tk.Label(info_frame, text=stats_text, bg="#e8f4fd", 
                          fg="#1565c0", font=("Arial", 10), justify=tk.LEFT)
    stats_label.pack(padx=10, pady=5)
    
    # متغير لعرض النتائج
    result_var = tk.StringVar()
    result_label = tk.Label(test_frame, textvariable=result_var, bg="#e8f4fd", 
                           fg="#1565c0", font=("Arial", 11, "bold"))
    result_label.pack(pady=5)
    
    # أزرار الاختبار
    buttons_frame = tk.Frame(test_frame, bg="#e8f4fd")
    buttons_frame.pack(pady=10)
    
    def test_department_opening():
        """اختبار فتح قسم الإجازات"""
        try:
            print("\n🏖️ اختبار فتح قسم الإجازات...")
            
            # محاولة فتح القسم
            hr_main.open_leave_department()
            
            result_var.set("✅ تم فتح قسم الإجازات بنجاح")
            print("   ✅ تم فتح قسم الإجازات بنجاح")
            
        except Exception as e:
            print(f"   ❌ خطأ في فتح قسم الإجازات: {e}")
            result_var.set(f"❌ خطأ في فتح القسم: {e}")
    
    def test_department_functions():
        """اختبار وظائف القسم"""
        try:
            print("\n🔧 اختبار وظائف قسم الإجازات...")
            
            # اختبار تصنيف الموظفين
            if leave_dept.employees_data:
                emp = leave_dept.employees_data[0]
                emp_id = emp.get("الرقم الوظيفي")
                emp_type = leave_dept.get_employee_type(emp_id)
                print(f"   📊 نوع الموظف {emp_id}: {emp_type}")
            
            # اختبار حساب أيام العمل
            working_days = leave_dept.calculate_working_days("2024-12-15", "2024-12-21")
            print(f"   📅 أيام العمل (15-21 ديسمبر): {working_days} يوم")
            
            # اختبار التنبيهات
            alerts = leave_dept.check_leave_alerts()
            print(f"   ⚠️ عدد التنبيهات: {len(alerts)}")
            
            result_var.set(f"✅ تم اختبار الوظائف - {working_days} أيام عمل، {len(alerts)} تنبيه")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار الوظائف: {e}")
            result_var.set(f"❌ خطأ في الوظائف: {e}")
    
    def test_integration():
        """اختبار التكامل مع النظام الرئيسي"""
        try:
            print("\n🔗 اختبار التكامل مع النظام الرئيسي...")
            
            # فحص وجود الدوال المطلوبة
            integration_checks = [
                ("open_leave_department", "دالة فتح قسم الإجازات"),
                ("check_user_permission", "فحص الصلاحيات"),
                ("open_windows", "إدارة النوافذ"),
                ("current_user", "معلومات المستخدم الحالي")
            ]
            
            passed_checks = 0
            for attr_name, description in integration_checks:
                if hasattr(hr_main, attr_name):
                    print(f"     ✅ {description}: موجود")
                    passed_checks += 1
                else:
                    print(f"     ❌ {description}: غير موجود")
            
            result_var.set(f"🔗 التكامل: {passed_checks}/{len(integration_checks)} فحوصات نجحت")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار التكامل: {e}")
            result_var.set(f"❌ خطأ التكامل: {e}")
    
    def test_data_consistency():
        """اختبار تناسق البيانات"""
        try:
            print("\n📊 اختبار تناسق البيانات...")
            
            # فحص تناسق بيانات الموظفين
            employees_count = len(leave_dept.employees_data)
            balance_count = len(leave_dept.balance_data)
            
            print(f"   👥 عدد الموظفين: {employees_count}")
            print(f"   💰 عدد الأرصدة: {balance_count}")
            
            # فحص تطابق الأرقام الوظيفية
            emp_ids = set(emp.get("الرقم الوظيفي") for emp in leave_dept.employees_data)
            balance_ids = set(balance.get("الرقم الوظيفي") for balance in leave_dept.balance_data)
            
            matching_ids = len(emp_ids.intersection(balance_ids))
            
            result_var.set(f"📊 تناسق البيانات: {matching_ids} موظف متطابق من {employees_count}")
            print(f"   🔍 الموظفين المتطابقين: {matching_ids}")
            
        except Exception as e:
            print(f"   ❌ خطأ في فحص تناسق البيانات: {e}")
            result_var.set(f"❌ خطأ تناسق البيانات: {e}")
    
    def show_department_features():
        """عرض مميزات قسم الإجازات"""
        features = """
🏖️ مميزات قسم الإجازات المدمج:

🔧 التصنيف الذكي:
   • تحديد نوع الموظف تلقائياً (موظف/معلم)
   • معاملة مختلفة حسب النوع

📅 حساب أيام العمل:
   • استثناء الجمعة والسبت تلقائياً
   • حساب دقيق للفترات

🔄 سير العمل المتقدم:
   • حالات: في الانتظار → موافق/مرفوض
   • خصم تلقائي للموظفين
   • منح يدوي للمعلمين

⚠️ نظام التنبيهات:
   • تنبيه قبل انتهاء إجازة المعلمين
   • مراقبة الأرصدة المنخفضة

📊 التقارير الشاملة:
   • تقرير شامل لجميع الإجازات
   • تقرير خاص بكل موظف
   • إحصائيات مفصلة

🔗 التكامل الكامل:
   • دمج مع النظام الرئيسي
   • نظام صلاحيات موحد
   • إدارة نوافذ متقدمة

🎯 النتيجة: قسم متكامل وشامل!
        """
        
        messagebox.showinfo("مميزات قسم الإجازات المدمج", features)
        result_var.set("📋 تم عرض مميزات قسم الإجازات")
    
    def show_usage_guide():
        """عرض دليل الاستخدام"""
        guide = """
📖 دليل استخدام قسم الإجازات المدمج:

🚀 الوصول للقسم:
   1. افتح النظام الرئيسي
   2. اضغط على "🏖️ قسم الإجازات"
   3. ستفتح نافذة القسم المتكاملة

📝 تقديم طلب إجازة:
   1. اختر تبويب "طلب إجازة"
   2. حدد الموظف من القائمة
   3. اختر نوع الإجازة والتواريخ
   4. اضغط "حساب الأيام" للتحقق
   5. أدخل سبب الإجازة
   6. اضغط "تقديم الطلب"

✅ الموافقة على الإجازات:
   1. اختر تبويب "الموافقة على الإجازات"
   2. راجع الطلبات المعلقة
   3. حدد الطلب المطلوب
   4. اضغط "موافقة" أو "رفض"

📊 التقارير:
   1. اختر تبويب "التقارير"
   2. اختر نوع التقرير المطلوب
   3. راجع المعاينة
   4. اضغط على التقرير لتصديره

⚙️ الإعدادات:
   1. اختر تبويب "الإعدادات"
   2. عدل أيام العطل الأسبوعية
   3. اضبط فترة التنبيهات
   4. احفظ التغييرات

🎯 نصائح:
   • استخدم النظام من الواجهة الرئيسية
   • راجع التنبيهات دورياً
   • استفد من التقارير المفصلة
        """
        
        messagebox.showinfo("دليل الاستخدام", guide)
        result_var.set("📖 تم عرض دليل الاستخدام")
    
    # أزرار الاختبار
    tk.Label(buttons_frame, text="اختبارات التكامل:", bg="#e8f4fd", 
            font=("Arial", 12, "bold")).pack()
    
    buttons_row1 = tk.Frame(buttons_frame, bg="#e8f4fd")
    buttons_row1.pack(pady=5)
    
    tk.Button(buttons_row1, text="🏖️ فتح القسم",
             command=test_department_opening,
             bg="#2196f3", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row1, text="🔧 اختبار الوظائف",
             command=test_department_functions,
             bg="#4caf50", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row1, text="🔗 اختبار التكامل",
             command=test_integration,
             bg="#ff9800", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    buttons_row2 = tk.Frame(buttons_frame, bg="#e8f4fd")
    buttons_row2.pack(pady=5)
    
    tk.Button(buttons_row2, text="📊 تناسق البيانات",
             command=test_data_consistency,
             bg="#9c27b0", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row2, text="🏢 مميزات القسم",
             command=show_department_features,
             bg="#795548", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row2, text="📖 دليل الاستخدام",
             command=show_usage_guide,
             bg="#455a64", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    # معلومات إضافية
    info_frame = tk.Frame(test_frame, bg="#e8f4fd")
    info_frame.pack(pady=10)
    
    info_text = """
💡 ملاحظات مهمة:
• قسم الإجازات مدمج بالكامل مع النظام الرئيسي
• يدعم جميع أنواع المستخدمين والصلاحيات
• واجهة موحدة ومتسقة مع باقي النظام
• إدارة متقدمة للنوافذ والجلسات
    """
    info_label = tk.Label(info_frame, text=info_text, bg="#e8f4fd", 
                         fg="#1565c0", font=("Arial", 10), justify=tk.LEFT)
    info_label.pack()
    
    # زر إغلاق
    tk.Button(test_frame, text="❌ إغلاق الاختبار", 
             command=root.destroy,
             bg="#616161", fg="white", 
             font=("Arial", 12, "bold")).pack(pady=15)

def main():
    """الدالة الرئيسية"""
    print("🏢 اختبار قسم الإجازات المدمج")
    print("=" * 60)
    
    test_integrated_leave_department()

if __name__ == "__main__":
    main()
