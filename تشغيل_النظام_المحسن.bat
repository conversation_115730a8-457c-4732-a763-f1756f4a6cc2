@echo off
chcp 65001 > nul
title نظام إدارة الموارد البشرية - النسخة المحسنة والمنظمة

echo.
echo ===============================================
echo    🏢 نظام إدارة الموارد البشرية
echo         النسخة المحسنة والمنظمة
echo ===============================================
echo.

echo 🔧 التحسينات المطبقة:
echo.
echo ✅ نظام التحقق من صحة البيانات المتقدم
echo ✅ محرك الحسابات الدقيق
echo ✅ نظام إدارة الأخطاء والسجلات
echo ✅ مراقب الأداء والذاكرة
echo ✅ النسخ الاحتياطية التلقائية
echo ✅ مراقب النظام الشامل
echo ✅ تحسين الذاكرة والملفات
echo.

echo 🎯 الميزات الرئيسية:
echo    👥 إدارة الموظفين مع قائمة منسدلة للمسمى الوظيفي
echo    🏖️ نظام الإجازات للموظفين والموظفات فقط
echo    💰 نظام رصيد الإجازات الذكي والمحسن
echo    ⬆️ نظام الترقيات
echo    📊 التقارير والإحصائيات
echo    🎨 مدير قوالب المراسلات
echo    🔐 إدارة المستخدمين
echo    🔍 مراقب النظام الشامل
echo.

echo 🔐 بيانات تسجيل الدخول:
echo    👤 المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.

echo 🔍 فحص المكتبات المطلوبة...
python -c "
try:
    import openpyxl, docx, psutil, tkcalendar
    print('✅ جميع المكتبات مثبتة ومتاحة')
except ImportError as e:
    print(f'⚠️ مكتبة مفقودة: {e}')
    print('💡 يرجى تشغيل install_requirements.bat أولاً')
    input('اضغط Enter للمتابعة...')
"

echo.
echo 🧪 اختبار النظام المحسن...
python test_enhanced_system.py

echo.
echo 🚀 بدء تشغيل النظام المحسن...
python hr_system.py

echo.
echo 📝 انتهى تشغيل النظام
echo.
echo 💡 للمساعدة راجع:
echo    📖 README.md - الدليل الرئيسي
echo    📋 دليل_القائمة_المنسدلة.md - دليل القائمة المنسدلة
echo    📊 تقرير_تأكيد_التحسينات.md - تقرير التحسينات
echo    🔍 system_monitor.py - مراقب النظام
echo.
echo اضغط أي مفتاح للخروج...
pause > nul
