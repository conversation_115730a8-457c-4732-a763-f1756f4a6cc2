#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار حذف أزرار تنظيف وفحص النص
Test Removal of Text Cleaning and Debug Buttons
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_buttons_removal():
    """اختبار حذف الأزرار"""
    print("🗑️ اختبار حذف أزرار تنظيف وفحص النص")
    print("=" * 60)
    
    try:
        # استيراد النظام
        print("📦 استيراد نظام إدارة الموظفين...")
        import employee_management
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة الاختبار...")
        root = tk.Tk()
        root.title("اختبار حذف الأزرار")
        root.geometry("1200x800")
        
        # إنشاء النظام
        print("🔧 إنشاء نظام إدارة الموظفين...")
        current_user = {"username": "button_tester", "name": "مختبر الأزرار"}
        emp_system = employee_management.EmployeeManagementSystem(root, current_user)
        print("✅ تم إنشاء النظام بنجاح")
        
        # فحص الدوال المحذوفة
        print("\n🔍 فحص الدوال المحذوفة:")
        
        # فحص دالة فحص النص
        if hasattr(emp_system, 'debug_search_text'):
            print("❌ دالة فحص النص ما زالت موجودة!")
        else:
            print("✅ تم حذف دالة فحص النص بنجاح")
        
        # فحص دالة تنظيف النص
        if hasattr(emp_system, 'clean_search_text'):
            print("❌ دالة تنظيف النص ما زالت موجودة!")
        else:
            print("✅ تم حذف دالة تنظيف النص بنجاح")
        
        # فحص الأزرار في الواجهة
        print("\n🔍 فحص الأزرار في الواجهة:")
        
        def find_buttons_with_text(widget, target_texts):
            """البحث عن أزرار تحتوي على نصوص محددة"""
            found_buttons = []
            
            if isinstance(widget, tk.Button):
                button_text = widget.cget("text")
                for target in target_texts:
                    if target in button_text:
                        found_buttons.append((widget, button_text))
            
            for child in widget.winfo_children():
                found_buttons.extend(find_buttons_with_text(child, target_texts))
            
            return found_buttons
        
        # البحث عن الأزرار المحذوفة
        target_texts = ["فحص النص", "تنظيف النص"]
        found_buttons = find_buttons_with_text(root, target_texts)
        
        if found_buttons:
            print("❌ تم العثور على أزرار لم يتم حذفها:")
            for button, text in found_buttons:
                print(f"   - زر: '{text}'")
        else:
            print("✅ تم حذف جميع الأزرار المطلوبة بنجاح")
        
        # فحص الأزرار الموجودة
        print("\n🔍 الأزرار الموجودة حالياً:")
        all_buttons = find_buttons_with_text(root, [""])
        
        search_buttons = []
        for button, text in all_buttons:
            if any(keyword in text for keyword in ["بحث", "🔍", "تصفية", "إعادة"]):
                search_buttons.append(text)
        
        if search_buttons:
            print("✅ الأزرار المتبقية في منطقة البحث:")
            for i, button_text in enumerate(search_buttons, 1):
                print(f"   {i}. {button_text}")
        else:
            print("⚠️ لم يتم العثور على أزرار البحث")
        
        # إنشاء واجهة تفاعلية للاختبار
        create_test_interface(root, emp_system)
        
        print("\n🎉 انتهى الاختبار - الواجهة التفاعلية جاهزة")
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def create_test_interface(root, emp_system):
    """إنشاء واجهة تفاعلية للاختبار"""
    
    # إطار الاختبار
    test_frame = tk.Frame(root, bg="#f8d7da")
    test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
    
    # عنوان
    title_label = tk.Label(test_frame, text="🗑️ اختبار حذف أزرار تنظيف وفحص النص", 
                          bg="#f8d7da", fg="#721c24", font=("Arial", 14, "bold"))
    title_label.pack(pady=5)
    
    # تعليمات
    instructions_text = """
✅ تم حذف الأزرار التالية:
• زر "🔍 فحص النص" - كان يعرض تفاصيل النص
• زر "🧹 تنظيف النص" - كان ينظف المسافات الزائدة

الأزرار المتبقية:
• زر "🔍 بحث" - للبحث المباشر
• أزرار التصفية والإعادة تعيين
    """
    instructions_label = tk.Label(test_frame, text=instructions_text, bg="#f8d7da", 
                                 fg="#721c24", font=("Arial", 10), justify=tk.LEFT)
    instructions_label.pack(pady=5)
    
    # متغير لعرض النتائج
    result_var = tk.StringVar()
    result_label = tk.Label(test_frame, textvariable=result_var, bg="#f8d7da", 
                           fg="#721c24", font=("Arial", 10, "bold"))
    result_label.pack(pady=5)
    
    # أزرار الاختبار
    buttons_frame = tk.Frame(test_frame, bg="#f8d7da")
    buttons_frame.pack(pady=10)
    
    def test_search_functionality():
        """اختبار وظيفة البحث المتبقية"""
        print("\n🔍 اختبار وظيفة البحث المتبقية:")
        
        # تعيين نص للبحث
        test_text = "أحمد"
        emp_system.search_var.set(test_text)
        print(f"   تم تعيين النص: '{test_text}'")
        
        try:
            # تشغيل البحث
            emp_system.force_search()
            result_var.set("✅ البحث يعمل بشكل طبيعي")
            print("   ✅ البحث يعمل بشكل طبيعي")
        except Exception as e:
            result_var.set(f"❌ خطأ في البحث: {e}")
            print(f"   ❌ خطأ في البحث: {e}")
    
    def test_removed_functions():
        """اختبار الدوال المحذوفة"""
        print("\n🔍 اختبار الدوال المحذوفة:")
        
        results = []
        
        # اختبار دالة فحص النص
        if hasattr(emp_system, 'debug_search_text'):
            results.append("❌ دالة فحص النص موجودة")
            print("   ❌ دالة فحص النص موجودة")
        else:
            results.append("✅ دالة فحص النص محذوفة")
            print("   ✅ دالة فحص النص محذوفة")
        
        # اختبار دالة تنظيف النص
        if hasattr(emp_system, 'clean_search_text'):
            results.append("❌ دالة تنظيف النص موجودة")
            print("   ❌ دالة تنظيف النص موجودة")
        else:
            results.append("✅ دالة تنظيف النص محذوفة")
            print("   ✅ دالة تنظيف النص محذوفة")
        
        result_var.set(" | ".join(results))
    
    def show_remaining_buttons():
        """عرض الأزرار المتبقية"""
        print("\n🔍 الأزرار المتبقية:")
        
        def find_all_buttons(widget):
            buttons = []
            if isinstance(widget, tk.Button):
                button_text = widget.cget("text")
                if button_text.strip():  # تجاهل الأزرار الفارغة
                    buttons.append(button_text)
            
            for child in widget.winfo_children():
                buttons.extend(find_all_buttons(child))
            
            return buttons
        
        all_buttons = find_all_buttons(root)
        search_related = [btn for btn in all_buttons if any(keyword in btn for keyword in ["بحث", "🔍", "تصفية", "إعادة", "🎛️", "🔄"])]
        
        if search_related:
            print("   الأزرار المتعلقة بالبحث:")
            for i, btn in enumerate(search_related, 1):
                print(f"     {i}. {btn}")
            result_var.set(f"وجد {len(search_related)} أزرار متعلقة بالبحث")
        else:
            print("   لم يتم العثور على أزرار البحث")
            result_var.set("لم يتم العثور على أزرار البحث")
    
    # أزرار الاختبار
    tk.Label(buttons_frame, text="اختبارات:", bg="#f8d7da", 
            font=("Arial", 10, "bold")).pack(side=tk.LEFT)
    
    tk.Button(buttons_frame, text="🔍 اختبار البحث",
             command=test_search_functionality,
             bg="#28a745", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="🗑️ فحص الحذف",
             command=test_removed_functions,
             bg="#dc3545", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="📋 الأزرار المتبقية",
             command=show_remaining_buttons,
             bg="#6c757d", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    # معلومات إضافية
    info_frame = tk.Frame(test_frame, bg="#f8d7da")
    info_frame.pack(pady=5)
    
    info_text = """
💡 ملاحظة: تم حذف أزرار "فحص النص" و"تنظيف النص" فقط
🎯 النتيجة: البحث الآن أبسط ومباشر أكثر
    """
    info_label = tk.Label(info_frame, text=info_text, bg="#f8d7da", 
                         fg="#721c24", font=("Arial", 9), justify=tk.LEFT)
    info_label.pack()
    
    # زر إغلاق
    tk.Button(test_frame, text="❌ إغلاق الاختبار", 
             command=root.destroy,
             bg="#6c757d", fg="white", 
             font=("Arial", 12, "bold")).pack(pady=10)

def main():
    """الدالة الرئيسية"""
    print("🗑️ اختبار حذف أزرار تنظيف وفحص النص")
    print("=" * 60)
    
    test_buttons_removal()

if __name__ == "__main__":
    main()
