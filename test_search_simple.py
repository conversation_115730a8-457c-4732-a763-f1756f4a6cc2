#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط لميزة البحث
Simple Search Test
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

def test_search_manually():
    """اختبار البحث يدوياً"""
    print("🔍 اختبار البحث اليدوي")
    print("=" * 50)
    
    # محاكاة بيانات الموظفين
    employees_data = [
        {
            "الرقم الوظيفي": "001",
            "الاسم العربي": "أحمد محمد علي",
            "الاسم الإنجليزي": "<PERSON>",
            "المسمى الوظيفي": "موظف",
            "الرقم الوطني": "123456789",
            "المؤهل": "بكالوريوس",
            "مكان العمل الحالي": "الإدارة العامة",
            "التخصص": "إدارة أعمال",
            "الجنسية": "ليبي"
        },
        {
            "الرقم الوظيفي": "002",
            "الاسم العربي": "فاطمة أحمد سالم",
            "الاسم الإنجليزي": "Fatima Ahmed Salem",
            "المسمى الوظيفي": "موظفة",
            "الرقم الوطني": "987654321",
            "المؤهل": "ماجستير",
            "مكان العمل الحالي": "إدارة الموارد البشرية",
            "التخصص": "موارد بشرية",
            "الجنسية": "ليبي"
        },
        {
            "الرقم الوظيفي": "003",
            "الاسم العربي": "محمد سالم أحمد",
            "الاسم الإنجليزي": "Mohamed Salem Ahmed",
            "المسمى الوظيفي": "معلم",
            "الرقم الوطني": "456789123",
            "المؤهل": "بكالوريوس",
            "مكان العمل الحالي": "المدرسة الابتدائية",
            "التخصص": "رياضيات",
            "الجنسية": "ليبي"
        }
    ]
    
    def perform_search(search_term, employees_data):
        """تنفيذ البحث"""
        search_term = search_term.strip().lower()
        
        if not search_term:
            return employees_data
        
        # حقول البحث
        search_fields = ["الرقم الوظيفي", "الاسم العربي", "الاسم الإنجليزي", "المسمى الوظيفي", "الرقم الوطني"]
        additional_fields = ["المؤهل", "مكان العمل الحالي", "التخصص", "الجنسية"]
        all_fields = search_fields + additional_fields
        
        filtered = []
        for emp in employees_data:
            found = False
            for field in all_fields:
                value = str(emp.get(field, "")).lower()
                if search_term in value:
                    filtered.append(emp)
                    found = True
                    print(f"✅ وجد '{search_term}' في {field}: {emp.get('الاسم العربي', 'غير محدد')}")
                    break
        
        return filtered
    
    # اختبارات مختلفة
    test_cases = [
        "أحمد",
        "محمد", 
        "001",
        "موظف",
        "بكالوريوس",
        "الإدارة",
        "ليبي",
        "غير موجود"
    ]
    
    print(f"📊 إجمالي الموظفين: {len(employees_data)}")
    print()
    
    for test_term in test_cases:
        print(f"🔍 البحث عن: '{test_term}'")
        results = perform_search(test_term, employees_data)
        print(f"📊 النتائج: {len(results)} من أصل {len(employees_data)}")
        
        if results:
            for emp in results:
                print(f"   - {emp['الاسم العربي']} ({emp['الرقم الوظيفي']})")
        else:
            print("   لا توجد نتائج")
        print("-" * 30)

def create_test_gui():
    """إنشاء واجهة اختبار"""
    root = tk.Tk()
    root.title("اختبار البحث")
    root.geometry("800x600")
    
    # بيانات تجريبية
    employees_data = [
        {"الرقم الوظيفي": "001", "الاسم العربي": "أحمد محمد علي", "المسمى الوظيفي": "موظف"},
        {"الرقم الوظيفي": "002", "الاسم العربي": "فاطمة أحمد سالم", "المسمى الوظيفي": "موظفة"},
        {"الرقم الوظيفي": "003", "الاسم العربي": "محمد سالم أحمد", "المسمى الوظيفي": "معلم"},
        {"الرقم الوظيفي": "004", "الاسم العربي": "سارة علي محمد", "المسمى الوظيفي": "معلمة"},
        {"الرقم الوظيفي": "005", "الاسم العربي": "خالد أحمد علي", "المسمى الوظيفي": "موظف"}
    ]
    
    filtered_data = []
    
    # إطار البحث
    search_frame = tk.LabelFrame(root, text="🔍 البحث", font=("Arial", 12, "bold"))
    search_frame.pack(fill=tk.X, padx=10, pady=5)
    
    search_var = tk.StringVar()
    
    tk.Label(search_frame, text="البحث:", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    search_entry = tk.Entry(search_frame, textvariable=search_var, font=("Arial", 10), width=30)
    search_entry.pack(side=tk.LEFT, padx=5)
    
    # إطار النتائج
    results_frame = tk.LabelFrame(root, text="📊 النتائج", font=("Arial", 12, "bold"))
    results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
    
    # جدول النتائج
    columns = ("الرقم الوظيفي", "الاسم العربي", "المسمى الوظيفي")
    tree = ttk.Treeview(results_frame, columns=columns, show="headings", height=15)
    
    for col in columns:
        tree.heading(col, text=col)
        tree.column(col, width=200)
    
    tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    # تسمية النتائج
    results_label = tk.Label(results_frame, text="عدد النتائج: 0", font=("Arial", 10, "bold"))
    results_label.pack(pady=5)
    
    def update_table():
        """تحديث الجدول"""
        # مسح البيانات الحالية
        for item in tree.get_children():
            tree.delete(item)
        
        # تحديد البيانات المراد عرضها
        data_to_show = filtered_data if filtered_data else employees_data
        
        # إضافة البيانات
        for emp in data_to_show:
            values = (
                emp.get("الرقم الوظيفي", ""),
                emp.get("الاسم العربي", ""),
                emp.get("المسمى الوظيفي", "")
            )
            tree.insert("", tk.END, values=values)
        
        # تحديث العداد
        results_label.config(text=f"عدد النتائج: {len(data_to_show)}")
    
    def perform_search():
        """تنفيذ البحث"""
        nonlocal filtered_data
        
        search_term = search_var.get().strip().lower()
        print(f"🔍 البحث عن: '{search_term}'")
        
        if not search_term:
            filtered_data = []
            update_table()
            return
        
        # البحث
        search_fields = ["الرقم الوظيفي", "الاسم العربي", "المسمى الوظيفي"]
        
        filtered = []
        for emp in employees_data:
            for field in search_fields:
                value = str(emp.get(field, "")).lower()
                if search_term in value:
                    filtered.append(emp)
                    print(f"✅ وجد في {field}: {emp.get('الاسم العربي', 'غير محدد')}")
                    break
        
        filtered_data = filtered
        update_table()
        print(f"📊 النتائج: {len(filtered)} من أصل {len(employees_data)}")
    
    def reset_search():
        """إعادة تعيين البحث"""
        nonlocal filtered_data
        search_var.set("")
        filtered_data = []
        update_table()
        print("🔄 تم إعادة تعيين البحث")
    
    # ربط الأحداث
    search_var.trace('w', lambda *args: perform_search())
    
    # أزرار
    buttons_frame = tk.Frame(search_frame)
    buttons_frame.pack(side=tk.RIGHT, padx=5)
    
    tk.Button(buttons_frame, text="🔍 بحث", command=perform_search,
              bg="#3498db", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(buttons_frame, text="🔄 إعادة تعيين", command=reset_search,
              bg="#95a5a6", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    # عرض البيانات الأولية
    update_table()
    
    root.mainloop()

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار ميزة البحث")
    print("=" * 50)
    
    # اختبار يدوي
    test_search_manually()
    
    print("\n" + "=" * 50)
    print("🖥️ فتح واجهة الاختبار...")
    
    # اختبار بالواجهة
    create_test_gui()

if __name__ == "__main__":
    main()
