from reference_data import get_bank_names, get_nationalities, get_qualifications, get_work_places
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المستخدمين
يتيح للمشرف العام إدارة المستخدمين والصلاحيات
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from datetime import datetime
import hashlib

class UserManagement:
    def __init__(self, parent, current_user):
        print(f"🔐 تهيئة نظام إدارة المستخدمين للمستخدم: {current_user}")

        self.parent = parent
        self.current_user = current_user
        self.users_file = "users.json"

        try:
            # التحقق من صلاحيات المستخدم الحالي
            print("🔍 فحص صلاحيات المستخدم...")
            if not self.check_admin_permissions():
                print(f"❌ المستخدم {current_user} ليس لديه صلاحيات إدارة المستخدمين")
                messagebox.showerror("خطأ في الصلاحيات",
                                   f"ليس لديك صلاحية للوصول لإدارة المستخدمين\n"
                                   f"المستخدم الحالي: {current_user}\n"
                                   f"مطلوب: مشرف عام (super_admin)")
                return

            print("✅ تم التحقق من الصلاحيات بنجاح")

            # إعداد واجهة المستخدم
            print("🎨 إعداد واجهة المستخدم...")
            self.setup_ui()

            # تحميل المستخدمين
            print("📋 تحميل قائمة المستخدمين...")
            self.load_users()

            print("🎉 تم تهيئة نظام إدارة المستخدمين بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تهيئة نظام إدارة المستخدمين: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("خطأ", f"فشل في تهيئة نظام إدارة المستخدمين:\n{str(e)}")
            raise
    
    def check_admin_permissions(self):
        """التحقق من صلاحيات المشرف"""
        try:
            print(f"🔍 فحص صلاحيات المستخدم: {self.current_user}")

            # استخراج اسم المستخدم من القاموس أو النص
            if isinstance(self.current_user, dict):
                username = self.current_user.get('username', '')
                current_role = self.current_user.get('role', '')
                print(f"👤 اسم المستخدم من القاموس: {username}")
                print(f"🎭 الدور الحالي: {current_role}")
            else:
                username = str(self.current_user)
                print(f"👤 اسم المستخدم: {username}")

            users_data = self.load_users_data()
            if not users_data:
                print("❌ لا توجد بيانات مستخدمين")
                return False

            print(f"📋 تم تحميل بيانات {len(users_data)} مستخدم")
            print(f"📋 المستخدمون المتاحون: {list(users_data.keys())}")

            if username in users_data:
                user_info = users_data[username]
                user_role = user_info.get('role', '')
                print(f"👤 دور المستخدم {username}: {user_role}")

                is_super_admin = user_role == 'super_admin'
                print(f"🔐 هل هو مشرف عام؟ {is_super_admin}")

                return is_super_admin
            else:
                print(f"❌ المستخدم {username} غير موجود في قاعدة البيانات")
                return False

        except Exception as e:
            print(f"❌ خطأ في فحص الصلاحيات: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def load_users_data(self):
        """تحميل بيانات المستخدمين"""
        try:
            if os.path.exists(self.users_file):
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"خطأ في تحميل بيانات المستخدمين: {e}")
            return {}
    
    def save_users_data(self, users_data):
        """حفظ بيانات المستخدمين"""
        try:
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump(users_data, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"خطأ في حفظ بيانات المستخدمين: {e}")
            return False
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.parent.title("🔐 إدارة المستخدمين - المشرف العام")
        self.parent.geometry("1200x800")
        self.parent.configure(bg="#f0f0f0")
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.parent, bg="#f0f0f0")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = tk.Label(main_frame, text="🔐 إدارة المستخدمين والصلاحيات", 
                              font=("Arial", 18, "bold"), bg="#f0f0f0", fg="#2c3e50")
        title_label.pack(pady=(0, 20))
        
        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg="#f0f0f0")
        buttons_frame.pack(fill=tk.X, pady=(0, 20))
        
        # أزرار الإدارة
        tk.Button(buttons_frame, text="➕ إضافة مستخدم جديد", 
                 command=self.add_user_dialog,
                 bg="#27ae60", fg="white", font=("Arial", 12, "bold"),
                 width=20, height=2).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="✏️ تعديل مستخدم", 
                 command=self.edit_user_dialog,
                 bg="#3498db", fg="white", font=("Arial", 12, "bold"),
                 width=20, height=2).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="🗑️ حذف مستخدم", 
                 command=self.delete_user,
                 bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                 width=20, height=2).pack(side=tk.LEFT, padx=5)
        
        tk.Button(buttons_frame, text="🔄 تحديث القائمة", 
                 command=self.load_users,
                 bg="#95a5a6", fg="white", font=("Arial", 12, "bold"),
                 width=20, height=2).pack(side=tk.LEFT, padx=5)
        
        # جدول المستخدمين
        self.create_users_table(main_frame)
        
        # إطار معلومات المستخدم المختار
        self.create_user_details_frame(main_frame)
    
    def create_users_table(self, parent):
        """إنشاء جدول المستخدمين"""
        # إطار الجدول
        table_frame = tk.LabelFrame(parent, text="📋 قائمة المستخدمين", 
                                   font=("Arial", 14, "bold"), bg="#f0f0f0")
        table_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # إنشاء Treeview
        columns = ("username", "name", "role", "created_date", "last_login", "status")
        self.users_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعريف العناوين
        self.users_tree.heading("username", text="اسم المستخدم")
        self.users_tree.heading("name", text="الاسم الكامل")
        self.users_tree.heading("role", text="الدور")
        self.users_tree.heading("created_date", text="تاريخ الإنشاء")
        self.users_tree.heading("last_login", text="آخر دخول")
        self.users_tree.heading("status", text="الحالة")
        
        # تعريف عرض الأعمدة
        self.users_tree.column("username", width=150)
        self.users_tree.column("name", width=200)
        self.users_tree.column("role", width=150)
        self.users_tree.column("created_date", width=120)
        self.users_tree.column("last_login", width=120)
        self.users_tree.column("status", width=100)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.users_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط حدث الاختيار
        self.users_tree.bind("<<TreeviewSelect>>", self.on_user_select)
    
    def create_user_details_frame(self, parent):
        """إنشاء إطار تفاصيل المستخدم"""
        details_frame = tk.LabelFrame(parent, text="📄 تفاصيل المستخدم المختار",
                                     font=("Arial", 14, "bold"), bg="#f0f0f0")
        details_frame.pack(fill=tk.X)

        # متغيرات النص مع تمرير النافذة الأب
        self.selected_user_info = tk.StringVar(master=self.parent)
        self.selected_user_info.set("لم يتم اختيار مستخدم")
        
        info_label = tk.Label(details_frame, textvariable=self.selected_user_info,
                             font=("Arial", 12), bg="#f0f0f0", fg="#34495e",
                             justify=tk.LEFT, wraplength=1000)
        info_label.pack(padx=20, pady=15, anchor=tk.W)
    
    def load_users(self):
        """تحميل المستخدمين في الجدول"""
        # مسح البيانات الحالية
        for item in self.users_tree.get_children():
            self.users_tree.delete(item)
        
        # تحميل البيانات
        users_data = self.load_users_data()
        
        for username, user_info in users_data.items():
            # تحديد حالة المستخدم
            status = "نشط" if username == self.current_user else "عادي"
            if user_info.get('role') == 'super_admin':
                status = "مشرف عام"
            
            # ترجمة الدور
            role_translation = {
                'super_admin': 'مشرف عام',
                'admin': 'مدير',
                'hr_manager': 'مدير موارد بشرية',
                'employee': 'موظف'
            }
            role_display = role_translation.get(user_info.get('role', ''), user_info.get('role', ''))
            
            # إدراج البيانات
            self.users_tree.insert("", tk.END, values=(
                username,
                user_info.get('name', ''),
                role_display,
                user_info.get('created_date', ''),
                user_info.get('last_login', 'لم يسجل دخول'),
                status
            ))
        
        print(f"✅ تم تحميل {len(users_data)} مستخدم")
    
    def on_user_select(self, event):
        """عند اختيار مستخدم من الجدول"""
        selection = self.users_tree.selection()
        if selection:
            item = self.users_tree.item(selection[0])
            username = item['values'][0]
            
            # تحميل تفاصيل المستخدم
            users_data = self.load_users_data()
            if username in users_data:
                user_info = users_data[username]
                
                # تنسيق المعلومات
                details = f"""
👤 اسم المستخدم: {username}
📝 الاسم الكامل: {user_info.get('name', 'غير محدد')}
🎭 الدور: {user_info.get('role', 'غير محدد')}
📅 تاريخ الإنشاء: {user_info.get('created_date', 'غير محدد')}
🕐 آخر دخول: {user_info.get('last_login', 'لم يسجل دخول')}
👨‍💼 أنشئ بواسطة: {user_info.get('created_by', 'غير محدد')}

🔐 الصلاحيات:"""
                
                # إضافة الصلاحيات إذا كانت موجودة
                permissions = user_info.get('permissions', {})
                if permissions:
                    for perm, value in permissions.items():
                        status = "✅" if value else "❌"
                        perm_name = {
                            'manage_users': 'إدارة المستخدمين',
                            'manage_employees': 'إدارة الموظفين',
                            'manage_leaves': 'إدارة الإجازات',
                            'manage_promotions': 'إدارة الترقيات',
                            'manage_reports': 'إدارة التقارير',
                            'manage_templates': 'إدارة القوالب',
                            'system_settings': 'إعدادات النظام',
                            'full_access': 'وصول كامل'
                        }.get(perm, perm)
                        details += f"\n   {status} {perm_name}"
                else:
                    details += "\n   لا توجد صلاحيات محددة"
                
                self.selected_user_info.set(details.strip())
    
    def add_user_dialog(self):
        """حوار إضافة مستخدم جديد"""
        dialog = tk.Toplevel(self.parent)
        dialog.title("➕ إضافة مستخدم جديد")
        dialog.geometry("500x600")
        dialog.configure(bg="#f0f0f0")
        dialog.grab_set()
        
        # العنوان
        tk.Label(dialog, text="➕ إضافة مستخدم جديد", 
                font=("Arial", 16, "bold"), bg="#f0f0f0").pack(pady=20)
        
        # الحقول
        fields_frame = tk.Frame(dialog, bg="#f0f0f0")
        fields_frame.pack(padx=40, pady=20, fill=tk.BOTH, expand=True)
        
        # اسم المستخدم
        tk.Label(fields_frame, text="اسم المستخدم:", font=("Arial", 12), bg="#f0f0f0").pack(anchor=tk.W)
        username_entry = tk.Entry(fields_frame, font=("Arial", 12), width=30)
        username_entry.pack(pady=(5, 15), fill=tk.X)
        
        # كلمة المرور
        tk.Label(fields_frame, text="كلمة المرور:", font=("Arial", 12), bg="#f0f0f0").pack(anchor=tk.W)
        password_entry = tk.Entry(fields_frame, font=("Arial", 12), width=30, show="*")
        password_entry.pack(pady=(5, 15), fill=tk.X)
        
        # الاسم الكامل
        tk.Label(fields_frame, text="الاسم الكامل:", font=("Arial", 12), bg="#f0f0f0").pack(anchor=tk.W)
        name_entry = tk.Entry(fields_frame, font=("Arial", 12), width=30)
        name_entry.pack(pady=(5, 15), fill=tk.X)
        
        # الدور
        tk.Label(fields_frame, text="الدور:", font=("Arial", 12), bg="#f0f0f0").pack(anchor=tk.W)
        role_var = tk.StringVar(master=dialog, value="employee")
        role_combo = ttk.Combobox(fields_frame, textvariable=role_var, font=("Arial", 12),
                                 values=["employee", "hr_manager", "admin"], state="readonly")
        role_combo.pack(pady=(5, 15), fill=tk.X)
        
        # الصلاحيات
        tk.Label(fields_frame, text="الصلاحيات:", font=("Arial", 12, "bold"), bg="#f0f0f0").pack(anchor=tk.W, pady=(10, 5))
        
        permissions_frame = tk.Frame(fields_frame, bg="#f0f0f0")
        permissions_frame.pack(fill=tk.X, pady=(0, 15))
        
        # متغيرات الصلاحيات
        perm_vars = {}
        permissions_list = [
            ('manage_employees', 'إدارة الموظفين'),
            ('manage_leaves', 'إدارة الإجازات'),
            ('manage_promotions', 'إدارة الترقيات'),
            ('manage_reports', 'إدارة التقارير'),
            ('manage_templates', 'إدارة القوالب')
        ]
        
        for perm_key, perm_name in permissions_list:
            var = tk.BooleanVar(master=dialog)
            perm_vars[perm_key] = var
            tk.Checkbutton(permissions_frame, text=perm_name, variable=var,
                          font=("Arial", 10), bg="#f0f0f0").pack(anchor=tk.W)
        
        # أزرار الحفظ والإلغاء
        buttons_frame = tk.Frame(dialog, bg="#f0f0f0")
        buttons_frame.pack(pady=20)
        
        def save_user():
            username = username_entry.get().strip()
            password = password_entry.get().strip()
            name = name_entry.get().strip()
            role = role_var.get()
            
            if not username or not password or not name:
                messagebox.showerror("خطأ", "جميع الحقول مطلوبة")
                return
            
            # التحقق من عدم وجود المستخدم
            users_data = self.load_users_data()
            if username in users_data:
                messagebox.showerror("خطأ", "اسم المستخدم موجود بالفعل")
                return
            
            # إنشاء بيانات المستخدم الجديد
            permissions = {}
            for perm_key, var in perm_vars.items():
                permissions[perm_key] = var.get()
            
            new_user = {
                'password': password,
                'role': role,
                'name': name,
                'created_date': datetime.now().strftime("%Y-%m-%d"),
                'created_by': self.current_user,
                'last_login': None,
                'permissions': permissions
            }
            
            # حفظ المستخدم الجديد
            users_data[username] = new_user
            if self.save_users_data(users_data):
                messagebox.showinfo("نجح", f"تم إضافة المستخدم '{username}' بنجاح")
                self.load_users()
                dialog.destroy()
            else:
                messagebox.showerror("خطأ", "فشل في حفظ المستخدم الجديد")
        
        tk.Button(buttons_frame, text="💾 حفظ", command=save_user,
                 bg="#27ae60", fg="white", font=("Arial", 12, "bold"),
                 width=15).pack(side=tk.LEFT, padx=10)
        
        tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                 bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                 width=15).pack(side=tk.LEFT, padx=10)
    
    def edit_user_dialog(self):
        """حوار تعديل مستخدم"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "الرجاء اختيار مستخدم للتعديل")
            return
        
        username = self.users_tree.item(selection[0])['values'][0]
        
        # منع تعديل المشرف العام
        if username == self.current_user:
            messagebox.showwarning("تحذير", "لا يمكن تعديل بيانات المشرف العام")
            return
        
        users_data = self.load_users_data()
        if username not in users_data:
            messagebox.showerror("خطأ", "المستخدم غير موجود")
            return
        
        user_info = users_data[username]
        
        # إنشاء حوار التعديل (مشابه لحوار الإضافة مع ملء البيانات الحالية)
        dialog = tk.Toplevel(self.parent)
        dialog.title(f"✏️ تعديل المستخدم: {username}")
        dialog.geometry("500x600")
        dialog.configure(bg="#f0f0f0")
        dialog.grab_set()
        
        # العنوان
        tk.Label(dialog, text=f"✏️ تعديل المستخدم: {username}", 
                font=("Arial", 16, "bold"), bg="#f0f0f0").pack(pady=20)
        
        # الحقول (مع ملء البيانات الحالية)
        fields_frame = tk.Frame(dialog, bg="#f0f0f0")
        fields_frame.pack(padx=40, pady=20, fill=tk.BOTH, expand=True)
        
        # كلمة المرور الجديدة
        tk.Label(fields_frame, text="كلمة المرور الجديدة (اتركها فارغة للاحتفاظ بالحالية):", 
                font=("Arial", 12), bg="#f0f0f0").pack(anchor=tk.W)
        password_entry = tk.Entry(fields_frame, font=("Arial", 12), width=30, show="*")
        password_entry.pack(pady=(5, 15), fill=tk.X)
        
        # الاسم الكامل
        tk.Label(fields_frame, text="الاسم الكامل:", font=("Arial", 12), bg="#f0f0f0").pack(anchor=tk.W)
        name_entry = tk.Entry(fields_frame, font=("Arial", 12), width=30)
        name_entry.insert(0, user_info.get('name', ''))
        name_entry.pack(pady=(5, 15), fill=tk.X)
        
        # الدور
        tk.Label(fields_frame, text="الدور:", font=("Arial", 12), bg="#f0f0f0").pack(anchor=tk.W)
        role_var = tk.StringVar(master=dialog, value=user_info.get('role', 'employee'))
        role_combo = ttk.Combobox(fields_frame, textvariable=role_var, font=("Arial", 12),
                                 values=["employee", "hr_manager", "admin"], state="readonly")
        role_combo.pack(pady=(5, 15), fill=tk.X)
        
        # الصلاحيات
        tk.Label(fields_frame, text="الصلاحيات:", font=("Arial", 12, "bold"), bg="#f0f0f0").pack(anchor=tk.W, pady=(10, 5))
        
        permissions_frame = tk.Frame(fields_frame, bg="#f0f0f0")
        permissions_frame.pack(fill=tk.X, pady=(0, 15))
        
        # متغيرات الصلاحيات مع القيم الحالية
        perm_vars = {}
        permissions_list = [
            ('manage_employees', 'إدارة الموظفين'),
            ('manage_leaves', 'إدارة الإجازات'),
            ('manage_promotions', 'إدارة الترقيات'),
            ('manage_reports', 'إدارة التقارير'),
            ('manage_templates', 'إدارة القوالب')
        ]
        
        current_permissions = user_info.get('permissions', {})
        for perm_key, perm_name in permissions_list:
            var = tk.BooleanVar(master=dialog, value=current_permissions.get(perm_key, False))
            perm_vars[perm_key] = var
            tk.Checkbutton(permissions_frame, text=perm_name, variable=var,
                          font=("Arial", 10), bg="#f0f0f0").pack(anchor=tk.W)
        
        # أزرار الحفظ والإلغاء
        buttons_frame = tk.Frame(dialog, bg="#f0f0f0")
        buttons_frame.pack(pady=20)
        
        def save_changes():
            new_password = password_entry.get().strip()
            name = name_entry.get().strip()
            role = role_var.get()
            
            if not name:
                messagebox.showerror("خطأ", "الاسم الكامل مطلوب")
                return
            
            # تحديث بيانات المستخدم
            if new_password:
                users_data[username]['password'] = new_password
            
            users_data[username]['name'] = name
            users_data[username]['role'] = role
            
            # تحديث الصلاحيات
            permissions = {}
            for perm_key, var in perm_vars.items():
                permissions[perm_key] = var.get()
            users_data[username]['permissions'] = permissions
            
            # حفظ التغييرات
            if self.save_users_data(users_data):
                messagebox.showinfo("نجح", f"تم تحديث بيانات المستخدم '{username}' بنجاح")
                self.load_users()
                dialog.destroy()
            else:
                messagebox.showerror("خطأ", "فشل في حفظ التغييرات")
        
        tk.Button(buttons_frame, text="💾 حفظ التغييرات", command=save_changes,
                 bg="#3498db", fg="white", font=("Arial", 12, "bold"),
                 width=15).pack(side=tk.LEFT, padx=10)
        
        tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                 bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                 width=15).pack(side=tk.LEFT, padx=10)
    
    def delete_user(self):
        """حذف مستخدم"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "الرجاء اختيار مستخدم للحذف")
            return
        
        username = self.users_tree.item(selection[0])['values'][0]
        
        # منع حذف المشرف العام
        if username == self.current_user:
            messagebox.showwarning("تحذير", "لا يمكن حذف المشرف العام")
            return
        
        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المستخدم '{username}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            users_data = self.load_users_data()
            if username in users_data:
                del users_data[username]
                if self.save_users_data(users_data):
                    messagebox.showinfo("نجح", f"تم حذف المستخدم '{username}' بنجاح")
                    self.load_users()
                    self.selected_user_info.set("لم يتم اختيار مستخدم")
                else:
                    messagebox.showerror("خطأ", "فشل في حذف المستخدم")
            else:
                messagebox.showerror("خطأ", "المستخدم غير موجود")

if __name__ == "__main__":
    # اختبار النظام
    root = tk.Tk()
    app = UserManagement(root, "يوسف")
    root.mainloop()
