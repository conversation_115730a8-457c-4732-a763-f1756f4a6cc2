#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
قسم الإجازات - نظام إدارة الموارد البشرية
Leave Department System - HR Management System

قسم متخصص لإدارة إجازات الموظفين مع:
- تصنيف المستخدمين (موظف/معلم)
- خصم تلقائي للموظفين، منح يدوي للمعلمين
- حساب أيام العمل الفعلية
- حالات الإجازة المتدرجة
- تنبيهات وتقارير شاملة
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import os
from datetime import datetime, timedelta, date
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, PatternFill, Alignment
import json

class LeaveDepartmentSystem:
    def __init__(self, parent_window):
        self.parent = parent_window
        self.window = None
        
        # تحميل البيانات
        self.employees_data = self.load_employees_data()
        self.leaves_data = self.load_leaves_data()
        self.balance_data = self.load_balance_data()
        self.settings = self.load_settings()
        
        print("🏢 تم تحميل قسم الإجازات")

    def show_leave_department(self):
        """عرض نافذة قسم الإجازات"""
        if self.window is None or not self.window.winfo_exists():
            self.create_leave_department_window()
        else:
            self.window.lift()
            self.window.focus_force()

    def create_leave_department_window(self):
        """إنشاء نافذة قسم الإجازات"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("قسم الإجازات - نظام إدارة الموارد البشرية")
        self.window.geometry("1400x900")
        self.window.configure(bg="#f0f8ff")
        
        # منع إغلاق النافذة بالضغط على X
        self.window.protocol("WM_DELETE_WINDOW", self.on_window_close)
        
        # العنوان الرئيسي
        title_frame = tk.Frame(self.window, bg="#2c3e50", height=80)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="🏢 قسم الإجازات",
                              font=("Arial", 20, "bold"), fg="white", bg="#2c3e50")
        title_label.pack(expand=True)
        
        # إطار الأزرار الرئيسية
        buttons_frame = tk.Frame(self.window, bg="#ecf0f1", height=100)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        buttons_frame.pack_propagate(False)
        
        # أزرار القسم
        self.create_department_buttons(buttons_frame)
        
        # إطار المحتوى الرئيسي
        content_frame = tk.Frame(self.window, bg="#f0f8ff")
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # إنشاء دفتر التبويبات
        self.notebook = ttk.Notebook(content_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # تبويبات القسم
        self.create_leave_request_tab()
        self.create_leave_approval_tab()
        self.create_reports_tab()
        self.create_settings_tab()
        
        # تحديث التنبيهات
        self.check_leave_alerts()

    def create_department_buttons(self, parent):
        """إنشاء أزرار القسم"""
        buttons = [
            ("📝 طلب إجازة جديد", self.new_leave_request, "#3498db"),
            ("✅ الموافقة على الإجازات", self.approve_leaves, "#27ae60"),
            ("📊 التقارير", self.show_reports, "#e67e22"),
            ("⚠️ التنبيهات", self.show_alerts, "#e74c3c"),
            ("⚙️ الإعدادات", self.show_settings, "#9b59b6"),
            ("📤 تصدير البيانات", self.export_data, "#34495e"),
            ("❌ إغلاق القسم", self.close_department, "#95a5a6")
        ]
        
        for i, (text, command, color) in enumerate(buttons):
            btn = tk.Button(parent, text=text, command=command,
                           font=("Arial", 14, "bold"), bg=color, fg="white",
                           relief=tk.RAISED, bd=4, padx=20, pady=12,
                           cursor="hand2", width=16)
            btn.pack(side=tk.LEFT, padx=8, pady=15)

    def load_employees_data(self):
        """تحميل بيانات الموظفين"""
        try:
            if os.path.exists("employees_data.xlsx"):
                wb = load_workbook("employees_data.xlsx")
                ws = wb.active
                
                employees = []
                headers = [cell.value for cell in ws[1]]
                
                for row in ws.iter_rows(min_row=2, values_only=True):
                    if row[0]:  # إذا كان هناك رقم وظيفي
                        employee = dict(zip(headers, row))
                        employees.append(employee)
                
                print(f"✅ قسم الإجازات: تم تحميل {len(employees)} موظف")
                return employees
            else:
                print("⚠️ قسم الإجازات: ملف بيانات الموظفين غير موجود")
                return []
        except Exception as e:
            print(f"❌ قسم الإجازات: خطأ في تحميل بيانات الموظفين: {e}")
            return []

    def load_leaves_data(self):
        """تحميل بيانات الإجازات"""
        try:
            if os.path.exists("department_leaves_data.xlsx"):
                wb = load_workbook("department_leaves_data.xlsx")
                ws = wb.active
                
                leaves = []
                headers = [cell.value for cell in ws[1]]
                
                for row in ws.iter_rows(min_row=2, values_only=True):
                    if row[0]:  # إذا كان هناك رقم وظيفي
                        leave = dict(zip(headers, row))
                        leaves.append(leave)
                
                print(f"✅ قسم الإجازات: تم تحميل {len(leaves)} إجازة")
                return leaves
            else:
                print("📝 قسم الإجازات: إنشاء ملف إجازات جديد")
                return []
        except Exception as e:
            print(f"❌ قسم الإجازات: خطأ في تحميل بيانات الإجازات: {e}")
            return []

    def load_balance_data(self):
        """تحميل بيانات أرصدة الإجازات"""
        try:
            if os.path.exists("leave_balance_data.xlsx"):
                wb = load_workbook("leave_balance_data.xlsx")
                ws = wb.active
                
                balances = []
                headers = [cell.value for cell in ws[1]]
                
                for row in ws.iter_rows(min_row=2, values_only=True):
                    if row[0]:  # إذا كان هناك رقم وظيفي
                        balance = dict(zip(headers, row))
                        balances.append(balance)
                
                print(f"✅ قسم الإجازات: تم تحميل {len(balances)} رصيد")
                return balances
            else:
                print("💰 قسم الإجازات: إنشاء ملف أرصدة جديد")
                return []
        except Exception as e:
            print(f"❌ قسم الإجازات: خطأ في تحميل أرصدة الإجازات: {e}")
            return []

    def load_settings(self):
        """تحميل إعدادات قسم الإجازات"""
        default_settings = {
            "weekend_days": ["Friday", "Saturday"],  # أيام العطل الأسبوعية
            "alert_days_before": 3,  # التنبيه قبل كم يوم للمعلمين
            "annual_leave_per_year": 30,  # الإجازة السنوية (يوم)
            "carry_over_enabled": True,  # ترحيل الرصيد
            "max_carry_over_days": 15  # أقصى أيام ترحيل
        }
        
        try:
            if os.path.exists("leave_department_settings.json"):
                with open("leave_department_settings.json", "r", encoding="utf-8") as f:
                    settings = json.load(f)
                    # دمج الإعدادات الافتراضية مع المحفوظة
                    default_settings.update(settings)
                    print("✅ قسم الإجازات: تم تحميل إعدادات القسم")
            else:
                print("⚙️ قسم الإجازات: استخدام الإعدادات الافتراضية")
        except Exception as e:
            print(f"❌ قسم الإجازات: خطأ في تحميل الإعدادات: {e}")
        
        return default_settings

    def save_settings(self):
        """حفظ إعدادات قسم الإجازات"""
        try:
            with open("leave_department_settings.json", "w", encoding="utf-8") as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
            print("✅ قسم الإجازات: تم حفظ إعدادات القسم")
        except Exception as e:
            print(f"❌ قسم الإجازات: خطأ في حفظ الإعدادات: {e}")

    def get_employee_type(self, employee_id):
        """تحديد نوع الموظف (موظف/معلم)"""
        for emp in self.employees_data:
            if str(emp.get("الرقم الوظيفي", "")) == str(employee_id):
                # فحص المسمى الوظيفي من عدة حقول محتملة
                job_title = (emp.get("المسمى الوظيفي", "") or
                           emp.get("الوظيفة", "") or
                           emp.get("المنصب", "") or
                           emp.get("job_title", "")).lower()

                # فحص كلمات دالة على المعلمين
                teacher_keywords = ["معلم", "معلمة", "مدرس", "مدرسة", "أستاذ", "أستاذة", "teacher"]
                if any(keyword in job_title for keyword in teacher_keywords):
                    return "teacher"

                # فحص كلمات دالة على الموظفين
                employee_keywords = ["موظف", "موظفة", "إداري", "إدارية", "employee", "staff"]
                if any(keyword in job_title for keyword in employee_keywords):
                    return "employee"

                # إذا لم يتم العثور على كلمات دالة، فحص بناءً على القسم أو الإدارة
                department = (emp.get("القسم", "") or
                            emp.get("الإدارة", "") or
                            emp.get("department", "")).lower()

                education_departments = ["تعليم", "تربية", "مدرسة", "education", "teaching"]
                if any(dept in department for dept in education_departments):
                    return "teacher"

                break

        return "employee"  # افتراضي

    def calculate_working_days(self, start_date, end_date):
        """حساب أيام العمل الفعلية (استثناء الجمعة والسبت)"""
        try:
            if isinstance(start_date, str):
                start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
            if isinstance(end_date, str):
                end_date = datetime.strptime(end_date, "%Y-%m-%d").date()
            
            working_days = 0
            current_date = start_date
            
            while current_date <= end_date:
                # تحقق من أن اليوم ليس جمعة (4) أو سبت (5)
                if current_date.weekday() not in [4, 5]:  # 0=Monday, 6=Sunday
                    working_days += 1
                current_date += timedelta(days=1)
            
            return working_days
        except Exception as e:
            print(f"❌ قسم الإجازات: خطأ في حساب أيام العمل: {e}")
            return 0

    def get_employee_balance(self, employee_id):
        """الحصول على رصيد الموظف"""
        for balance in self.balance_data:
            if str(balance.get("الرقم الوظيفي", "")) == str(employee_id):
                # محاولة الحصول على الأرصدة من حقول مختلفة
                auto_balance = (balance.get("الرصيد المحسوب تلقائياً", 0) or
                              balance.get("الرصيد التلقائي", 0) or
                              balance.get("automatic_balance", 0))

                manual_balance = (balance.get("الرصيد اليدوي المضاف", 0) or
                                balance.get("الرصيد اليدوي", 0) or
                                balance.get("manual_balance", 0))

                used_leaves = (balance.get("الإجازات المأخوذة", 0) or
                             balance.get("المستخدم", 0) or
                             balance.get("used_leaves", 0))

                try:
                    # تحويل القيم إلى أرقام
                    auto_balance = float(auto_balance or 0)
                    manual_balance = float(manual_balance or 0)
                    used_leaves = float(used_leaves or 0)

                    total_balance = auto_balance + manual_balance
                    remaining_balance = total_balance - used_leaves

                    return {
                        "total": int(total_balance),
                        "auto": int(auto_balance),
                        "manual": int(manual_balance),
                        "used": int(used_leaves),
                        "remaining": int(remaining_balance)
                    }
                except (ValueError, TypeError) as e:
                    print(f"❌ خطأ في تحويل أرصدة الموظف {employee_id}: {e}")
                    return {"total": 0, "auto": 0, "manual": 0, "used": 0, "remaining": 0}

        # إذا لم يتم العثور على رصيد للموظف، إنشاء رصيد افتراضي
        print(f"⚠️ لم يتم العثور على رصيد للموظف {employee_id}")
        return {"total": 0, "auto": 0, "manual": 0, "used": 0, "remaining": 0}

    def new_leave_request(self):
        """طلب إجازة جديد"""
        self.notebook.select(0)  # الانتقال لتبويب طلب الإجازة

    def approve_leaves(self):
        """الموافقة على الإجازات"""
        self.notebook.select(1)  # الانتقال لتبويب الموافقة

    def show_reports(self):
        """عرض التقارير"""
        self.notebook.select(2)  # الانتقال لتبويب التقارير

    def show_alerts(self):
        """عرض التنبيهات"""
        alerts = self.check_leave_alerts()
        if alerts:
            alert_message = "⚠️ تنبيهات قسم الإجازات:\n\n"
            for alert in alerts:
                alert_message += f"• {alert}\n"
            messagebox.showwarning("تنبيهات قسم الإجازات", alert_message)
        else:
            messagebox.showinfo("التنبيهات", "✅ لا توجد تنبيهات حالياً في قسم الإجازات")

    def show_settings(self):
        """عرض الإعدادات"""
        self.notebook.select(3)  # الانتقال لتبويب الإعدادات

    def export_data(self):
        """تصدير بيانات قسم الإجازات"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"تقرير_قسم_الإجازات_{timestamp}.xlsx"
            
            export_file = filedialog.asksaveasfilename(
                title="تصدير بيانات قسم الإجازات",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialfile=filename,
                initialdir=os.path.expanduser("~/Desktop")
            )
            
            if export_file:
                self.create_department_report(export_file)
                messagebox.showinfo("نجح التصدير", f"تم تصدير تقرير قسم الإجازات:\n{export_file}")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تصدير بيانات القسم: {str(e)}")

    def close_department(self):
        """إغلاق قسم الإجازات"""
        if messagebox.askyesno("إغلاق القسم", "هل تريد إغلاق قسم الإجازات؟"):
            self.on_window_close()

    def on_window_close(self):
        """عند إغلاق النافذة"""
        if self.window:
            self.window.destroy()
            self.window = None
        print("🏢 تم إغلاق قسم الإجازات")

    def check_leave_alerts(self):
        """فحص تنبيهات الإجازات"""
        alerts = []
        current_date = datetime.now().date()
        alert_days = self.settings.get("alert_days_before", 3)
        
        for leave in self.leaves_data:
            if leave.get("الحالة") == "موافق عليها":
                emp_id = leave.get("الرقم الوظيفي")
                emp_type = self.get_employee_type(emp_id)
                
                # تنبيهات للمعلمين فقط
                if emp_type == "teacher":
                    try:
                        end_date = datetime.strptime(leave.get("تاريخ الانتهاء"), "%Y-%m-%d").date()
                        days_remaining = (end_date - current_date).days
                        
                        if 0 <= days_remaining <= alert_days:
                            emp_name = leave.get("اسم الموظف", "غير معروف")
                            alerts.append(f"المعلم {emp_name} - تنتهي إجازته خلال {days_remaining} يوم")
                    except:
                        continue
        
        return alerts

    def create_leave_request_tab(self):
        """تبويب طلب الإجازة"""
        request_frame = ttk.Frame(self.notebook)
        self.notebook.add(request_frame, text="📝 طلب إجازة")

        # إطار معلومات الموظف
        emp_info_frame = tk.LabelFrame(request_frame, text="معلومات الموظف",
                                      font=("Arial", 12, "bold"), fg="#2c3e50")
        emp_info_frame.pack(fill=tk.X, padx=10, pady=10)

        # الرقم الوظيفي
        tk.Label(emp_info_frame, text="الرقم الوظيفي:", font=("Arial", 11)).grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.emp_id_var = tk.StringVar()
        emp_id_combo = ttk.Combobox(emp_info_frame, textvariable=self.emp_id_var, width=15)
        emp_id_combo['values'] = [emp.get("الرقم الوظيفي", "") for emp in self.employees_data]
        emp_id_combo.grid(row=0, column=1, padx=5, pady=5)
        emp_id_combo.bind('<<ComboboxSelected>>', self.on_employee_selected)

        # اسم الموظف
        tk.Label(emp_info_frame, text="اسم الموظف:", font=("Arial", 11)).grid(row=0, column=2, sticky="w", padx=5, pady=5)
        self.emp_name_var = tk.StringVar()
        tk.Label(emp_info_frame, textvariable=self.emp_name_var, font=("Arial", 11, "bold"), fg="#2980b9").grid(row=0, column=3, sticky="w", padx=5, pady=5)

        # نوع الموظف
        tk.Label(emp_info_frame, text="نوع الموظف:", font=("Arial", 11)).grid(row=1, column=0, sticky="w", padx=5, pady=5)
        self.emp_type_var = tk.StringVar()
        tk.Label(emp_info_frame, textvariable=self.emp_type_var, font=("Arial", 11, "bold"), fg="#e74c3c").grid(row=1, column=1, sticky="w", padx=5, pady=5)

        # الرصيد المتبقي
        tk.Label(emp_info_frame, text="الرصيد المتبقي:", font=("Arial", 11)).grid(row=1, column=2, sticky="w", padx=5, pady=5)
        self.balance_var = tk.StringVar()
        tk.Label(emp_info_frame, textvariable=self.balance_var, font=("Arial", 11, "bold"), fg="#27ae60").grid(row=1, column=3, sticky="w", padx=5, pady=5)

        # إطار تفاصيل الإجازة المحسن
        leave_details_frame = tk.LabelFrame(request_frame, text="📋 تفاصيل الإجازة",
                                           font=("Arial", 14, "bold"), fg="#2c3e50", bg="#f8f9fa",
                                           padx=15, pady=15)
        leave_details_frame.pack(fill=tk.X, padx=10, pady=15)

        # الصف الأول: نوع الإجازة
        row1_frame = tk.Frame(leave_details_frame, bg="#f8f9fa")
        row1_frame.pack(fill=tk.X, pady=10)

        tk.Label(row1_frame, text="🏷️ نوع الإجازة:", font=("Arial", 12, "bold"),
                bg="#f8f9fa", fg="#2c3e50").pack(side=tk.LEFT, padx=5)

        self.leave_type_var = tk.StringVar()
        leave_type_combo = ttk.Combobox(row1_frame, textvariable=self.leave_type_var, width=25,
                                       font=("Arial", 11), state="readonly")
        leave_type_combo['values'] = ["إجازة سنوية", "إجازة مرضية", "إجازة طارئة", "إجازة أمومة", "إجازة بدون راتب"]
        leave_type_combo.pack(side=tk.LEFT, padx=10)

        # الصف الثاني: التواريخ
        dates_frame = tk.LabelFrame(leave_details_frame, text="📅 فترة الإجازة",
                                   font=("Arial", 12, "bold"), fg="#2c3e50", bg="#f8f9fa",
                                   padx=10, pady=10)
        dates_frame.pack(fill=tk.X, pady=10)

        # تاريخ البدء
        start_date_row = tk.Frame(dates_frame, bg="#f8f9fa")
        start_date_row.pack(fill=tk.X, pady=8)

        tk.Label(start_date_row, text="🟢 تاريخ البدء:", font=("Arial", 12, "bold"),
                bg="#f8f9fa", fg="#27ae60", width=15, anchor="w").pack(side=tk.LEFT, padx=5)

        start_date_frame = tk.Frame(start_date_row, bg="#f8f9fa")
        start_date_frame.pack(side=tk.LEFT, padx=10)

        self.start_date_var = tk.StringVar()
        start_date_entry = tk.Entry(start_date_frame, textvariable=self.start_date_var, width=15,
                                   font=("Arial", 12), state="readonly", bg="white", relief=tk.SUNKEN, bd=2)
        start_date_entry.pack(side=tk.LEFT, padx=2)
        start_date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))

        # زر اختيار تاريخ البدء محسن
        start_date_btn = tk.Button(start_date_frame, text="📅 اختيار التاريخ",
                                  command=lambda: self.select_date(self.start_date_var),
                                  bg="#27ae60", fg="white", font=("Arial", 12, "bold"),
                                  padx=15, pady=8, relief=tk.RAISED, bd=3,
                                  cursor="hand2", width=15)
        start_date_btn.pack(side=tk.LEFT, padx=8)

        # تلميح التنسيق
        tk.Label(start_date_row, text="(سنة-شهر-يوم)", font=("Arial", 9),
                fg="#7f8c8d", bg="#f8f9fa").pack(side=tk.LEFT, padx=5)

        # تاريخ الانتهاء
        end_date_row = tk.Frame(dates_frame, bg="#f8f9fa")
        end_date_row.pack(fill=tk.X, pady=8)

        tk.Label(end_date_row, text="🔴 تاريخ الانتهاء:", font=("Arial", 12, "bold"),
                bg="#f8f9fa", fg="#e74c3c", width=15, anchor="w").pack(side=tk.LEFT, padx=5)

        end_date_frame = tk.Frame(end_date_row, bg="#f8f9fa")
        end_date_frame.pack(side=tk.LEFT, padx=10)

        self.end_date_var = tk.StringVar()
        end_date_entry = tk.Entry(end_date_frame, textvariable=self.end_date_var, width=15,
                                 font=("Arial", 12), state="readonly", bg="white", relief=tk.SUNKEN, bd=2)
        end_date_entry.pack(side=tk.LEFT, padx=2)

        # زر اختيار تاريخ الانتهاء محسن
        end_date_btn = tk.Button(end_date_frame, text="📅 اختيار التاريخ",
                                command=lambda: self.select_date(self.end_date_var),
                                bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                                padx=15, pady=8, relief=tk.RAISED, bd=3,
                                cursor="hand2", width=15)
        end_date_btn.pack(side=tk.LEFT, padx=8)

        # تلميح التنسيق
        tk.Label(end_date_row, text="(سنة-شهر-يوم)", font=("Arial", 9),
                fg="#7f8c8d", bg="#f8f9fa").pack(side=tk.LEFT, padx=5)

        # الصف الثالث: حساب الأيام
        calculation_frame = tk.Frame(leave_details_frame, bg="#f8f9fa")
        calculation_frame.pack(fill=tk.X, pady=10)

        # زر حساب الأيام محسن
        calc_btn = tk.Button(calculation_frame, text="🧮 حساب أيام العمل",
                            command=self.calculate_leave_days,
                            bg="#3498db", fg="white", font=("Arial", 14, "bold"),
                            padx=20, pady=12, relief=tk.RAISED, bd=4,
                            cursor="hand2", width=20)
        calc_btn.pack(side=tk.LEFT, padx=15)

        # عرض عدد الأيام
        tk.Label(calculation_frame, text="📊 عدد أيام العمل:", font=("Arial", 12, "bold"),
                bg="#f8f9fa", fg="#2c3e50").pack(side=tk.LEFT, padx=20)

        self.leave_days_var = tk.StringVar()
        days_label = tk.Label(calculation_frame, textvariable=self.leave_days_var,
                             font=("Arial", 14, "bold"), fg="#e67e22", bg="#f8f9fa",
                             relief=tk.SUNKEN, bd=2, padx=10, pady=5)
        days_label.pack(side=tk.LEFT, padx=5)

        # الصف الرابع: سبب الإجازة
        reason_frame = tk.LabelFrame(leave_details_frame, text="📝 سبب الإجازة",
                                    font=("Arial", 12, "bold"), fg="#2c3e50", bg="#f8f9fa",
                                    padx=10, pady=10)
        reason_frame.pack(fill=tk.X, pady=10)

        self.leave_reason_text = tk.Text(reason_frame, width=60, height=4, font=("Arial", 11),
                                        wrap=tk.WORD, relief=tk.SUNKEN, bd=2)

        # شريط تمرير للنص
        reason_scrollbar = tk.Scrollbar(reason_frame, orient=tk.VERTICAL, command=self.leave_reason_text.yview)
        self.leave_reason_text.configure(yscrollcommand=reason_scrollbar.set)

        self.leave_reason_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        reason_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)

        # أزرار العمليات المحسنة
        buttons_frame = tk.LabelFrame(request_frame, text="⚡ العمليات المتاحة",
                                     font=("Arial", 12, "bold"), fg="#2c3e50", bg="#f8f9fa",
                                     padx=15, pady=15)
        buttons_frame.pack(fill=tk.X, padx=10, pady=20)

        # صف الأزرار الرئيسية
        main_buttons_row = tk.Frame(buttons_frame, bg="#f8f9fa")
        main_buttons_row.pack(pady=10)

        # زر تقديم الطلب
        submit_btn = tk.Button(main_buttons_row, text="📝 تقديم طلب الإجازة",
                              command=self.submit_leave_request,
                              bg="#27ae60", fg="white", font=("Arial", 16, "bold"),
                              padx=30, pady=15, relief=tk.RAISED, bd=4,
                              cursor="hand2", width=18)
        submit_btn.pack(side=tk.LEFT, padx=20)

        # زر مسح الحقول
        clear_btn = tk.Button(main_buttons_row, text="🔄 مسح جميع الحقول",
                             command=self.clear_leave_form,
                             bg="#95a5a6", fg="white", font=("Arial", 16, "bold"),
                             padx=30, pady=15, relief=tk.RAISED, bd=4,
                             cursor="hand2", width=18)
        clear_btn.pack(side=tk.LEFT, padx=20)

        # صف الأزرار الثانوية
        secondary_buttons_row = tk.Frame(buttons_frame, bg="#f8f9fa")
        secondary_buttons_row.pack(pady=10)

        # زر عرض إجازات الموظف
        show_leaves_btn = tk.Button(secondary_buttons_row, text="📋 عرض إجازات الموظف",
                                   command=self.show_employee_leaves,
                                   bg="#9b59b6", fg="white", font=("Arial", 14, "bold"),
                                   padx=25, pady=12, relief=tk.RAISED, bd=3,
                                   cursor="hand2", width=16)
        show_leaves_btn.pack(side=tk.LEFT, padx=15)

        # زر معاينة البيانات
        preview_btn = tk.Button(secondary_buttons_row, text="👁️ معاينة البيانات",
                               command=self.preview_leave_data,
                               bg="#3498db", fg="white", font=("Arial", 14, "bold"),
                               padx=25, pady=12, relief=tk.RAISED, bd=3,
                               cursor="hand2", width=16)
        preview_btn.pack(side=tk.LEFT, padx=15)

        # زر مساعدة
        help_btn = tk.Button(secondary_buttons_row, text="❓ مساعدة",
                            command=self.show_leave_help,
                            bg="#f39c12", fg="white", font=("Arial", 14, "bold"),
                            padx=25, pady=12, relief=tk.RAISED, bd=3,
                            cursor="hand2", width=16)
        help_btn.pack(side=tk.LEFT, padx=15)

    def create_leave_approval_tab(self):
        """تبويب الموافقة على الإجازات"""
        approval_frame = ttk.Frame(self.notebook)
        self.notebook.add(approval_frame, text="✅ الموافقة على الإجازات")

        # أزرار التحكم
        control_frame = tk.Frame(approval_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Button(control_frame, text="🔄 تحديث القائمة", command=self.refresh_pending_leaves,
                 bg="#3498db", fg="white", font=("Arial", 14, "bold"),
                 padx=20, pady=10, relief=tk.RAISED, bd=3, cursor="hand2", width=14).pack(side=tk.LEFT, padx=8)

        tk.Button(control_frame, text="✅ موافقة على الإجازة", command=self.approve_selected_leave,
                 bg="#27ae60", fg="white", font=("Arial", 14, "bold"),
                 padx=20, pady=10, relief=tk.RAISED, bd=3, cursor="hand2", width=14).pack(side=tk.LEFT, padx=8)

        tk.Button(control_frame, text="❌ رفض الإجازة", command=self.reject_selected_leave,
                 bg="#e74c3c", fg="white", font=("Arial", 14, "bold"),
                 padx=20, pady=10, relief=tk.RAISED, bd=3, cursor="hand2", width=14).pack(side=tk.LEFT, padx=8)

        tk.Button(control_frame, text="📝 إضافة ملاحظة", command=self.add_leave_note,
                 bg="#f39c12", fg="white", font=("Arial", 14, "bold"),
                 padx=20, pady=10, relief=tk.RAISED, bd=3, cursor="hand2", width=14).pack(side=tk.LEFT, padx=8)

        # جدول الإجازات المعلقة
        pending_frame = tk.LabelFrame(approval_frame, text="الإجازات في الانتظار",
                                     font=("Arial", 12, "bold"), fg="#2c3e50")
        pending_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # إنشاء الجدول
        columns = ("الرقم الوظيفي", "اسم الموظف", "نوع الموظف", "نوع الإجازة",
                  "تاريخ البدء", "تاريخ الانتهاء", "عدد الأيام", "الرصيد المتبقي", "الحالة")

        self.pending_tree = ttk.Treeview(pending_frame, columns=columns, show="headings", height=15)

        # تعريف العناوين
        column_widths = [100, 150, 100, 120, 100, 100, 80, 100, 100]
        for i, col in enumerate(columns):
            self.pending_tree.heading(col, text=col)
            self.pending_tree.column(col, width=column_widths[i], anchor="center")

        # شريط التمرير
        pending_scrollbar = ttk.Scrollbar(pending_frame, orient=tk.VERTICAL, command=self.pending_tree.yview)
        self.pending_tree.configure(yscrollcommand=pending_scrollbar.set)

        self.pending_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        pending_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # تحميل البيانات
        self.refresh_pending_leaves()

    def create_reports_tab(self):
        """تبويب التقارير"""
        reports_frame = ttk.Frame(self.notebook)
        self.notebook.add(reports_frame, text="📊 التقارير")

        # أزرار التقارير
        reports_buttons_frame = tk.Frame(reports_frame)
        reports_buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Button(reports_buttons_frame, text="📋 تقرير شامل للإجازات",
                 command=self.generate_comprehensive_report,
                 bg="#3498db", fg="white", font=("Arial", 12, "bold"), padx=20, pady=10).pack(side=tk.LEFT, padx=10)

        tk.Button(reports_buttons_frame, text="👥 تقرير حسب الموظف",
                 command=self.generate_employee_report,
                 bg="#27ae60", fg="white", font=("Arial", 12, "bold"), padx=20, pady=10).pack(side=tk.LEFT, padx=10)

        tk.Button(reports_buttons_frame, text="📊 إحصائيات الإجازات",
                 command=self.generate_statistics_report,
                 bg="#e67e22", fg="white", font=("Arial", 12, "bold"), padx=20, pady=10).pack(side=tk.LEFT, padx=10)

        # إطار معاينة التقرير
        preview_frame = tk.LabelFrame(reports_frame, text="معاينة التقرير",
                                     font=("Arial", 12, "bold"), fg="#2c3e50")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # منطقة النص للمعاينة
        self.report_preview = tk.Text(preview_frame, font=("Arial", 10), wrap=tk.WORD)
        preview_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.report_preview.yview)
        self.report_preview.configure(yscrollcommand=preview_scrollbar.set)

        self.report_preview.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        preview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_settings_tab(self):
        """تبويب الإعدادات"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="⚙️ الإعدادات")

        # إعدادات أيام العطل
        weekend_frame = tk.LabelFrame(settings_frame, text="إعدادات أيام العطل الأسبوعية",
                                     font=("Arial", 12, "bold"), fg="#2c3e50")
        weekend_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(weekend_frame, text="أيام العطل الأسبوعية:", font=("Arial", 11)).pack(anchor="w", padx=10, pady=5)

        weekend_days_frame = tk.Frame(weekend_frame)
        weekend_days_frame.pack(anchor="w", padx=20, pady=5)

        self.weekend_vars = {}
        days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
        days_ar = ["الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت", "الأحد"]

        for i, (day, day_ar) in enumerate(zip(days, days_ar)):
            self.weekend_vars[day] = tk.BooleanVar()
            if day in self.settings.get("weekend_days", []):
                self.weekend_vars[day].set(True)

            tk.Checkbutton(weekend_days_frame, text=day_ar, variable=self.weekend_vars[day],
                          font=("Arial", 10)).grid(row=i//4, column=i%4, sticky="w", padx=10, pady=2)

        # إعدادات التنبيهات
        alerts_frame = tk.LabelFrame(settings_frame, text="إعدادات التنبيهات",
                                    font=("Arial", 12, "bold"), fg="#2c3e50")
        alerts_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(alerts_frame, text="التنبيه قبل انتهاء إجازة المعلمين (بالأيام):",
                font=("Arial", 11)).grid(row=0, column=0, sticky="w", padx=10, pady=5)

        self.alert_days_var = tk.StringVar()
        self.alert_days_var.set(str(self.settings.get("alert_days_before", 3)))
        tk.Entry(alerts_frame, textvariable=self.alert_days_var, width=10,
                font=("Arial", 11)).grid(row=0, column=1, padx=10, pady=5)

        # أزرار الحفظ
        save_frame = tk.Frame(settings_frame)
        save_frame.pack(fill=tk.X, padx=10, pady=20)

        tk.Button(save_frame, text="💾 حفظ الإعدادات", command=self.save_settings_changes,
                 bg="#27ae60", fg="white", font=("Arial", 16, "bold"),
                 padx=30, pady=15, relief=tk.RAISED, bd=4, cursor="hand2", width=18).pack(side=tk.LEFT, padx=15)

    def on_employee_selected(self, event=None):
        """عند اختيار موظف"""
        emp_id = self.emp_id_var.get()
        if emp_id:
            # البحث عن الموظف
            employee_found = False
            for emp in self.employees_data:
                if str(emp.get("الرقم الوظيفي", "")) == str(emp_id):
                    # عرض اسم الموظف
                    emp_name = emp.get("الاسم العربي", "") or emp.get("الاسم", "") or "غير محدد"
                    self.emp_name_var.set(emp_name)

                    # تحديد نوع الموظف بدقة
                    emp_type = self.get_employee_type(emp_id)
                    job_title = emp.get("المسمى الوظيفي", "") or emp.get("الوظيفة", "") or emp.get("المنصب", "")

                    if emp_type == "teacher":
                        self.emp_type_var.set(f"معلم/معلمة ({job_title})")
                        self.balance_var.set("غير محدود")
                    else:
                        self.emp_type_var.set(f"موظف/موظفة ({job_title})")
                        balance = self.get_employee_balance(emp_id)
                        if balance['remaining'] >= 0:
                            self.balance_var.set(f"{balance['remaining']} يوم متبقي")
                        else:
                            self.balance_var.set(f"عجز {abs(balance['remaining'])} يوم")

                    employee_found = True
                    print(f"✅ تم اختيار الموظف: {emp_name} ({emp_id}) - نوع: {emp_type}")
                    break

            if not employee_found:
                self.emp_name_var.set("موظف غير موجود")
                self.emp_type_var.set("غير محدد")
                self.balance_var.set("غير متاح")
                print(f"❌ الموظف {emp_id} غير موجود في البيانات")
        else:
            # مسح المعلومات عند عدم اختيار موظف
            self.emp_name_var.set("")
            self.emp_type_var.set("")
            self.balance_var.set("")

    def select_date(self, date_var):
        """فتح نافذة اختيار التاريخ المحسنة"""
        try:
            # إنشاء نافذة اختيار التاريخ
            date_window = tk.Toplevel(self.window)
            date_window.title("📅 اختيار التاريخ")
            date_window.geometry("450x400")
            date_window.configure(bg="#f8f9fa")
            date_window.resizable(False, False)

            # جعل النافذة في المقدمة ووسط الشاشة
            date_window.transient(self.window)
            date_window.grab_set()

            # توسيط النافذة
            date_window.update_idletasks()
            x = (date_window.winfo_screenwidth() // 2) - (450 // 2)
            y = (date_window.winfo_screenheight() // 2) - (400 // 2)
            date_window.geometry(f"450x400+{x}+{y}")

            # عنوان النافذة
            title_frame = tk.Frame(date_window, bg="#2c3e50", height=60)
            title_frame.pack(fill=tk.X)
            title_frame.pack_propagate(False)

            tk.Label(title_frame, text="📅 اختيار التاريخ",
                    font=("Arial", 16, "bold"), bg="#2c3e50", fg="white").pack(expand=True)

            # إطار المحتوى الرئيسي
            main_frame = tk.Frame(date_window, bg="#f8f9fa")
            main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # متغيرات التاريخ
            current_date = datetime.now()
            try:
                if date_var.get():
                    current_date = datetime.strptime(date_var.get(), "%Y-%m-%d")
            except:
                pass

            year_var = tk.StringVar(value=str(current_date.year))
            month_var = tk.StringVar(value=str(current_date.month))
            day_var = tk.StringVar(value=str(current_date.day))

            # إطار اختيار التاريخ
            date_selection_frame = tk.LabelFrame(main_frame, text="اختيار التاريخ",
                                               font=("Arial", 12, "bold"), fg="#2c3e50",
                                               bg="#f8f9fa", padx=15, pady=15)
            date_selection_frame.pack(fill=tk.X, pady=(0, 20))

            # السنة
            year_frame = tk.Frame(date_selection_frame, bg="#f8f9fa")
            year_frame.pack(fill=tk.X, pady=5)

            tk.Label(year_frame, text="السنة:", font=("Arial", 12, "bold"),
                    bg="#f8f9fa", fg="#2c3e50").pack(side=tk.LEFT)
            year_combo = ttk.Combobox(year_frame, textvariable=year_var, width=15,
                                    font=("Arial", 11), state="readonly")
            year_combo['values'] = [str(y) for y in range(2020, 2035)]
            year_combo.pack(side=tk.RIGHT)

            # الشهر
            month_frame = tk.Frame(date_selection_frame, bg="#f8f9fa")
            month_frame.pack(fill=tk.X, pady=5)

            tk.Label(month_frame, text="الشهر:", font=("Arial", 12, "bold"),
                    bg="#f8f9fa", fg="#2c3e50").pack(side=tk.LEFT)

            # أسماء الشهور بالعربية
            months_ar = [
                "1 - يناير", "2 - فبراير", "3 - مارس", "4 - أبريل",
                "5 - مايو", "6 - يونيو", "7 - يوليو", "8 - أغسطس",
                "9 - سبتمبر", "10 - أكتوبر", "11 - نوفمبر", "12 - ديسمبر"
            ]

            month_combo = ttk.Combobox(month_frame, width=15, font=("Arial", 11), state="readonly")
            month_combo['values'] = months_ar
            month_combo.pack(side=tk.RIGHT)

            # تعيين الشهر الحالي
            month_combo.set(months_ar[int(month_var.get()) - 1])

            # اليوم
            day_frame = tk.Frame(date_selection_frame, bg="#f8f9fa")
            day_frame.pack(fill=tk.X, pady=5)

            tk.Label(day_frame, text="اليوم:", font=("Arial", 12, "bold"),
                    bg="#f8f9fa", fg="#2c3e50").pack(side=tk.LEFT)
            day_combo = ttk.Combobox(day_frame, textvariable=day_var, width=15,
                                   font=("Arial", 11), state="readonly")
            day_combo['values'] = [str(d) for d in range(1, 32)]
            day_combo.pack(side=tk.RIGHT)

            # إطار المعاينة
            preview_frame = tk.LabelFrame(main_frame, text="معاينة التاريخ المختار",
                                        font=("Arial", 12, "bold"), fg="#2c3e50",
                                        bg="#f8f9fa", padx=15, pady=15)
            preview_frame.pack(fill=tk.X, pady=(0, 20))

            preview_var = tk.StringVar()
            preview_label = tk.Label(preview_frame, textvariable=preview_var,
                                   font=("Arial", 14, "bold"), fg="#2980b9", bg="#f8f9fa")
            preview_label.pack()

            # معلومات إضافية عن التاريخ
            info_var = tk.StringVar()
            info_label = tk.Label(preview_frame, textvariable=info_var,
                                font=("Arial", 10), fg="#7f8c8d", bg="#f8f9fa")
            info_label.pack(pady=(5, 0))

            def update_preview():
                try:
                    # التحقق من وجود القيم
                    if not year_var.get() or not day_var.get():
                        preview_var.set("الرجاء اختيار التاريخ")
                        info_var.set("اختر السنة والشهر واليوم")
                        return

                    year = int(year_var.get())

                    # الحصول على رقم الشهر من الاختيار
                    month_selection = month_combo.get()
                    if month_selection and " - " in month_selection:
                        month = int(month_selection.split(" - ")[0])
                    elif month_var.get():
                        month = int(month_var.get())
                    else:
                        preview_var.set("الرجاء اختيار الشهر")
                        info_var.set("اختر الشهر من القائمة")
                        return

                    day = int(day_var.get())

                    # التحقق من صحة التاريخ
                    selected_date = datetime(year, month, day)

                    # تحديث المعاينة
                    formatted_date = selected_date.strftime("%Y-%m-%d")
                    preview_var.set(formatted_date)

                    # معلومات إضافية
                    weekday_names = ["الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت", "الأحد"]
                    weekday = weekday_names[selected_date.weekday()]

                    # حساب الفرق مع اليوم
                    today = datetime.now().date()
                    diff = (selected_date.date() - today).days

                    if diff == 0:
                        diff_text = "اليوم"
                    elif diff == 1:
                        diff_text = "غداً"
                    elif diff == -1:
                        diff_text = "أمس"
                    elif diff > 0:
                        diff_text = f"بعد {diff} يوم"
                    else:
                        diff_text = f"قبل {abs(diff)} يوم"

                    info_var.set(f"يوم {weekday} - {diff_text}")

                    # تحديث متغير الشهر
                    month_var.set(str(month))

                    print(f"🔍 معاينة التاريخ: {formatted_date} ({weekday})")

                except ValueError as ve:
                    preview_var.set("❌ تاريخ غير صحيح")
                    info_var.set("تحقق من اليوم والشهر والسنة")
                    print(f"❌ خطأ في معاينة التاريخ: {ve}")
                except Exception as e:
                    preview_var.set("❌ خطأ")
                    info_var.set("حدث خطأ في المعاينة")
                    print(f"❌ خطأ عام في المعاينة: {e}")

            # ربط التحديث بالتغيير
            year_var.trace('w', lambda *args: update_preview())
            month_var.trace('w', lambda *args: update_preview())
            day_var.trace('w', lambda *args: update_preview())

            # ربط تغيير الشهر
            def on_month_change(event):
                update_preview()
            month_combo.bind('<<ComboboxSelected>>', on_month_change)

            # تحديث أولي
            update_preview()

            # إطار الأزرار السريعة
            quick_buttons_frame = tk.LabelFrame(main_frame, text="اختيار سريع",
                                              font=("Arial", 11, "bold"), fg="#2c3e50",
                                              bg="#f8f9fa", padx=10, pady=10)
            quick_buttons_frame.pack(fill=tk.X, pady=(0, 20))

            quick_frame = tk.Frame(quick_buttons_frame, bg="#f8f9fa")
            quick_frame.pack()

            def set_today():
                today = datetime.now()
                year_var.set(str(today.year))
                month_combo.set(months_ar[today.month - 1])
                day_var.set(str(today.day))
                update_preview()

            def set_tomorrow():
                tomorrow = datetime.now() + timedelta(days=1)
                year_var.set(str(tomorrow.year))
                month_combo.set(months_ar[tomorrow.month - 1])
                day_var.set(str(tomorrow.day))
                update_preview()

            def set_next_week():
                next_week = datetime.now() + timedelta(days=7)
                year_var.set(str(next_week.year))
                month_combo.set(months_ar[next_week.month - 1])
                day_var.set(str(next_week.day))
                update_preview()

            tk.Button(quick_frame, text="📅 اليوم", command=set_today,
                     bg="#3498db", fg="white", font=("Arial", 10, "bold"),
                     padx=10, pady=5, relief=tk.RAISED, bd=2).pack(side=tk.LEFT, padx=5)

            tk.Button(quick_frame, text="➡️ غداً", command=set_tomorrow,
                     bg="#2ecc71", fg="white", font=("Arial", 10, "bold"),
                     padx=10, pady=5, relief=tk.RAISED, bd=2).pack(side=tk.LEFT, padx=5)

            tk.Button(quick_frame, text="📆 الأسبوع القادم", command=set_next_week,
                     bg="#9b59b6", fg="white", font=("Arial", 10, "bold"),
                     padx=10, pady=5, relief=tk.RAISED, bd=2).pack(side=tk.LEFT, padx=5)

            # أزرار التحكم الرئيسية
            buttons_frame = tk.Frame(main_frame, bg="#f8f9fa")
            buttons_frame.pack(fill=tk.X)

            def confirm_date():
                try:
                    # التحقق من وجود القيم
                    if not year_var.get() or not day_var.get():
                        messagebox.showerror("خطأ", "الرجاء اختيار جميع قيم التاريخ (السنة، الشهر، اليوم)")
                        return

                    year = int(year_var.get())

                    # الحصول على رقم الشهر
                    month_selection = month_combo.get()
                    if month_selection and " - " in month_selection:
                        month = int(month_selection.split(" - ")[0])
                    elif month_var.get():
                        month = int(month_var.get())
                    else:
                        messagebox.showerror("خطأ", "الرجاء اختيار الشهر")
                        return

                    day = int(day_var.get())

                    # التحقق من صحة التاريخ
                    selected_date = datetime(year, month, day)
                    formatted_date = selected_date.strftime("%Y-%m-%d")

                    # تحديث المتغير
                    date_var.set(formatted_date)

                    # رسالة تأكيد
                    print(f"✅ تم اختيار التاريخ: {formatted_date}")
                    messagebox.showinfo("تم الحفظ", f"تم حفظ التاريخ: {formatted_date}")

                    # إغلاق النافذة
                    date_window.destroy()

                except ValueError as ve:
                    print(f"❌ خطأ في التاريخ: {ve}")
                    messagebox.showerror("خطأ في التاريخ",
                                       f"التاريخ المدخل غير صحيح:\n{str(ve)}\n\nالرجاء التحقق من:\n• اليوم (1-31)\n• الشهر (1-12)\n• السنة")
                except Exception as e:
                    print(f"❌ خطأ عام: {e}")
                    messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

            # أزرار كبيرة وواضحة
            tk.Button(buttons_frame, text="✅ تأكيد الاختيار", command=confirm_date,
                     bg="#27ae60", fg="white", font=("Arial", 12, "bold"),
                     padx=20, pady=10, relief=tk.RAISED, bd=3).pack(side=tk.LEFT, padx=10)

            tk.Button(buttons_frame, text="❌ إلغاء", command=date_window.destroy,
                     bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                     padx=20, pady=10, relief=tk.RAISED, bd=3).pack(side=tk.RIGHT, padx=10)

            # تركيز على أول عنصر
            year_combo.focus_set()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة اختيار التاريخ: {str(e)}")

    def preview_leave_data(self):
        """معاينة بيانات الإجازة قبل التقديم"""
        try:
            emp_id = self.emp_id_var.get()
            emp_name = self.emp_name_var.get()
            leave_type = self.leave_type_var.get()
            start_date = self.start_date_var.get()
            end_date = self.end_date_var.get()
            leave_reason = self.leave_reason_text.get("1.0", tk.END).strip()
            leave_days = self.leave_days_var.get()

            if not emp_id:
                messagebox.showwarning("تحذير", "الرجاء اختيار موظف أولاً")
                return

            # إنشاء نافذة المعاينة
            preview_window = tk.Toplevel(self.window)
            preview_window.title("👁️ معاينة بيانات الإجازة")
            preview_window.geometry("500x600")
            preview_window.configure(bg="#f8f9fa")
            preview_window.resizable(False, False)

            # توسيط النافذة
            preview_window.transient(self.window)
            preview_window.grab_set()

            # عنوان النافذة
            title_frame = tk.Frame(preview_window, bg="#3498db", height=60)
            title_frame.pack(fill=tk.X)
            title_frame.pack_propagate(False)

            tk.Label(title_frame, text="👁️ معاينة بيانات الإجازة",
                    font=("Arial", 16, "bold"), bg="#3498db", fg="white").pack(expand=True)

            # إطار المحتوى
            content_frame = tk.Frame(preview_window, bg="#f8f9fa")
            content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # معلومات الموظف
            emp_frame = tk.LabelFrame(content_frame, text="👤 معلومات الموظف",
                                     font=("Arial", 12, "bold"), fg="#2c3e50", bg="#f8f9fa")
            emp_frame.pack(fill=tk.X, pady=10)

            emp_info = f"""
الرقم الوظيفي: {emp_id}
اسم الموظف: {emp_name or 'غير محدد'}
نوع الموظف: {self.emp_type_var.get() or 'غير محدد'}
الرصيد المتبقي: {self.balance_var.get() or 'غير محدد'}
            """

            tk.Label(emp_frame, text=emp_info, font=("Arial", 11), bg="#f8f9fa",
                    fg="#2c3e50", justify=tk.LEFT).pack(padx=15, pady=10)

            # تفاصيل الإجازة
            leave_frame = tk.LabelFrame(content_frame, text="📋 تفاصيل الإجازة",
                                       font=("Arial", 12, "bold"), fg="#2c3e50", bg="#f8f9fa")
            leave_frame.pack(fill=tk.X, pady=10)

            leave_info = f"""
نوع الإجازة: {leave_type or 'غير محدد'}
تاريخ البدء: {start_date or 'غير محدد'}
تاريخ الانتهاء: {end_date or 'غير محدد'}
عدد أيام العمل: {leave_days or 'غير محسوب'}
            """

            tk.Label(leave_frame, text=leave_info, font=("Arial", 11), bg="#f8f9fa",
                    fg="#2c3e50", justify=tk.LEFT).pack(padx=15, pady=10)

            # سبب الإجازة
            reason_frame = tk.LabelFrame(content_frame, text="📝 سبب الإجازة",
                                        font=("Arial", 12, "bold"), fg="#2c3e50", bg="#f8f9fa")
            reason_frame.pack(fill=tk.X, pady=10)

            reason_text = tk.Text(reason_frame, height=4, font=("Arial", 11),
                                 wrap=tk.WORD, state=tk.DISABLED, bg="white")
            reason_text.pack(fill=tk.X, padx=15, pady=10)

            reason_text.config(state=tk.NORMAL)
            reason_text.insert("1.0", leave_reason or "لم يتم إدخال سبب")
            reason_text.config(state=tk.DISABLED)

            # أزرار التحكم
            buttons_frame = tk.Frame(content_frame, bg="#f8f9fa")
            buttons_frame.pack(fill=tk.X, pady=20)

            tk.Button(buttons_frame, text="✅ تقديم الطلب",
                     command=lambda: [preview_window.destroy(), self.submit_leave_request()],
                     bg="#27ae60", fg="white", font=("Arial", 12, "bold"),
                     padx=20, pady=10).pack(side=tk.LEFT, padx=10)

            tk.Button(buttons_frame, text="❌ إغلاق", command=preview_window.destroy,
                     bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                     padx=20, pady=10).pack(side=tk.RIGHT, padx=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في معاينة البيانات: {str(e)}")

    def show_leave_help(self):
        """عرض مساعدة استخدام نظام الإجازات"""
        help_text = """
📖 دليل استخدام نظام الإجازات

👤 اختيار الموظف:
• اختر الرقم الوظيفي من القائمة المنسدلة
• ستظهر معلومات الموظف تلقائياً
• تحقق من نوع الموظف والرصيد المتبقي

🏷️ نوع الإجازة:
• إجازة سنوية: للراحة والاستجمام
• إجازة مرضية: للعلاج والتعافي
• إجازة طارئة: للظروف الطارئة
• إجازة أمومة: للأمهات الجدد
• إجازة بدون راتب: بدون راتب

📅 اختيار التواريخ:
• اضغط على زر "📅 اختيار" لفتح نافذة التاريخ
• اختر السنة والشهر واليوم
• راجع المعاينة قبل التأكيد
• استخدم الأزرار السريعة للاختيار السريع

🧮 حساب الأيام:
• اضغط "حساب أيام العمل" بعد اختيار التواريخ
• يتم استثناء أيام الجمعة والسبت تلقائياً
• تحقق من كفاية الرصيد للموظفين

📝 سبب الإجازة:
• أدخل سبباً واضحاً ومفصلاً للإجازة
• السبب مطلوب لجميع أنواع الإجازات

⚡ العمليات:
• "معاينة البيانات": لمراجعة البيانات قبل التقديم
• "تقديم الطلب": لحفظ طلب الإجازة
• "مسح الحقول": لمسح جميع البيانات
• "عرض إجازات الموظف": لمراجعة الإجازات السابقة

💡 نصائح:
• تأكد من صحة جميع البيانات قبل التقديم
• راجع الرصيد المتبقي للموظفين
• استخدم المعاينة للتحقق من البيانات
• احفظ نسخة من طلب الإجازة للمراجعة

🎯 للمساعدة الإضافية، راجع دليل المستخدم أو اتصل بالدعم الفني.
        """

        messagebox.showinfo("📖 مساعدة نظام الإجازات", help_text)

    def calculate_leave_days(self):
        """حساب أيام الإجازة"""
        try:
            start_date = self.start_date_var.get()
            end_date = self.end_date_var.get()

            if start_date and end_date:
                working_days = self.calculate_working_days(start_date, end_date)
                self.leave_days_var.set(f"{working_days} يوم")

                # التحقق من كفاية الرصيد للموظفين
                emp_id = self.emp_id_var.get()
                if emp_id:
                    emp_type = self.get_employee_type(emp_id)
                    if emp_type == "employee":
                        balance = self.get_employee_balance(emp_id)
                        if working_days > balance['remaining']:
                            messagebox.showwarning("تحذير",
                                f"عدد أيام الإجازة ({working_days}) أكبر من الرصيد المتبقي ({balance['remaining']})")
            else:
                messagebox.showwarning("تحذير", "الرجاء إدخال تاريخ البدء والانتهاء")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حساب الأيام: {str(e)}")

    def submit_leave_request(self):
        """تقديم طلب الإجازة"""
        try:
            # التحقق من البيانات المطلوبة
            emp_id = self.emp_id_var.get()
            emp_name = self.emp_name_var.get()
            leave_type = self.leave_type_var.get()
            start_date = self.start_date_var.get()
            end_date = self.end_date_var.get()
            leave_reason = self.leave_reason_text.get("1.0", tk.END).strip()

            if not all([emp_id, leave_type, start_date, end_date]):
                messagebox.showwarning("تحذير", "الرجاء ملء جميع الحقول المطلوبة")
                return

            # حساب أيام العمل
            working_days = self.calculate_working_days(start_date, end_date)

            # إنشاء طلب الإجازة
            leave_request = {
                "الرقم الوظيفي": emp_id,
                "اسم الموظف": emp_name,
                "نوع الإجازة": leave_type,
                "تاريخ البدء": start_date,
                "تاريخ الانتهاء": end_date,
                "عدد أيام العمل": working_days,
                "سبب الإجازة": leave_reason,
                "الحالة": "في الانتظار",
                "تاريخ التقديم": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "نوع الموظف": self.get_employee_type(emp_id)
            }

            # إضافة الطلب للبيانات
            self.leaves_data.append(leave_request)

            # حفظ البيانات
            self.save_leaves_data()

            messagebox.showinfo("نجح", f"تم تقديم طلب الإجازة بنجاح\nرقم الطلب: {len(self.leaves_data)}")

            # مسح النموذج
            self.clear_leave_form()

            # تحديث جدول الموافقة
            if hasattr(self, 'pending_tree'):
                self.refresh_pending_leaves()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تقديم الطلب: {str(e)}")

    def clear_leave_form(self):
        """مسح نموذج الإجازة"""
        self.emp_id_var.set("")
        self.emp_name_var.set("")
        self.emp_type_var.set("")
        self.balance_var.set("")
        self.leave_type_var.set("")
        self.start_date_var.set(datetime.now().strftime("%Y-%m-%d"))
        self.end_date_var.set("")
        self.leave_days_var.set("")
        self.leave_reason_text.delete("1.0", tk.END)

    def show_employee_leaves(self):
        """عرض إجازات الموظف"""
        emp_id = self.emp_id_var.get()
        if not emp_id:
            messagebox.showwarning("تحذير", "الرجاء اختيار موظف أولاً")
            return

        # البحث عن إجازات الموظف
        employee_leaves = [leave for leave in self.leaves_data
                          if str(leave.get("الرقم الوظيفي", "")) == str(emp_id)]

        if not employee_leaves:
            messagebox.showinfo("معلومات", "لا توجد إجازات مسجلة لهذا الموظف")
            return

        # إنشاء نافذة عرض الإجازات
        self.create_employee_leaves_window(employee_leaves)

    def create_employee_leaves_window(self, employee_leaves):
        """إنشاء نافذة عرض إجازات الموظف"""
        leaves_window = tk.Toplevel(self.window)
        leaves_window.title(f"إجازات الموظف - {self.emp_name_var.get()}")
        leaves_window.geometry("1000x600")
        leaves_window.configure(bg="#f0f8ff")

        # عنوان النافذة
        title_label = tk.Label(leaves_window, text=f"📋 إجازات الموظف: {self.emp_name_var.get()}",
                              font=("Arial", 16, "bold"), fg="#2c3e50", bg="#f0f8ff")
        title_label.pack(pady=10)

        # إطار الجدول
        table_frame = tk.Frame(leaves_window)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # جدول الإجازات
        columns = ("نوع الإجازة", "تاريخ البدء", "تاريخ الانتهاء", "عدد الأيام", "الحالة", "تاريخ التقديم")
        leaves_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # تعريف العناوين
        for col in columns:
            leaves_tree.heading(col, text=col)
            leaves_tree.column(col, width=150, anchor="center")

        # إضافة البيانات
        for leave in employee_leaves:
            leaves_tree.insert("", tk.END, values=(
                leave.get("نوع الإجازة", ""),
                leave.get("تاريخ البدء", ""),
                leave.get("تاريخ الانتهاء", ""),
                leave.get("عدد أيام العمل", ""),
                leave.get("الحالة", ""),
                leave.get("تاريخ التقديم", "")
            ))

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=leaves_tree.yview)
        leaves_tree.configure(yscrollcommand=scrollbar.set)

        leaves_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # زر إغلاق
        tk.Button(leaves_window, text="❌ إغلاق", command=leaves_window.destroy,
                 bg="#e74c3c", fg="white", font=("Arial", 12, "bold"), padx=20, pady=10).pack(pady=10)

    def save_leaves_data(self):
        """حفظ بيانات الإجازات"""
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = "إجازات_القسم"

            # العناوين
            headers = [
                "الرقم الوظيفي", "اسم الموظف", "نوع الإجازة", "تاريخ البدء",
                "تاريخ الانتهاء", "عدد أيام العمل", "سبب الإجازة", "الحالة",
                "تاريخ التقديم", "نوع الموظف", "تاريخ الموافقة", "ملاحظات"
            ]
            ws.append(headers)

            # البيانات
            for leave in self.leaves_data:
                row = [
                    leave.get("الرقم الوظيفي", ""),
                    leave.get("اسم الموظف", ""),
                    leave.get("نوع الإجازة", ""),
                    leave.get("تاريخ البدء", ""),
                    leave.get("تاريخ الانتهاء", ""),
                    leave.get("عدد أيام العمل", ""),
                    leave.get("سبب الإجازة", ""),
                    leave.get("الحالة", ""),
                    leave.get("تاريخ التقديم", ""),
                    leave.get("نوع الموظف", ""),
                    leave.get("تاريخ الموافقة", ""),
                    leave.get("ملاحظات", "")
                ]
                ws.append(row)

            wb.save("department_leaves_data.xlsx")
            print("✅ قسم الإجازات: تم حفظ بيانات الإجازات")

        except Exception as e:
            print(f"❌ قسم الإجازات: خطأ في حفظ بيانات الإجازات: {e}")

    def refresh_pending_leaves(self):
        """تحديث قائمة الإجازات المعلقة"""
        if not hasattr(self, 'pending_tree'):
            return

        # مسح البيانات الحالية
        for item in self.pending_tree.get_children():
            self.pending_tree.delete(item)

        # إضافة الإجازات المعلقة
        pending_count = 0
        for leave in self.leaves_data:
            if leave.get("الحالة") == "في الانتظار":
                emp_id = leave.get("الرقم الوظيفي", "")
                emp_name = leave.get("اسم الموظف", "")

                # البحث عن اسم الموظف من بيانات الموظفين إذا لم يكن موجود
                if not emp_name or emp_name == "":
                    for emp in self.employees_data:
                        if str(emp.get("الرقم الوظيفي", "")) == str(emp_id):
                            emp_name = emp.get("الاسم العربي", "") or emp.get("الاسم", "") or "غير محدد"
                            break
                    if not emp_name:
                        emp_name = "موظف غير معروف"

                emp_type = self.get_employee_type(emp_id)

                # الحصول على الرصيد المتبقي مع تفاصيل أكثر
                if emp_type == "employee":
                    balance = self.get_employee_balance(emp_id)
                    if balance['remaining'] >= 0:
                        remaining_balance = f"{balance['remaining']} يوم متبقي"
                    else:
                        remaining_balance = f"عجز {abs(balance['remaining'])} يوم"
                else:
                    remaining_balance = "غير محدود"

                # تحديد نوع الموظف بالعربية مع المسمى الوظيفي
                job_title = ""
                for emp in self.employees_data:
                    if str(emp.get("الرقم الوظيفي", "")) == str(emp_id):
                        job_title = emp.get("المسمى الوظيفي", "") or emp.get("الوظيفة", "") or emp.get("المنصب", "")
                        break

                if emp_type == "teacher":
                    emp_type_ar = f"معلم/معلمة"
                    if job_title:
                        emp_type_ar += f" ({job_title})"
                else:
                    emp_type_ar = f"موظف/موظفة"
                    if job_title:
                        emp_type_ar += f" ({job_title})"

                # تنسيق التواريخ
                start_date = leave.get("تاريخ البدء", "")
                end_date = leave.get("تاريخ الانتهاء", "")

                # محاولة تنسيق التواريخ
                try:
                    if start_date:
                        start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
                        start_date = start_date_obj.strftime("%Y-%m-%d")
                except:
                    pass

                try:
                    if end_date:
                        end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
                        end_date = end_date_obj.strftime("%Y-%m-%d")
                except:
                    pass

                self.pending_tree.insert("", tk.END, values=(
                    emp_id,
                    emp_name,
                    emp_type_ar,
                    leave.get("نوع الإجازة", ""),
                    start_date,
                    end_date,
                    f"{leave.get('عدد أيام العمل', 0)} يوم",
                    remaining_balance,
                    leave.get("الحالة", "")
                ))
                pending_count += 1

        print(f"✅ قسم الإجازات: تم تحديث {pending_count} إجازة معلقة")

    def approve_selected_leave(self):
        """الموافقة على الإجازة المحددة"""
        if not hasattr(self, 'pending_tree'):
            return

        selected_item = self.pending_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "الرجاء تحديد إجازة للموافقة عليها")
            return

        # الحصول على بيانات الإجازة المحددة
        item_values = self.pending_tree.item(selected_item[0])["values"]
        emp_id = item_values[0]
        emp_name = item_values[1]
        start_date = item_values[4]
        working_days = int(item_values[6])

        # البحث عن الإجازة في البيانات
        for leave in self.leaves_data:
            if (leave.get("الرقم الوظيفي") == emp_id and
                leave.get("تاريخ البدء") == start_date and
                leave.get("الحالة") == "في الانتظار"):

                # تحديد نوع الموظف
                emp_type = self.get_employee_type(emp_id)

                # للموظفين: التحقق من الرصيد وخصم الأيام
                if emp_type == "employee":
                    balance = self.get_employee_balance(emp_id)
                    if working_days > balance['remaining']:
                        messagebox.showerror("خطأ",
                            f"الرصيد المتبقي ({balance['remaining']} يوم) غير كافي للإجازة ({working_days} يوم)")
                        return

                    # خصم الأيام من الرصيد
                    self.deduct_from_balance(emp_id, working_days)

                # تحديث حالة الإجازة
                leave["الحالة"] = "موافق عليها"
                leave["تاريخ الموافقة"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # حفظ البيانات
                self.save_leaves_data()

                messagebox.showinfo("نجح", f"تمت الموافقة على إجازة {emp_name}")

                # تحديث الجدول
                self.refresh_pending_leaves()
                break

    def reject_selected_leave(self):
        """رفض الإجازة المحددة"""
        if not hasattr(self, 'pending_tree'):
            return

        selected_item = self.pending_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "الرجاء تحديد إجازة لرفضها")
            return

        # طلب سبب الرفض
        reason = simpledialog.askstring("سبب الرفض", "الرجاء إدخال سبب رفض الإجازة:")
        if not reason:
            return

        # الحصول على بيانات الإجازة المحددة
        item_values = self.pending_tree.item(selected_item[0])["values"]
        emp_id = item_values[0]
        emp_name = item_values[1]
        start_date = item_values[4]

        # البحث عن الإجازة في البيانات
        for leave in self.leaves_data:
            if (leave.get("الرقم الوظيفي") == emp_id and
                leave.get("تاريخ البدء") == start_date and
                leave.get("الحالة") == "في الانتظار"):

                # تحديث حالة الإجازة
                leave["الحالة"] = "مرفوضة"
                leave["تاريخ الرفض"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                leave["سبب الرفض"] = reason

                # حفظ البيانات
                self.save_leaves_data()

                messagebox.showinfo("تم", f"تم رفض إجازة {emp_name}")

                # تحديث الجدول
                self.refresh_pending_leaves()
                break

    def add_leave_note(self):
        """إضافة ملاحظة للإجازة"""
        if not hasattr(self, 'pending_tree'):
            return

        selected_item = self.pending_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "الرجاء تحديد إجازة لإضافة ملاحظة")
            return

        # طلب الملاحظة
        note = simpledialog.askstring("إضافة ملاحظة", "الرجاء إدخال الملاحظة:")
        if not note:
            return

        # الحصول على بيانات الإجازة المحددة
        item_values = self.pending_tree.item(selected_item[0])["values"]
        emp_id = item_values[0]
        start_date = item_values[4]

        # البحث عن الإجازة في البيانات
        for leave in self.leaves_data:
            if (leave.get("الرقم الوظيفي") == emp_id and
                leave.get("تاريخ البدء") == start_date):

                # إضافة الملاحظة
                existing_notes = leave.get("ملاحظات", "")
                new_note = f"{datetime.now().strftime('%Y-%m-%d %H:%M')}: {note}"

                if existing_notes:
                    leave["ملاحظات"] = f"{existing_notes}\n{new_note}"
                else:
                    leave["ملاحظات"] = new_note

                # حفظ البيانات
                self.save_leaves_data()

                messagebox.showinfo("تم", "تمت إضافة الملاحظة بنجاح")
                break

    def deduct_from_balance(self, emp_id, days):
        """خصم أيام من رصيد الموظف"""
        for balance in self.balance_data:
            if str(balance.get("الرقم الوظيفي", "")) == str(emp_id):
                current_used = balance.get("الإجازات المأخوذة", 0)
                try:
                    new_used = int(current_used) + days
                    balance["الإجازات المأخوذة"] = new_used
                    balance["تاريخ آخر تحديث"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    print(f"✅ قسم الإجازات: تم خصم {days} يوم من رصيد الموظف {emp_id}")
                except:
                    balance["الإجازات المأخوذة"] = days
                break

    def generate_comprehensive_report(self):
        """إنشاء تقرير شامل للإجازات"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"تقرير_قسم_الإجازات_شامل_{timestamp}.xlsx"

            export_file = filedialog.asksaveasfilename(
                title="حفظ التقرير الشامل لقسم الإجازات",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialfile=filename,
                initialdir=os.path.expanduser("~/Desktop")
            )

            if export_file:
                self.create_department_report(export_file)
                messagebox.showinfo("نجح", f"تم إنشاء التقرير الشامل:\n{export_file}")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء التقرير: {str(e)}")

    def generate_employee_report(self):
        """تقرير حسب الموظف"""
        # نافذة اختيار الموظف
        emp_window = tk.Toplevel(self.window)
        emp_window.title("اختيار الموظف")
        emp_window.geometry("400x200")
        emp_window.configure(bg="#f0f8ff")

        tk.Label(emp_window, text="اختر الموظف:", font=("Arial", 12, "bold")).pack(pady=10)

        emp_var = tk.StringVar()
        emp_combo = ttk.Combobox(emp_window, textvariable=emp_var, width=30)
        emp_combo['values'] = [f"{emp.get('الرقم الوظيفي')} - {emp.get('الاسم العربي')}"
                              for emp in self.employees_data]
        emp_combo.pack(pady=10)

        def generate_report():
            selected = emp_var.get()
            if selected:
                emp_id = selected.split(" - ")[0]
                self.create_employee_specific_report(emp_id)
                emp_window.destroy()

        tk.Button(emp_window, text="إنشاء التقرير", command=generate_report,
                 bg="#27ae60", fg="white", font=("Arial", 11, "bold")).pack(pady=20)

    def generate_statistics_report(self):
        """إنشاء تقرير الإحصائيات"""
        stats = self.calculate_leave_statistics()

        report_text = f"""📊 إحصائيات قسم الإجازات
{'='*50}

📋 إجمالي الإجازات: {stats['total_leaves']}
✅ الموافق عليها: {stats['approved_leaves']}
❌ المرفوضة: {stats['rejected_leaves']}
⏳ في الانتظار: {stats['pending_leaves']}

👥 إحصائيات الموظفين:
• الموظفين: {stats['employees_count']}
• المعلمين: {stats['teachers_count']}

📊 أنواع الإجازات:
"""

        for leave_type, count in stats['leave_types'].items():
            report_text += f"• {leave_type}: {count}\n"

        report_text += f"""
💰 إحصائيات الأرصدة:
• متوسط الرصيد المتبقي: {stats['avg_remaining_balance']:.1f} يوم
• إجمالي الأيام المستخدمة: {stats['total_used_days']} يوم

📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        if hasattr(self, 'report_preview'):
            self.report_preview.delete("1.0", tk.END)
            self.report_preview.insert("1.0", report_text)

    def calculate_leave_statistics(self):
        """حساب إحصائيات الإجازات"""
        stats = {
            'total_leaves': len(self.leaves_data),
            'approved_leaves': 0,
            'rejected_leaves': 0,
            'pending_leaves': 0,
            'employees_count': 0,
            'teachers_count': 0,
            'leave_types': {},
            'total_used_days': 0,
            'avg_remaining_balance': 0
        }

        # إحصائيات الإجازات
        for leave in self.leaves_data:
            status = leave.get("الحالة", "")
            if status == "موافق عليها":
                stats['approved_leaves'] += 1
                stats['total_used_days'] += int(leave.get("عدد أيام العمل", 0))
            elif status == "مرفوضة":
                stats['rejected_leaves'] += 1
            elif status == "في الانتظار":
                stats['pending_leaves'] += 1

            # أنواع الإجازات
            leave_type = leave.get("نوع الإجازة", "غير محدد")
            stats['leave_types'][leave_type] = stats['leave_types'].get(leave_type, 0) + 1

        # إحصائيات الموظفين
        for emp in self.employees_data:
            emp_type = self.get_employee_type(emp.get("الرقم الوظيفي"))
            if emp_type == "teacher":
                stats['teachers_count'] += 1
            else:
                stats['employees_count'] += 1

        # متوسط الرصيد المتبقي
        total_remaining = 0
        employee_count = 0
        for emp in self.employees_data:
            emp_type = self.get_employee_type(emp.get("الرقم الوظيفي"))
            if emp_type == "employee":
                balance = self.get_employee_balance(emp.get("الرقم الوظيفي"))
                total_remaining += balance['remaining']
                employee_count += 1

        if employee_count > 0:
            stats['avg_remaining_balance'] = total_remaining / employee_count

        return stats

    def save_settings_changes(self):
        """حفظ تغييرات الإعدادات"""
        try:
            # تحديث أيام العطل
            weekend_days = [day for day, var in self.weekend_vars.items() if var.get()]
            self.settings["weekend_days"] = weekend_days

            # تحديث إعدادات التنبيهات
            self.settings["alert_days_before"] = int(self.alert_days_var.get())

            # حفظ الإعدادات
            self.save_settings()

            messagebox.showinfo("نجح", "تم حفظ إعدادات قسم الإجازات بنجاح")

        except ValueError:
            messagebox.showerror("خطأ", "الرجاء إدخال قيم صحيحة للأرقام")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ الإعدادات: {str(e)}")

    def create_department_report(self, file_path):
        """إنشاء تقرير قسم الإجازات"""
        wb = Workbook()

        # ورقة الإجازات
        ws_leaves = wb.active
        ws_leaves.title = "إجازات_القسم"

        # عناوين ورقة الإجازات
        leave_headers = [
            "الرقم الوظيفي", "اسم الموظف", "نوع الموظف", "نوع الإجازة",
            "تاريخ البدء", "تاريخ الانتهاء", "عدد أيام العمل", "سبب الإجازة",
            "الحالة", "تاريخ التقديم", "تاريخ الموافقة", "ملاحظات"
        ]
        ws_leaves.append(leave_headers)

        # بيانات الإجازات
        for leave in self.leaves_data:
            emp_type = "معلم/معلمة" if self.get_employee_type(leave.get("الرقم الوظيفي")) == "teacher" else "موظف/موظفة"
            row = [
                leave.get("الرقم الوظيفي", ""),
                leave.get("اسم الموظف", ""),
                emp_type,
                leave.get("نوع الإجازة", ""),
                leave.get("تاريخ البدء", ""),
                leave.get("تاريخ الانتهاء", ""),
                leave.get("عدد أيام العمل", ""),
                leave.get("سبب الإجازة", ""),
                leave.get("الحالة", ""),
                leave.get("تاريخ التقديم", ""),
                leave.get("تاريخ الموافقة", ""),
                leave.get("ملاحظات", "")
            ]
            ws_leaves.append(row)

        # ورقة الإحصائيات
        ws_stats = wb.create_sheet("إحصائيات_القسم")
        stats = self.calculate_leave_statistics()

        stats_data = [
            ["نوع الإحصائية", "القيمة"],
            ["إجمالي الإجازات", stats['total_leaves']],
            ["الموافق عليها", stats['approved_leaves']],
            ["المرفوضة", stats['rejected_leaves']],
            ["في الانتظار", stats['pending_leaves']],
            ["عدد الموظفين", stats['employees_count']],
            ["عدد المعلمين", stats['teachers_count']],
            ["متوسط الرصيد المتبقي", f"{stats['avg_remaining_balance']:.1f}"],
            ["إجمالي الأيام المستخدمة", stats['total_used_days']],
            ["تاريخ التقرير", datetime.now().strftime("%Y-%m-%d %H:%M:%S")]
        ]

        for row in stats_data:
            ws_stats.append(row)

        # تنسيق الجداول
        self.format_worksheet(ws_leaves)
        self.format_worksheet(ws_stats)

        wb.save(file_path)

    def format_worksheet(self, worksheet):
        """تنسيق ورقة العمل"""
        try:
            # تنسيق العناوين
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

            for cell in worksheet[1]:
                if cell.value:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = Alignment(horizontal="center", vertical="center")

            # ضبط عرض الأعمدة
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 30)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        except Exception as e:
            print(f"تحذير: فشل في تنسيق الجدول: {e}")

    def create_employee_specific_report(self, emp_id):
        """إنشاء تقرير خاص بموظف"""
        try:
            # البحث عن الموظف
            emp_data = None
            for emp in self.employees_data:
                if str(emp.get("الرقم الوظيفي")) == str(emp_id):
                    emp_data = emp
                    break

            if not emp_data:
                messagebox.showerror("خطأ", "الموظف غير موجود")
                return

            # إنشاء التقرير
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"تقرير_موظف_قسم_الإجازات_{emp_id}_{timestamp}.xlsx"

            export_file = filedialog.asksaveasfilename(
                title="حفظ تقرير الموظف",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialfile=filename,
                initialdir=os.path.expanduser("~/Desktop")
            )

            if export_file:
                self.create_employee_report_file(emp_data, export_file)
                messagebox.showinfo("نجح", f"تم إنشاء تقرير الموظف:\n{export_file}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء تقرير الموظف: {str(e)}")

    def create_employee_report_file(self, emp_data, file_path):
        """إنشاء ملف تقرير الموظف"""
        wb = Workbook()
        ws = wb.active
        ws.title = "تقرير_الموظف"

        # معلومات الموظف
        emp_info = [
            ["تقرير إجازات الموظف - قسم الإجازات", ""],
            ["", ""],
            ["معلومات الموظف", ""],
            ["الرقم الوظيفي", emp_data.get("الرقم الوظيفي", "")],
            ["الاسم", emp_data.get("الاسم العربي", "")],
            ["المسمى الوظيفي", emp_data.get("المسمى الوظيفي", "")],
            ["مكان العمل", emp_data.get("مكان العمل الحالي", "")],
            ["", ""],
        ]

        for row in emp_info:
            ws.append(row)

        # إجازات الموظف
        emp_id = emp_data.get("الرقم الوظيفي")
        employee_leaves = [leave for leave in self.leaves_data
                          if str(leave.get("الرقم الوظيفي")) == str(emp_id)]

        ws.append(["إجازات الموظف", ""])
        leave_headers = ["نوع الإجازة", "تاريخ البدء", "تاريخ الانتهاء", "عدد الأيام", "الحالة"]
        ws.append(leave_headers)

        for leave in employee_leaves:
            row = [
                leave.get("نوع الإجازة", ""),
                leave.get("تاريخ البدء", ""),
                leave.get("تاريخ الانتهاء", ""),
                leave.get("عدد أيام العمل", ""),
                leave.get("الحالة", "")
            ]
            ws.append(row)

        # معلومات الرصيد
        emp_type = self.get_employee_type(emp_id)
        if emp_type == "employee":
            balance = self.get_employee_balance(emp_id)
            ws.append(["", ""])
            ws.append(["معلومات الرصيد", ""])
            ws.append(["الرصيد الإجمالي", balance['total']])
            ws.append(["الإجازات المأخوذة", balance['used']])
            ws.append(["الرصيد المتبقي", balance['remaining']])
        else:
            ws.append(["", ""])
            ws.append(["نوع الموظف", "معلم/معلمة - رصيد غير محدود"])

        self.format_worksheet(ws)
        wb.save(file_path)
