#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح ملف الترقيات
Test Fix Promotions File
"""

import sys
import datetime
import os

def test_promotions_file_creation():
    """اختبار إنشاء ملف الترقيات"""
    print("📋 اختبار إصلاح ملف الترقيات")
    print("=" * 60)
    
    test_results = {
        'total_tests': 0,
        'passed_tests': 0,
        'failed_tests': 0
    }
    
    # اختبار 1: استيراد النظام
    print("\n🧪 اختبار 1: استيراد النظام المحدث")
    print("-" * 50)
    
    try:
        import promotion_system_safe
        print("✅ تم استيراد النظام بنجاح")
        test_results['passed_tests'] += 1
    except ImportError as e:
        print(f"❌ فشل في الاستيراد: {e}")
        test_results['failed_tests'] += 1
    test_results['total_tests'] += 1
    
    # اختبار 2: فحص الدوال الجديدة
    print("\n🧪 اختبار 2: فحص الدوال الجديدة")
    print("-" * 50)
    
    try:
        from promotion_system_safe import PromotionSystem
        
        new_methods = [
            'force_create_promotions_file',
            'force_create_and_open'
        ]
        
        for method_name in new_methods:
            if hasattr(PromotionSystem, method_name):
                print(f"✅ دالة {method_name}: موجودة")
                test_results['passed_tests'] += 1
            else:
                print(f"❌ دالة {method_name}: مفقودة")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
    
    except Exception as e:
        print(f"❌ خطأ في فحص الدوال: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 3: إنشاء ملف الترقيات بقوة
    print("\n🧪 اختبار 3: إنشاء ملف الترقيات بقوة")
    print("-" * 50)
    
    try:
        import tkinter as tk
        from promotion_system_safe import PromotionSystem
        
        # إنشاء نافذة وهمية للاختبار
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # إنشاء نظام الترقيات
        promotion_sys = PromotionSystem(root)
        
        # حذف الملف إذا كان موجود للاختبار
        if os.path.exists(promotion_sys.promotions_file):
            os.remove(promotion_sys.promotions_file)
            print(f"🗑️ تم حذف الملف السابق: {promotion_sys.promotions_file}")
        
        # إنشاء الملف بقوة
        success = promotion_sys.force_create_promotions_file()
        
        if success:
            print("✅ تم إنشاء الملف بقوة بنجاح")
            test_results['passed_tests'] += 1
        else:
            print("❌ فشل في إنشاء الملف بقوة")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
        
        # فحص وجود الملف
        if os.path.exists(promotion_sys.promotions_file):
            print(f"✅ الملف موجود: {promotion_sys.promotions_file}")
            test_results['passed_tests'] += 1
            
            # فحص حجم الملف
            file_size = os.path.getsize(promotion_sys.promotions_file)
            if file_size > 0:
                print(f"✅ حجم الملف: {file_size} بايت")
                test_results['passed_tests'] += 1
            else:
                print("❌ الملف فارغ")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
        else:
            print("❌ الملف غير موجود")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
        
        root.destroy()
    
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء الملف: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 4: قراءة محتوى الملف
    print("\n🧪 اختبار 4: قراءة محتوى الملف")
    print("-" * 50)
    
    try:
        from openpyxl import load_workbook
        
        # قراءة الملف المنشأ
        promotions_file = "ترقيات.xlsx"
        if os.path.exists(promotions_file):
            wb = load_workbook(promotions_file)
            ws = wb.active
            
            # فحص العناوين
            headers_count = 0
            for cell in ws[1]:
                if cell.value:
                    headers_count += 1
                else:
                    break
            
            expected_headers = 19  # عدد العناوين المتوقع
            if headers_count == expected_headers:
                print(f"✅ عدد العناوين صحيح: {headers_count}")
                test_results['passed_tests'] += 1
            else:
                print(f"❌ عدد العناوين غير صحيح: {headers_count} (متوقع: {expected_headers})")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
            
            # فحص الرسالة التوضيحية
            message_cell = ws.cell(row=2, column=1)
            if message_cell.value and "ملف الترقيات جاهز" in str(message_cell.value):
                print("✅ الرسالة التوضيحية موجودة")
                test_results['passed_tests'] += 1
            else:
                print("❌ الرسالة التوضيحية مفقودة")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
            
        else:
            print("❌ ملف الترقيات غير موجود للقراءة")
            test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
    
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 5: فحص الأزرار الجديدة
    print("\n🧪 اختبار 5: فحص الأزرار الجديدة")
    print("-" * 50)
    
    try:
        import tkinter as tk
        from promotion_system_safe import PromotionSystem
        
        # إنشاء نافذة وهمية للاختبار
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # إنشاء نظام الترقيات
        promotion_sys = PromotionSystem(root)
        
        # فحص عدد الأزرار (يجب أن يكون 7 أزرار الآن)
        buttons_frame = tk.Frame(root)
        promotion_sys.create_control_buttons(buttons_frame)
        
        button_count = len(buttons_frame.winfo_children())
        expected_buttons = 7  # 7 أزرار الآن
        
        if button_count == expected_buttons:
            print(f"✅ عدد الأزرار صحيح: {button_count}")
            test_results['passed_tests'] += 1
        else:
            print(f"❌ عدد الأزرار غير صحيح: {button_count} (متوقع: {expected_buttons})")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
        
        root.destroy()
    
    except Exception as e:
        print(f"❌ خطأ في فحص الأزرار: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    return test_results

def main():
    """تشغيل جميع الاختبارات"""
    print("📋 اختبار إصلاح ملف الترقيات")
    print(f"📅 التاريخ: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 إصدار Python: {sys.version}")
    print()
    
    # تشغيل الاختبارات
    results = test_promotions_file_creation()
    
    # عرض النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print(f"   📈 إجمالي الاختبارات: {results['total_tests']}")
    print(f"   ✅ نجح: {results['passed_tests']}")
    print(f"   ❌ فشل: {results['failed_tests']}")
    
    success_rate = (results['passed_tests'] / results['total_tests']) * 100 if results['total_tests'] > 0 else 0
    print(f"   📊 معدل النجاح: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("\n🎉 ممتاز! إصلاح ملف الترقيات تم بنجاح")
    elif success_rate >= 75:
        print("\n✅ جيد! الإصلاح يعمل مع بعض التحسينات المطلوبة")
    elif success_rate >= 50:
        print("\n⚠️ متوسط! الإصلاح يحتاج إلى تحسينات إضافية")
    else:
        print("\n❌ ضعيف! الإصلاح يحتاج إلى مراجعة شاملة")
    
    print("=" * 60)
    
    # عرض تعليمات الاستخدام
    print("\n📋 كيفية استخدام الإصلاح:")
    print("1. افتح نظام الترقيات")
    print("2. اضغط 'إنشاء ملف الترقيات' لإنشاء الملف بقوة")
    print("3. أو اضغط 'فتح ملف الترقيات' وسيتم إنشاؤه تلقائياً")
    print("4. الملف سيحتوي على جميع العناوين المطلوبة")
    print("5. يمكن تحرير الملف في Excel وتحديث البيانات")
    
    # فحص وجود الملف النهائي
    promotions_file = "ترقيات.xlsx"
    if os.path.exists(promotions_file):
        print(f"\n✅ ملف الترقيات موجود الآن: {promotions_file}")
        file_size = os.path.getsize(promotions_file)
        print(f"📊 حجم الملف: {file_size} بايت")
    else:
        print(f"\n❌ ملف الترقيات غير موجود: {promotions_file}")
    
    print("\n🏁 انتهى اختبار إصلاح ملف الترقيات")

if __name__ == "__main__":
    main()
