#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الأزرار المحسنة في قسم الإجازات
Test Enhanced Buttons in Leave Department
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
from datetime import datetime

def test_enhanced_buttons():
    """اختبار الأزرار المحسنة"""
    print("🔘 اختبار الأزرار المحسنة في قسم الإجازات")
    print("=" * 60)
    
    try:
        # استيراد قسم الإجازات
        print("📦 استيراد قسم الإجازات...")
        import leave_department_system
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة الاختبار...")
        root = tk.Tk()
        root.title("اختبار الأزرار المحسنة - قسم الإجازات")
        root.geometry("1400x900")
        root.configure(bg="#f0f8ff")
        
        # إنشاء قسم الإجازات
        print("🔧 إنشاء قسم الإجازات...")
        leave_dept = leave_department_system.LeaveDepartmentSystem(root)
        print("✅ تم إنشاء قسم الإجازات بنجاح")
        
        # إنشاء واجهة اختبار الأزرار
        create_buttons_test_interface(root, leave_dept)
        
        print("\n🎉 انتهى الاختبار - الواجهة التفاعلية جاهزة")
        print("\n📋 تعليمات الاختبار:")
        print("   • افتح قسم الإجازات وراجع الأزرار المحسنة")
        print("   • جرب جميع الأزرار في التبويبات المختلفة")
        print("   • راجع الأحجام والألوان والوضوح")
        print("   • اختبر أزرار اختيار التاريخ")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def create_buttons_test_interface(root, leave_dept):
    """إنشاء واجهة اختبار الأزرار"""
    
    # إطار الاختبار
    test_frame = tk.Frame(root, bg="#e8f4fd")
    test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
    
    # عنوان
    title_label = tk.Label(test_frame, text="🔘 اختبار الأزرار المحسنة في قسم الإجازات", 
                          bg="#e8f4fd", fg="#1565c0", font=("Arial", 16, "bold"))
    title_label.pack(pady=10)
    
    # معلومات التحسينات
    info_frame = tk.LabelFrame(test_frame, text="تحسينات الأزرار المطبقة", 
                              bg="#e8f4fd", fg="#1565c0", font=("Arial", 12, "bold"))
    info_frame.pack(fill=tk.X, pady=10)
    
    # إحصائيات
    improvements_text = f"""
🔘 تحسينات الأزرار المطبقة:

📏 الأحجام والأبعاد:
• زيادة حجم الخط إلى 14-16 نقطة
• زيادة المساحة الداخلية (padx=20-30, pady=10-15)
• تحديد عرض ثابت للأزرار (width=14-18)
• زيادة سماكة الحدود (bd=3-4)

🎨 التصميم والمظهر:
• تحسين الألوان والتباين
• إضافة تأثير الرفع (relief=tk.RAISED)
• مؤشر اليد عند التمرير (cursor="hand2")
• تباعد أفضل بين الأزرار

📅 أزرار اختيار التاريخ:
• نص أوضح: "📅 اختيار التاريخ"
• حجم أكبر: font=("Arial", 12, "bold")
• عرض ثابت: width=15
• ألوان مميزة (أخضر للبدء، أحمر للانتهاء)

🧮 زر حساب الأيام:
• حجم خط كبير: font=("Arial", 14, "bold")
• مساحة أكبر: padx=20, pady=12
• عرض ثابت: width=20
• لون أزرق مميز

📝 أزرار العمليات الرئيسية:
• حجم خط كبير جداً: font=("Arial", 16, "bold")
• مساحة كبيرة: padx=30, pady=15
• عرض ثابت: width=18
• ألوان واضحة ومميزة

✅ أزرار الموافقة:
• نص أوضح: "✅ موافقة على الإجازة"
• حجم موحد: font=("Arial", 14, "bold")
• عرض ثابت: width=14
• ألوان مناسبة للعمليات

🏷️ أزرار التبويبات:
• حجم خط كبير: font=("Arial", 14, "bold")
• مساحة أكبر: padx=20, pady=12
• عرض ثابت: width=16
• تباعد محسن

📊 البيانات المحملة:
• عدد الموظفين: {len(leave_dept.employees_data)}
• عدد الأرصدة: {len(leave_dept.balance_data)}

🎯 النتيجة: أزرار أكثر وضوحاً وسهولة في الاستخدام!
    """
    
    improvements_label = tk.Label(info_frame, text=improvements_text, bg="#e8f4fd", 
                                 fg="#1565c0", font=("Arial", 10), justify=tk.LEFT)
    improvements_label.pack(padx=10, pady=10)
    
    # متغير لعرض النتائج
    result_var = tk.StringVar()
    result_label = tk.Label(test_frame, textvariable=result_var, bg="#e8f4fd", 
                           fg="#1565c0", font=("Arial", 11, "bold"))
    result_label.pack(pady=5)
    
    # أزرار الاختبار
    buttons_frame = tk.Frame(test_frame, bg="#e8f4fd")
    buttons_frame.pack(pady=15)
    
    def test_leave_department():
        """اختبار فتح قسم الإجازات"""
        try:
            print("\n🏖️ اختبار فتح قسم الإجازات...")
            
            # فتح نافذة قسم الإجازات
            leave_dept.show_leave_department()
            
            result_var.set("🏖️ تم فتح قسم الإجازات - راجع الأزرار المحسنة في جميع التبويبات")
            print("   ✅ تم فتح قسم الإجازات بنجاح")
            print("   📋 راجع الأزرار المحسنة في:")
            print("     • تبويب 'طلب إجازة' - أزرار اختيار التاريخ والعمليات")
            print("     • تبويب 'الموافقة على الإجازات' - أزرار الموافقة والرفض")
            print("     • تبويب 'التقارير' - أزرار التقارير")
            print("     • تبويب 'الإعدادات' - زر حفظ الإعدادات")
            
        except Exception as e:
            print(f"   ❌ خطأ في فتح قسم الإجازات: {e}")
            result_var.set(f"❌ خطأ في فتح القسم: {e}")
    
    def test_date_selection():
        """اختبار أزرار اختيار التاريخ"""
        try:
            print("\n📅 اختبار أزرار اختيار التاريخ...")
            
            # إنشاء متغير تاريخ للاختبار
            test_date_var = tk.StringVar()
            
            # اختبار دالة اختيار التاريخ
            leave_dept.select_date(test_date_var)
            
            result_var.set("📅 تم اختبار أزرار التاريخ - راجع النافذة المحسنة والأزرار الكبيرة")
            print("   ✅ تم استدعاء نافذة اختيار التاريخ")
            print("   📋 راجع التحسينات:")
            print("     • أزرار أكبر وأوضح")
            print("     • ألوان مميزة")
            print("     • نصوص واضحة")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار التاريخ: {e}")
            result_var.set(f"❌ خطأ التاريخ: {e}")
    
    def test_preview_function():
        """اختبار زر المعاينة"""
        try:
            print("\n👁️ اختبار زر معاينة البيانات...")
            
            # اختبار دالة المعاينة
            leave_dept.preview_leave_data()
            
            result_var.set("👁️ تم اختبار زر المعاينة - راجع الأزرار المحسنة في نافذة المعاينة")
            print("   ✅ تم استدعاء نافذة المعاينة")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار المعاينة: {e}")
            result_var.set(f"❌ خطأ المعاينة: {e}")
    
    def test_help_function():
        """اختبار زر المساعدة"""
        try:
            print("\n❓ اختبار زر المساعدة...")
            
            # اختبار دالة المساعدة
            leave_dept.show_leave_help()
            
            result_var.set("❓ تم اختبار زر المساعدة - راجع محتوى المساعدة الشامل")
            print("   ✅ تم استدعاء نافذة المساعدة")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار المساعدة: {e}")
            result_var.set(f"❌ خطأ المساعدة: {e}")
    
    def show_buttons_comparison():
        """عرض مقارنة الأزرار قبل وبعد"""
        comparison = """
🔘 مقارنة الأزرار قبل وبعد التحسين:

📏 الأحجام:
   قبل: font=("Arial", 10-11, "bold")
   بعد: font=("Arial", 14-16, "bold")

📐 المساحة الداخلية:
   قبل: padx=8-15, pady=4-8
   بعد: padx=20-30, pady=10-15

📊 العرض:
   قبل: عرض متغير حسب النص
   بعد: عرض ثابت (width=14-20)

🎨 التصميم:
   قبل: bd=2, relief بسيط
   بعد: bd=3-4, relief=tk.RAISED

🖱️ التفاعل:
   قبل: مؤشر عادي
   بعد: cursor="hand2"

📅 أزرار التاريخ:
   قبل: "📅" فقط
   بعد: "📅 اختيار التاريخ"

✅ أزرار الموافقة:
   قبل: "✅ موافقة"
   بعد: "✅ موافقة على الإجازة"

🧮 زر الحساب:
   قبل: font=("Arial", 12, "bold")
   بعد: font=("Arial", 14, "bold"), width=20

📝 الأزرار الرئيسية:
   قبل: font=("Arial", 13, "bold")
   بعد: font=("Arial", 16, "bold"), width=18

🎯 النتيجة: تحسن كبير في الوضوح وسهولة الاستخدام!
        """
        
        messagebox.showinfo("مقارنة الأزرار", comparison)
        result_var.set("📊 تم عرض مقارنة الأزرار قبل وبعد التحسين")
    
    def show_usage_tips():
        """عرض نصائح استخدام الأزرار المحسنة"""
        tips = """
💡 نصائح استخدام الأزرار المحسنة:

🏖️ في تبويب طلب الإجازة:
   • أزرار اختيار التاريخ أصبحت أكبر وأوضح
   • زر حساب الأيام أصبح أكثر بروزاً
   • الأزرار الرئيسية (تقديم، مسح) أصبحت أكبر
   • الأزرار الثانوية منظمة بشكل أفضل

✅ في تبويب الموافقة:
   • أزرار الموافقة والرفض أصبحت أوضح
   • نصوص الأزرار أكثر تفصيلاً
   • ألوان مميزة لكل عملية
   • أحجام موحدة لجميع الأزرار

📊 في تبويب التقارير:
   • أزرار التقارير أصبحت أكبر
   • تنظيم أفضل للأزرار
   • ألوان مناسبة لكل نوع تقرير

⚙️ في تبويب الإعدادات:
   • زر حفظ الإعدادات أصبح أكبر وأوضح
   • لون مميز للحفظ
   • حجم مناسب للأهمية

🎯 نصائح عامة:
   • جميع الأزرار الآن لها مؤشر يد
   • الألوان متناسقة ومعبرة
   • الأحجام موحدة حسب الأهمية
   • النصوص واضحة ومفصلة

🚀 للحصول على أفضل تجربة:
   • استخدم الأزرار بالترتيب المنطقي
   • راجع النصوص قبل الضغط
   • استفد من الألوان المميزة
   • استخدم مؤشر اليد للتأكد من التفاعل
        """
        
        messagebox.showinfo("نصائح الاستخدام", tips)
        result_var.set("💡 تم عرض نصائح استخدام الأزرار المحسنة")
    
    def show_technical_details():
        """عرض التفاصيل التقنية للتحسينات"""
        details = """
🔧 التفاصيل التقنية للتحسينات:

📏 أحجام الخطوط:
   • التبويبات الرئيسية: font=("Arial", 14, "bold")
   • الأزرار الرئيسية: font=("Arial", 16, "bold")
   • أزرار التاريخ: font=("Arial", 12, "bold")
   • أزرار الموافقة: font=("Arial", 14, "bold")

📐 المساحات الداخلية:
   • الأزرار الرئيسية: padx=30, pady=15
   • الأزرار الثانوية: padx=25, pady=12
   • أزرار التاريخ: padx=15, pady=8
   • أزرار الموافقة: padx=20, pady=10

📊 العروض الثابتة:
   • التبويبات: width=16
   • الأزرار الرئيسية: width=18
   • أزرار الموافقة: width=14
   • زر الحساب: width=20

🎨 التصميم:
   • الحدود: bd=3-4
   • النمط: relief=tk.RAISED
   • المؤشر: cursor="hand2"
   • التباعد: padx=8-20

🌈 الألوان:
   • أخضر (#27ae60): الموافقة والحفظ
   • أحمر (#e74c3c): الرفض والإلغاء
   • أزرق (#3498db): المعلومات والتحديث
   • برتقالي (#f39c12): المساعدة والملاحظات
   • بنفسجي (#9b59b6): العرض والتقارير
   • رمادي (#95a5a6): المسح والإعادة

🎯 معايير التصميم:
   • التناسق في الأحجام
   • الوضوح في النصوص
   • التمييز بالألوان
   • سهولة التفاعل
        """
        
        messagebox.showinfo("التفاصيل التقنية", details)
        result_var.set("🔧 تم عرض التفاصيل التقنية للتحسينات")
    
    # أزرار الاختبار
    tk.Label(buttons_frame, text="اختبارات الأزرار المحسنة:", bg="#e8f4fd", 
            font=("Arial", 12, "bold")).pack()
    
    buttons_row1 = tk.Frame(buttons_frame, bg="#e8f4fd")
    buttons_row1.pack(pady=8)
    
    tk.Button(buttons_row1, text="🏖️ فتح قسم الإجازات",
             command=test_leave_department,
             bg="#4caf50", fg="white", font=("Arial", 12, "bold"),
             padx=15, pady=8, cursor="hand2").pack(side=tk.LEFT, padx=8)
    
    tk.Button(buttons_row1, text="📅 اختبار التاريخ",
             command=test_date_selection,
             bg="#2196f3", fg="white", font=("Arial", 12, "bold"),
             padx=15, pady=8, cursor="hand2").pack(side=tk.LEFT, padx=8)
    
    tk.Button(buttons_row1, text="👁️ اختبار المعاينة",
             command=test_preview_function,
             bg="#ff9800", fg="white", font=("Arial", 12, "bold"),
             padx=15, pady=8, cursor="hand2").pack(side=tk.LEFT, padx=8)
    
    buttons_row2 = tk.Frame(buttons_frame, bg="#e8f4fd")
    buttons_row2.pack(pady=8)
    
    tk.Button(buttons_row2, text="❓ اختبار المساعدة",
             command=test_help_function,
             bg="#9c27b0", fg="white", font=("Arial", 12, "bold"),
             padx=15, pady=8, cursor="hand2").pack(side=tk.LEFT, padx=8)
    
    tk.Button(buttons_row2, text="📊 مقارنة الأزرار",
             command=show_buttons_comparison,
             bg="#795548", fg="white", font=("Arial", 12, "bold"),
             padx=15, pady=8, cursor="hand2").pack(side=tk.LEFT, padx=8)
    
    tk.Button(buttons_row2, text="💡 نصائح الاستخدام",
             command=show_usage_tips,
             bg="#607d8b", fg="white", font=("Arial", 12, "bold"),
             padx=15, pady=8, cursor="hand2").pack(side=tk.LEFT, padx=8)
    
    buttons_row3 = tk.Frame(buttons_frame, bg="#e8f4fd")
    buttons_row3.pack(pady=8)
    
    tk.Button(buttons_row3, text="🔧 التفاصيل التقنية",
             command=show_technical_details,
             bg="#34495e", fg="white", font=("Arial", 12, "bold"),
             padx=15, pady=8, cursor="hand2").pack(side=tk.LEFT, padx=8)
    
    # معلومات إضافية
    info_frame = tk.Frame(test_frame, bg="#e8f4fd")
    info_frame.pack(pady=15)
    
    info_text = """
🎯 التحسينات الرئيسية:
• زيادة أحجام الخطوط (14-16 نقطة)
• زيادة المساحات الداخلية للأزرار
• توحيد العروض لتحسين المظهر
• إضافة مؤشر اليد للتفاعل
• تحسين الألوان والتباين
• نصوص أوضح وأكثر تفصيلاً
    """
    info_label = tk.Label(info_frame, text=info_text, bg="#e8f4fd", 
                         fg="#1565c0", font=("Arial", 10), justify=tk.LEFT)
    info_label.pack()
    
    # زر إغلاق
    tk.Button(test_frame, text="❌ إغلاق الاختبار", 
             command=root.destroy,
             bg="#616161", fg="white", 
             font=("Arial", 14, "bold"), padx=20, pady=10, cursor="hand2").pack(pady=20)
    
    # تعيين نتيجة أولية
    result_var.set("🎯 جاهز للاختبار - اضغط على الأزرار لاختبار التحسينات")

def main():
    """الدالة الرئيسية"""
    print("🔘 اختبار الأزرار المحسنة")
    print("=" * 60)
    
    test_enhanced_buttons()

if __name__ == "__main__":
    main()
