#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المراقبة الشامل للنظام
Comprehensive System Monitoring
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import datetime
import threading
import time
from typing import Dict, List, Any

# استيراد أنظمة التحسين
try:
    from data_validation import DataValidator, CalculationEngine
    from error_handler import error_handler, backup_manager
    from performance_optimizer import performance_monitor, memory_optimizer
    ENHANCED_FEATURES = True
except ImportError:
    ENHANCED_FEATURES = False

class SystemMonitor:
    """مراقب النظام الشامل"""

    def __init__(self, root):
        """تهيئة مراقب النظام"""
        self.root = root
        self.monitoring = False
        self.monitor_thread = None
        self.system_stats = {
            'uptime': 0,
            'operations_count': 0,
            'errors_count': 0,
            'warnings_count': 0,
            'last_backup': None,
            'memory_usage': 0,
            'cpu_usage': 0
        }

        # تهيئة أنظمة التحسين
        if ENHANCED_FEATURES:
            self.enhanced_monitoring = True
            print("✅ تم تفعيل المراقبة المحسنة")
        else:
            self.enhanced_monitoring = False

    def show_monitor_window(self):
        """عرض نافذة المراقبة"""
        monitor_window = tk.Toplevel(self.root)
        monitor_window.title("🔍 مراقب النظام الشامل")
        monitor_window.geometry("800x600")
        monitor_window.resizable(True, True)

        # إطار رئيسي
        main_frame = tk.Frame(monitor_window, bg="#f5f5f5")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # عنوان النافذة
        title_frame = tk.Frame(main_frame, bg="#2c3e50", height=60)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="🔍 مراقب النظام الشامل",
                              font=("Arial", 18, "bold"), fg="white", bg="#2c3e50")
        title_label.pack(expand=True)

        # إطار الإحصائيات
        stats_frame = tk.LabelFrame(main_frame, text="📊 إحصائيات النظام",
                                   font=("Arial", 12, "bold"), bg="#ffffff")
        stats_frame.pack(fill=tk.X, pady=(0, 10))

        # إنشاء عناصر الإحصائيات
        self.create_stats_display(stats_frame)

        # إطار الأداء
        performance_frame = tk.LabelFrame(main_frame, text="⚡ مراقبة الأداء",
                                        font=("Arial", 12, "bold"), bg="#ffffff")
        performance_frame.pack(fill=tk.X, pady=(0, 10))

        # إنشاء عناصر مراقبة الأداء
        self.create_performance_display(performance_frame)

        # إطار السجلات
        logs_frame = tk.LabelFrame(main_frame, text="📋 السجلات الأخيرة",
                                  font=("Arial", 12, "bold"), bg="#ffffff")
        logs_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # إنشاء عرض السجلات
        self.create_logs_display(logs_frame)

        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg="#f5f5f5")
        buttons_frame.pack(fill=tk.X)

        # أزرار التحكم
        self.create_control_buttons(buttons_frame, monitor_window)

        # بدء المراقبة
        self.start_monitoring()

        # تحديث دوري للبيانات
        self.update_monitor_display(monitor_window)

    def create_stats_display(self, parent):
        """إنشاء عرض الإحصائيات"""
        stats_inner = tk.Frame(parent, bg="#ffffff")
        stats_inner.pack(fill=tk.X, padx=10, pady=10)

        # إحصائيات في شبكة
        stats_data = [
            ("⏰ وقت التشغيل", "uptime", "#3498db"),
            ("🔧 العمليات", "operations_count", "#2ecc71"),
            ("❌ الأخطاء", "errors_count", "#e74c3c"),
            ("⚠️ التحذيرات", "warnings_count", "#f39c12"),
            ("💾 آخر نسخة احتياطية", "last_backup", "#9b59b6"),
            ("🖥️ استخدام الذاكرة", "memory_usage", "#16a085")
        ]

        self.stats_labels = {}

        for i, (title, key, color) in enumerate(stats_data):
            row = i // 3
            col = i % 3

            stat_frame = tk.Frame(stats_inner, bg=color, relief=tk.RAISED, bd=2)
            stat_frame.grid(row=row, column=col, padx=5, pady=5, sticky="nsew", ipadx=10, ipady=8)

            title_label = tk.Label(stat_frame, text=title, font=("Arial", 10, "bold"),
                                 bg=color, fg="white")
            title_label.pack(pady=(5, 2))

            value_label = tk.Label(stat_frame, text="0", font=("Arial", 12, "bold"),
                                 bg=color, fg="white")
            value_label.pack(pady=(0, 5))

            self.stats_labels[key] = value_label

        # تكوين الشبكة
        for col in range(3):
            stats_inner.grid_columnconfigure(col, weight=1)

    def create_performance_display(self, parent):
        """إنشاء عرض مراقبة الأداء"""
        perf_inner = tk.Frame(parent, bg="#ffffff")
        perf_inner.pack(fill=tk.X, padx=10, pady=10)

        if self.enhanced_monitoring:
            # عرض إحصائيات الأداء المحسنة
            perf_text = tk.Text(perf_inner, height=6, font=("Courier", 10))
            perf_scrollbar = ttk.Scrollbar(perf_inner, orient="vertical", command=perf_text.yview)
            perf_text.configure(yscrollcommand=perf_scrollbar.set)

            perf_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            perf_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            self.performance_text = perf_text
        else:
            info_label = tk.Label(perf_inner, text="⚠️ مراقبة الأداء المحسنة غير متاحة",
                                font=("Arial", 12), fg="#e74c3c", bg="#ffffff")
            info_label.pack(pady=20)

    def create_logs_display(self, parent):
        """إنشاء عرض السجلات"""
        logs_inner = tk.Frame(parent, bg="#ffffff")
        logs_inner.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # منطقة نص للسجلات
        logs_text = tk.Text(logs_inner, font=("Courier", 9), wrap=tk.WORD)
        logs_scrollbar = ttk.Scrollbar(logs_inner, orient="vertical", command=logs_text.yview)
        logs_text.configure(yscrollcommand=logs_scrollbar.set)

        logs_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        logs_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.logs_text = logs_text

    def create_control_buttons(self, parent, window):
        """إنشاء أزرار التحكم"""
        # زر تحديث
        refresh_btn = tk.Button(parent, text="🔄 تحديث",
                               command=self.refresh_data,
                               bg="#3498db", fg="white",
                               font=("Arial", 11, "bold"),
                               padx=15, pady=8)
        refresh_btn.pack(side=tk.LEFT, padx=5)

        # زر تحسين الذاكرة
        if self.enhanced_monitoring:
            optimize_btn = tk.Button(parent, text="🧹 تحسين الذاكرة",
                                   command=self.optimize_memory,
                                   bg="#27ae60", fg="white",
                                   font=("Arial", 11, "bold"),
                                   padx=15, pady=8)
            optimize_btn.pack(side=tk.LEFT, padx=5)

        # زر تقرير الأداء
        report_btn = tk.Button(parent, text="📊 تقرير الأداء",
                              command=self.show_performance_report,
                              bg="#9b59b6", fg="white",
                              font=("Arial", 11, "bold"),
                              padx=15, pady=8)
        report_btn.pack(side=tk.LEFT, padx=5)

        # زر إغلاق
        close_btn = tk.Button(parent, text="❌ إغلاق",
                             command=lambda: [self.stop_monitoring(), window.destroy()],
                             bg="#e74c3c", fg="white",
                             font=("Arial", 11, "bold"),
                             padx=15, pady=8)
        close_btn.pack(side=tk.RIGHT, padx=5)

    def start_monitoring(self):
        """بدء المراقبة"""
        if not self.monitoring:
            self.monitoring = True
            if self.enhanced_monitoring:
                performance_monitor.start_monitoring()
            print("🔍 بدء مراقبة النظام...")

    def stop_monitoring(self):
        """إيقاف المراقبة"""
        if self.monitoring:
            self.monitoring = False
            if self.enhanced_monitoring:
                performance_monitor.stop_monitoring()
            print("⏹️ تم إيقاف مراقبة النظام")

    def refresh_data(self):
        """تحديث البيانات"""
        try:
            # تحديث الإحصائيات الأساسية
            self.update_basic_stats()

            # تحديث السجلات
            self.update_logs_display()

            # تحديث الأداء
            if self.enhanced_monitoring:
                self.update_performance_display()

            print("✅ تم تحديث بيانات المراقبة")

        except Exception as e:
            print(f"❌ خطأ في تحديث البيانات: {e}")

    def update_basic_stats(self):
        """تحديث الإحصائيات الأساسية"""
        try:
            # حساب وقت التشغيل
            uptime = time.time() - getattr(self, 'start_time', time.time())
            uptime_str = f"{int(uptime//3600)}س {int((uptime%3600)//60)}د"

            # تحديث التسميات
            if hasattr(self, 'stats_labels'):
                self.stats_labels['uptime'].config(text=uptime_str)
                self.stats_labels['operations_count'].config(text=str(self.system_stats['operations_count']))
                self.stats_labels['errors_count'].config(text=str(self.system_stats['errors_count']))
                self.stats_labels['warnings_count'].config(text=str(self.system_stats['warnings_count']))

                # آخر نسخة احتياطية
                last_backup = self.get_last_backup_time()
                self.stats_labels['last_backup'].config(text=last_backup)

                # استخدام الذاكرة
                if self.enhanced_monitoring:
                    try:
                        from performance_optimizer import PSUTIL_AVAILABLE
                        if PSUTIL_AVAILABLE:
                            import psutil
                            memory_percent = psutil.virtual_memory().percent
                            self.stats_labels['memory_usage'].config(text=f"{memory_percent:.1f}%")
                        else:
                            self.stats_labels['memory_usage'].config(text="غير متاح")
                    except Exception as e:
                        self.stats_labels['memory_usage'].config(text="خطأ")
                        print(f"خطأ في قراءة الذاكرة: {e}")
                else:
                    self.stats_labels['memory_usage'].config(text="غير متاح")

        except Exception as e:
            print(f"❌ خطأ في تحديث الإحصائيات: {e}")

    def update_logs_display(self):
        """تحديث عرض السجلات"""
        try:
            if hasattr(self, 'logs_text'):
                self.logs_text.delete(1.0, tk.END)

                if self.enhanced_monitoring:
                    # عرض آخر الأخطاء
                    recent_errors = error_handler.get_recent_errors(10)
                    if recent_errors:
                        self.logs_text.insert(tk.END, "📋 آخر الأخطاء:\n")
                        for error in recent_errors:
                            self.logs_text.insert(tk.END, f"  • {error}\n")
                    else:
                        self.logs_text.insert(tk.END, "✅ لا توجد أخطاء حديثة\n")
                else:
                    self.logs_text.insert(tk.END, "⚠️ نظام السجلات المحسن غير متاح\n")
                    self.logs_text.insert(tk.END, "يرجى تثبيت المكتبات المطلوبة للحصول على سجلات مفصلة\n")

        except Exception as e:
            print(f"❌ خطأ في تحديث السجلات: {e}")

    def update_performance_display(self):
        """تحديث عرض الأداء"""
        try:
            if hasattr(self, 'performance_text') and self.enhanced_monitoring:
                self.performance_text.delete(1.0, tk.END)

                # عرض تقرير الأداء
                performance_report = performance_monitor.generate_performance_report()
                self.performance_text.insert(tk.END, performance_report)

        except Exception as e:
            print(f"❌ خطأ في تحديث الأداء: {e}")

    def get_last_backup_time(self):
        """الحصول على وقت آخر نسخة احتياطية"""
        try:
            if self.enhanced_monitoring:
                backups = backup_manager.list_backups()
                if backups:
                    latest_backup = backups[0]  # أحدث نسخة
                    return latest_backup['created'].strftime("%H:%M")
                else:
                    return "لا يوجد"
            else:
                # فحص مجلد النسخ الاحتياطية التقليدي
                backup_dir = "backups"
                if os.path.exists(backup_dir):
                    files = [f for f in os.listdir(backup_dir) if f.endswith('.xlsx')]
                    if files:
                        latest_file = max(files, key=lambda f: os.path.getctime(os.path.join(backup_dir, f)))
                        file_time = datetime.datetime.fromtimestamp(
                            os.path.getctime(os.path.join(backup_dir, latest_file))
                        )
                        return file_time.strftime("%H:%M")
                return "لا يوجد"

        except Exception as e:
            print(f"❌ خطأ في الحصول على وقت النسخة الاحتياطية: {e}")
            return "خطأ"

    def optimize_memory(self):
        """تحسين الذاكرة"""
        try:
            if self.enhanced_monitoring:
                result = memory_optimizer.optimize_memory()
                messagebox.showinfo("تحسين الذاكرة",
                                  f"تم تحسين الذاكرة بنجاح!\n"
                                  f"تم جمع {result['collected_objects']} كائن\n"
                                  f"استخدام الذاكرة: {result['memory_percent']:.1f}%")
            else:
                # تحسين بسيط
                import gc
                collected = gc.collect()
                messagebox.showinfo("تحسين الذاكرة", f"تم جمع {collected} كائن من الذاكرة")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحسين الذاكرة: {e}")

    def show_performance_report(self):
        """عرض تقرير الأداء"""
        try:
            report_window = tk.Toplevel(self.root)
            report_window.title("📊 تقرير الأداء التفصيلي")
            report_window.geometry("700x500")

            # منطقة نص للتقرير
            text_frame = tk.Frame(report_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            report_text = tk.Text(text_frame, font=("Courier", 10), wrap=tk.WORD)
            scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=report_text.yview)
            report_text.configure(yscrollcommand=scrollbar.set)

            report_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # إنشاء التقرير
            if self.enhanced_monitoring:
                report = performance_monitor.generate_performance_report()
                report += "\n\n" + error_handler.generate_error_report()
            else:
                report = "📊 تقرير الأداء الأساسي\n"
                report += "=" * 40 + "\n"
                report += f"📅 التاريخ: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                report += f"⏰ وقت التشغيل: {time.time() - getattr(self, 'start_time', time.time()):.1f} ثانية\n"
                report += "\n⚠️ للحصول على تقرير مفصل، يرجى تثبيت المكتبات المطلوبة"

            report_text.insert(tk.END, report)
            report_text.config(state=tk.DISABLED)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير الأداء: {e}")

    def update_monitor_display(self, window):
        """تحديث دوري لعرض المراقبة"""
        try:
            if window.winfo_exists():
                self.refresh_data()
                # تحديث كل 5 ثوان
                window.after(5000, lambda: self.update_monitor_display(window))
        except:
            pass  # النافذة مغلقة

# إنشاء مثيل عام
system_monitor = None

def show_system_monitor(root):
    """عرض مراقب النظام"""
    global system_monitor
    if system_monitor is None:
        system_monitor = SystemMonitor(root)
        system_monitor.start_time = time.time()

    system_monitor.show_monitor_window()