#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام تحسين الأداء والذاكرة
Performance and Memory Optimization System
"""

import os
import gc
import time
import threading
from typing import Dict, List, Any, Optional, Callable
from functools import wraps
import datetime

# استيراد psutil مع معالجة الأخطاء
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("⚠️ psutil غير متاح - بعض ميزات مراقبة الأداء ستكون محدودة")

class PerformanceMonitor:
    """مراقب الأداء والذاكرة"""
    
    def __init__(self):
        """تهيئة مراقب الأداء"""
        self.start_time = time.time()
        self.operation_times = {}
        self.memory_usage = []
        self.cpu_usage = []
        self.monitoring = False
        self.monitor_thread = None
    
    def start_monitoring(self, interval: float = 1.0):
        """بدء مراقبة الأداء"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, args=(interval,))
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        print("🔍 بدء مراقبة الأداء...")
    
    def stop_monitoring(self):
        """إيقاف مراقبة الأداء"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        print("⏹️ تم إيقاف مراقبة الأداء")
    
    def _monitor_loop(self, interval: float):
        """حلقة مراقبة الأداء"""
        while self.monitoring:
            try:
                if PSUTIL_AVAILABLE:
                    # مراقبة استخدام الذاكرة
                    memory_info = psutil.virtual_memory()
                    self.memory_usage.append({
                        'timestamp': time.time(),
                        'percent': memory_info.percent,
                        'available': memory_info.available,
                        'used': memory_info.used
                    })

                    # مراقبة استخدام المعالج
                    cpu_percent = psutil.cpu_percent(interval=0.1)
                    self.cpu_usage.append({
                        'timestamp': time.time(),
                        'percent': cpu_percent
                    })
                else:
                    # مراقبة بسيطة بدون psutil
                    self.memory_usage.append({
                        'timestamp': time.time(),
                        'percent': 0,
                        'available': 0,
                        'used': 0
                    })

                    self.cpu_usage.append({
                        'timestamp': time.time(),
                        'percent': 0
                    })
                
                # الاحتفاظ بآخر 100 قراءة فقط
                if len(self.memory_usage) > 100:
                    self.memory_usage = self.memory_usage[-100:]
                if len(self.cpu_usage) > 100:
                    self.cpu_usage = self.cpu_usage[-100:]
                
                time.sleep(interval)
                
            except Exception as e:
                print(f"❌ خطأ في مراقبة الأداء: {e}")
                break
    
    def measure_time(self, operation_name: str):
        """ديكوريتر لقياس وقت العمليات"""
        def decorator(func: Callable):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    execution_time = time.time() - start_time
                    
                    # حفظ وقت التنفيذ
                    if operation_name not in self.operation_times:
                        self.operation_times[operation_name] = []
                    
                    self.operation_times[operation_name].append(execution_time)
                    
                    # الاحتفاظ بآخر 50 قياس فقط
                    if len(self.operation_times[operation_name]) > 50:
                        self.operation_times[operation_name] = self.operation_times[operation_name][-50:]
                    
                    print(f"⏱️ {operation_name}: {execution_time:.3f} ثانية")
                    return result
                    
                except Exception as e:
                    execution_time = time.time() - start_time
                    print(f"❌ {operation_name} فشل بعد {execution_time:.3f} ثانية: {e}")
                    raise
            
            return wrapper
        return decorator
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأداء"""
        stats = {
            'uptime': time.time() - self.start_time,
            'operation_times': {},
            'memory_stats': {},
            'cpu_stats': {}
        }
        
        # إحصائيات أوقات العمليات
        for operation, times in self.operation_times.items():
            if times:
                stats['operation_times'][operation] = {
                    'count': len(times),
                    'avg_time': sum(times) / len(times),
                    'min_time': min(times),
                    'max_time': max(times),
                    'total_time': sum(times)
                }
        
        # إحصائيات الذاكرة
        if self.memory_usage:
            memory_percents = [m['percent'] for m in self.memory_usage]
            stats['memory_stats'] = {
                'current': self.memory_usage[-1]['percent'],
                'avg': sum(memory_percents) / len(memory_percents),
                'max': max(memory_percents),
                'min': min(memory_percents)
            }
        
        # إحصائيات المعالج
        if self.cpu_usage:
            cpu_percents = [c['percent'] for c in self.cpu_usage]
            stats['cpu_stats'] = {
                'current': self.cpu_usage[-1]['percent'],
                'avg': sum(cpu_percents) / len(cpu_percents),
                'max': max(cpu_percents),
                'min': min(cpu_percents)
            }
        
        return stats
    
    def generate_performance_report(self) -> str:
        """إنشاء تقرير الأداء"""
        stats = self.get_performance_stats()
        
        report = []
        report.append("📊 تقرير الأداء والذاكرة")
        report.append("=" * 50)
        report.append(f"📅 التاريخ: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"⏰ وقت التشغيل: {stats['uptime']:.1f} ثانية")
        report.append("")
        
        # إحصائيات العمليات
        if stats['operation_times']:
            report.append("⚡ أداء العمليات:")
            for operation, times_stats in stats['operation_times'].items():
                report.append(f"  • {operation}:")
                report.append(f"    - عدد المرات: {times_stats['count']}")
                report.append(f"    - متوسط الوقت: {times_stats['avg_time']:.3f}ث")
                report.append(f"    - أسرع وقت: {times_stats['min_time']:.3f}ث")
                report.append(f"    - أبطأ وقت: {times_stats['max_time']:.3f}ث")
            report.append("")
        
        # إحصائيات الذاكرة
        if stats['memory_stats']:
            mem_stats = stats['memory_stats']
            report.append("💾 استخدام الذاكرة:")
            report.append(f"  • الحالي: {mem_stats['current']:.1f}%")
            report.append(f"  • المتوسط: {mem_stats['avg']:.1f}%")
            report.append(f"  • الحد الأقصى: {mem_stats['max']:.1f}%")
            report.append(f"  • الحد الأدنى: {mem_stats['min']:.1f}%")
            report.append("")
        
        # إحصائيات المعالج
        if stats['cpu_stats']:
            cpu_stats = stats['cpu_stats']
            report.append("🖥️ استخدام المعالج:")
            report.append(f"  • الحالي: {cpu_stats['current']:.1f}%")
            report.append(f"  • المتوسط: {cpu_stats['avg']:.1f}%")
            report.append(f"  • الحد الأقصى: {cpu_stats['max']:.1f}%")
            report.append(f"  • الحد الأدنى: {cpu_stats['min']:.1f}%")
        
        return "\n".join(report)

class MemoryOptimizer:
    """محسن الذاكرة"""
    
    def __init__(self):
        """تهيئة محسن الذاكرة"""
        self.cache = {}
        self.cache_max_size = 100
        self.cache_access_count = {}
    
    def clear_cache(self):
        """مسح الذاكرة المؤقتة"""
        cleared_items = len(self.cache)
        self.cache.clear()
        self.cache_access_count.clear()
        gc.collect()  # تشغيل جامع القمامة
        print(f"🧹 تم مسح {cleared_items} عنصر من الذاكرة المؤقتة")
        return cleared_items
    
    def cache_data(self, key: str, data: Any, max_age: int = 300):
        """حفظ البيانات في الذاكرة المؤقتة"""
        # إذا امتلأت الذاكرة المؤقتة، احذف الأقل استخداماً
        if len(self.cache) >= self.cache_max_size:
            self._cleanup_cache()
        
        self.cache[key] = {
            'data': data,
            'timestamp': time.time(),
            'max_age': max_age
        }
        self.cache_access_count[key] = 0
    
    def get_cached_data(self, key: str) -> Optional[Any]:
        """الحصول على البيانات من الذاكرة المؤقتة"""
        if key not in self.cache:
            return None
        
        cache_entry = self.cache[key]
        
        # فحص انتهاء الصلاحية
        if time.time() - cache_entry['timestamp'] > cache_entry['max_age']:
            del self.cache[key]
            if key in self.cache_access_count:
                del self.cache_access_count[key]
            return None
        
        # تحديث عداد الوصول
        self.cache_access_count[key] = self.cache_access_count.get(key, 0) + 1
        
        return cache_entry['data']
    
    def _cleanup_cache(self):
        """تنظيف الذاكرة المؤقتة"""
        # حذف العناصر منتهية الصلاحية
        current_time = time.time()
        expired_keys = []
        
        for key, cache_entry in self.cache.items():
            if current_time - cache_entry['timestamp'] > cache_entry['max_age']:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.cache[key]
            if key in self.cache_access_count:
                del self.cache_access_count[key]
        
        # إذا لا تزال الذاكرة ممتلئة، احذف الأقل استخداماً
        if len(self.cache) >= self.cache_max_size:
            # ترتيب حسب عدد مرات الوصول
            sorted_keys = sorted(self.cache_access_count.items(), key=lambda x: x[1])
            
            # حذف النصف الأقل استخداماً
            keys_to_remove = [key for key, _ in sorted_keys[:len(sorted_keys)//2]]
            
            for key in keys_to_remove:
                if key in self.cache:
                    del self.cache[key]
                if key in self.cache_access_count:
                    del self.cache_access_count[key]
    
    def optimize_memory(self):
        """تحسين استخدام الذاكرة"""
        print("🔧 بدء تحسين الذاكرة...")

        # مسح الذاكرة المؤقتة منتهية الصلاحية
        self._cleanup_cache()

        # تشغيل جامع القمامة
        collected = gc.collect()

        # الحصول على معلومات الذاكرة
        if PSUTIL_AVAILABLE:
            memory_info = psutil.virtual_memory()
            memory_percent = memory_info.percent
            available_memory = memory_info.available
            available_gb = available_memory / (1024**3)
        else:
            memory_percent = 0
            available_memory = 0
            available_gb = 0

        print(f"✅ تم تحسين الذاكرة:")
        print(f"  • تم جمع {collected} كائن")
        if PSUTIL_AVAILABLE:
            print(f"  • استخدام الذاكرة: {memory_percent:.1f}%")
            print(f"  • الذاكرة المتاحة: {available_gb:.1f} GB")
        else:
            print(f"  • معلومات الذاكرة غير متاحة (psutil غير مثبت)")

        return {
            'collected_objects': collected,
            'memory_percent': memory_percent,
            'available_memory': available_memory
        }

class FileOptimizer:
    """محسن الملفات"""
    
    def __init__(self):
        """تهيئة محسن الملفات"""
        self.temp_files = []
    
    def optimize_excel_file(self, file_path: str) -> bool:
        """تحسين ملف Excel"""
        try:
            if not os.path.exists(file_path):
                return False
            
            print(f"🔧 تحسين ملف Excel: {file_path}")
            
            # إنشاء نسخة احتياطية
            backup_path = f"{file_path}.backup"
            try:
                import shutil
                shutil.copy2(file_path, backup_path)
            except ImportError:
                # نسخ بسيط بدون shutil
                with open(file_path, 'rb') as src, open(backup_path, 'wb') as dst:
                    dst.write(src.read())
            
            # تحميل وتحسين الملف
            from openpyxl import load_workbook
            wb = load_workbook(file_path)
            
            for ws in wb.worksheets:
                # إزالة الصفوف الفارغة
                rows_to_delete = []
                for row_num, row in enumerate(ws.iter_rows(values_only=True), 1):
                    if not any(row):
                        rows_to_delete.append(row_num)
                
                # حذف الصفوف الفارغة من الأسفل للأعلى
                for row_num in reversed(rows_to_delete):
                    ws.delete_rows(row_num)
                
                # تحسين عرض الأعمدة
                for column in ws.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        if cell.value:
                            max_length = max(max_length, len(str(cell.value)))
                    
                    adjusted_width = min(max_length + 2, 50)
                    ws.column_dimensions[column_letter].width = adjusted_width
            
            # حفظ الملف المحسن
            wb.save(file_path)
            
            # حذف النسخة الاحتياطية إذا نجح التحسين
            os.remove(backup_path)
            
            print(f"✅ تم تحسين ملف Excel بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ فشل في تحسين ملف Excel: {e}")
            
            # استعادة النسخة الاحتياطية في حالة الفشل
            if os.path.exists(backup_path):
                try:
                    import shutil
                    shutil.copy2(backup_path, file_path)
                except ImportError:
                    # نسخ بسيط بدون shutil
                    with open(backup_path, 'rb') as src, open(file_path, 'wb') as dst:
                        dst.write(src.read())
                os.remove(backup_path)
            
            return False
    
    def cleanup_temp_files(self):
        """تنظيف الملفات المؤقتة"""
        cleaned_count = 0
        
        for temp_file in self.temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    cleaned_count += 1
            except Exception as e:
                print(f"❌ فشل في حذف الملف المؤقت {temp_file}: {e}")
        
        self.temp_files.clear()
        print(f"🧹 تم حذف {cleaned_count} ملف مؤقت")
        return cleaned_count

# إنشاء مثيلات عامة
performance_monitor = PerformanceMonitor()
memory_optimizer = MemoryOptimizer()
file_optimizer = FileOptimizer()
