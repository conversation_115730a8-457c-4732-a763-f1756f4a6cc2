#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار القائمة المنسدلة للمسمى الوظيفي ونظام الإجازات المحسن
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

def test_job_dropdown():
    """اختبار القائمة المنسدلة للمسمى الوظيفي"""
    
    print("🧪 اختبار القائمة المنسدلة للمسمى الوظيفي")
    print("=" * 70)
    
    try:
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.title("🧪 اختبار القائمة المنسدلة للمسمى الوظيفي")
        root.geometry("600x400")
        root.configure(bg="#f5f5f5")
        
        # إطار رئيسي
        main_frame = tk.Frame(root, bg="#f5f5f5", padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان
        title_label = tk.Label(main_frame, text="🧪 اختبار القائمة المنسدلة للمسمى الوظيفي",
                              font=("Arial", 16, "bold"), fg="#2c3e50", bg="#f5f5f5")
        title_label.pack(pady=(0, 20))
        
        # إطار نموذج إضافة موظف
        form_frame = tk.LabelFrame(main_frame, text="نموذج إضافة موظف",
                                 font=("Arial", 12, "bold"), fg="#2c3e50",
                                 bg="#ffffff", padx=20, pady=15)
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # الحقول الأساسية
        fields = [
            ("الرقم الوظيفي", "entry"),
            ("الاسم العربي", "entry"),
            ("الرقم الوطني", "entry"),
            ("المسمى الوظيفي", "combobox")
        ]
        
        entries = {}
        
        for i, (label, field_type) in enumerate(fields):
            # تسمية الحقل
            tk.Label(form_frame, text=f"{label}:", font=("Arial", 11, "bold"),
                    bg="#ffffff").grid(row=i, column=0, padx=10, pady=8, sticky="e")
            
            if field_type == "entry":
                # حقل نص عادي
                entry = tk.Entry(form_frame, font=("Arial", 11), width=30)
                entry.grid(row=i, column=1, padx=10, pady=8, sticky="w")
                entries[label] = entry
                
            elif field_type == "combobox":
                # قائمة منسدلة للمسمى الوظيفي
                job_titles = ["موظف", "موظفة", "معلم", "معلمة"]
                combobox = ttk.Combobox(form_frame, values=job_titles, 
                                      font=("Arial", 11), width=28, state="readonly")
                combobox.grid(row=i, column=1, padx=10, pady=8, sticky="w")
                entries[label] = combobox
                
                # تعيين قيمة افتراضية
                combobox.current(0)  # "موظف"
        
        # معلومات القائمة المنسدلة
        info_frame = tk.LabelFrame(main_frame, text="معلومات القائمة المنسدلة",
                                 font=("Arial", 12, "bold"), fg="#2c3e50",
                                 bg="#ffffff", padx=20, pady=15)
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        info_text = """
📋 خيارات المسمى الوظيفي:
• موظف - يستحق رصيد إجازات تلقائي
• موظفة - تستحق رصيد إجازات تلقائي  
• معلم - لا يستحق رصيد إجازات تلقائي
• معلمة - لا تستحق رصيد إجازات تلقائي

🎯 نظام الإجازات:
• يظهر فقط الموظفين والموظفات في قائمة الإجازات
• المعلمون والمعلمات لا يظهرون في نظام الإجازات
• يمكن إضافة رصيد يدوي للجميع حسب الحاجة
        """
        
        info_label = tk.Label(info_frame, text=info_text, font=("Arial", 10),
                             bg="#ffffff", fg="#495057", justify=tk.LEFT)
        info_label.pack(anchor="w")
        
        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg="#f5f5f5")
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        def test_save():
            """اختبار حفظ البيانات"""
            data = {}
            for label, entry in entries.items():
                if isinstance(entry, ttk.Combobox):
                    data[label] = entry.get()
                else:
                    data[label] = entry.get()
            
            # التحقق من البيانات المطلوبة
            required_fields = ["الرقم الوظيفي", "الاسم العربي", "الرقم الوطني", "المسمى الوظيفي"]
            missing_fields = [field for field in required_fields if not data.get(field)]
            
            if missing_fields:
                messagebox.showwarning("تحذير", f"الرجاء ملء الحقول المطلوبة:\n{', '.join(missing_fields)}")
                return
            
            # عرض البيانات
            job_title = data["المسمى الوظيفي"]
            leave_eligible = "نعم" if job_title in ["موظف", "موظفة"] else "لا"
            
            result_text = f"""
✅ تم حفظ البيانات بنجاح:

👤 الرقم الوظيفي: {data['الرقم الوظيفي']}
👤 الاسم العربي: {data['الاسم العربي']}
🆔 الرقم الوطني: {data['الرقم الوطني']}
💼 المسمى الوظيفي: {data['المسمى الوظيفي']}

🏖️ مؤهل للإجازات: {leave_eligible}
📊 رصيد تلقائي: {'نعم' if job_title in ['موظف', 'موظفة'] else 'لا'}
            """
            
            messagebox.showinfo("نجاح الحفظ", result_text)
            
            print(f"✅ تم اختبار حفظ الموظف:")
            print(f"   المسمى الوظيفي: {job_title}")
            print(f"   مؤهل للإجازات: {leave_eligible}")
        
        def test_clear():
            """مسح البيانات"""
            for entry in entries.values():
                if isinstance(entry, ttk.Combobox):
                    entry.current(0)
                else:
                    entry.delete(0, tk.END)
            print("🧹 تم مسح البيانات")
        
        # أزرار
        save_btn = tk.Button(buttons_frame, text="💾 اختبار الحفظ",
                           command=test_save, font=("Arial", 12, "bold"),
                           bg="#27ae60", fg="white", relief=tk.RAISED, bd=2,
                           padx=20, pady=8)
        save_btn.pack(side=tk.LEFT, padx=10)
        
        clear_btn = tk.Button(buttons_frame, text="🧹 مسح",
                            command=test_clear, font=("Arial", 12, "bold"),
                            bg="#f39c12", fg="white", relief=tk.RAISED, bd=2,
                            padx=20, pady=8)
        clear_btn.pack(side=tk.LEFT, padx=10)
        
        close_btn = tk.Button(buttons_frame, text="❌ إغلاق",
                            command=root.destroy, font=("Arial", 12, "bold"),
                            bg="#e74c3c", fg="white", relief=tk.RAISED, bd=2,
                            padx=20, pady=8)
        close_btn.pack(side=tk.RIGHT, padx=10)
        
        print("✅ تم إنشاء نافذة اختبار القائمة المنسدلة")
        print("📋 الخيارات المتاحة: موظف، موظفة، معلم، معلمة")
        print("🎯 الافتراضي: موظف")
        
        # تشغيل النافذة
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار القائمة المنسدلة: {e}")
        return False

def test_leave_system_filtering():
    """اختبار فلترة نظام الإجازات للموظفين فقط"""
    
    print("\n🧪 اختبار فلترة نظام الإجازات")
    print("=" * 70)
    
    try:
        # محاكاة بيانات الموظفين
        test_employees = [
            {"الرقم الوظيفي": "001", "الاسم العربي": "أحمد محمد", "المسمى الوظيفي": "موظف"},
            {"الرقم الوظيفي": "002", "الاسم العربي": "فاطمة علي", "المسمى الوظيفي": "موظفة"},
            {"الرقم الوظيفي": "003", "الاسم العربي": "محمد أحمد", "المسمى الوظيفي": "معلم"},
            {"الرقم الوظيفي": "004", "الاسم العربي": "عائشة سالم", "المسمى الوظيفي": "معلمة"},
            {"الرقم الوظيفي": "005", "الاسم العربي": "سالم محمد", "المسمى الوظيفي": "موظف"},
        ]
        
        print(f"📊 إجمالي الموظفين في النظام: {len(test_employees)}")
        print("\n👥 جميع الموظفين:")
        for emp in test_employees:
            print(f"   {emp['الرقم الوظيفي']} - {emp['الاسم العربي']} ({emp['المسمى الوظيفي']})")
        
        # فلترة الموظفين المؤهلين للإجازات
        eligible_employees = []
        for emp in test_employees:
            if emp["المسمى الوظيفي"] in ["موظف", "موظفة"]:
                eligible_employees.append(emp)
        
        print(f"\n🏖️ الموظفون المؤهلون للإجازات: {len(eligible_employees)}")
        print("👥 قائمة المؤهلين:")
        for emp in eligible_employees:
            print(f"   ✅ {emp['الرقم الوظيفي']} - {emp['الاسم العربي']} ({emp['المسمى الوظيفي']})")
        
        # الموظفون غير المؤهلين
        non_eligible = [emp for emp in test_employees if emp["المسمى الوظيفي"] in ["معلم", "معلمة"]]
        print(f"\n👨‍🏫 الموظفون غير المؤهلين للإجازات: {len(non_eligible)}")
        print("👥 قائمة غير المؤهلين:")
        for emp in non_eligible:
            print(f"   ❌ {emp['الرقم الوظيفي']} - {emp['الاسم العربي']} ({emp['المسمى الوظيفي']})")
        
        # إحصائيات
        print(f"\n📈 الإحصائيات:")
        print(f"   📊 إجمالي الموظفين: {len(test_employees)}")
        print(f"   ✅ مؤهلون للإجازات: {len(eligible_employees)} ({len(eligible_employees)/len(test_employees)*100:.1f}%)")
        print(f"   ❌ غير مؤهلين: {len(non_eligible)} ({len(non_eligible)/len(test_employees)*100:.1f}%)")
        
        # اختبار النتيجة
        expected_eligible = 3  # موظف + موظفة + موظف
        expected_non_eligible = 2  # معلم + معلمة
        
        if len(eligible_employees) == expected_eligible and len(non_eligible) == expected_non_eligible:
            print("\n✅ اختبار الفلترة نجح!")
            print("   ✅ تم فلترة الموظفين المؤهلين بشكل صحيح")
            print("   ✅ تم استبعاد المعلمين بشكل صحيح")
            return True
        else:
            print("\n❌ اختبار الفلترة فشل!")
            print(f"   متوقع: {expected_eligible} مؤهل، {expected_non_eligible} غير مؤهل")
            print(f"   فعلي: {len(eligible_employees)} مؤهل، {len(non_eligible)} غير مؤهل")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار فلترة نظام الإجازات: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار القائمة المنسدلة ونظام الإجازات المحسن")
    print("=" * 80)
    
    # تشغيل الاختبارات
    test1 = test_leave_system_filtering()
    
    print("\n" + "=" * 80)
    print("📊 ملخص نتائج الاختبار:")
    print(f"   اختبار فلترة نظام الإجازات: {'✅ نجح' if test1 else '❌ فشل'}")
    
    if test1:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ نظام الفلترة يعمل بشكل صحيح")
        print("✅ الموظفون والموظفات فقط مؤهلون للإجازات")
        print("✅ المعلمون والمعلمات مستبعدون من نظام الإجازات")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    print("\n🎨 تشغيل اختبار القائمة المنسدلة التفاعلي...")
    test_job_dropdown()
    
    print("=" * 80)
