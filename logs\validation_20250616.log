[2025-06-16 03:32:16] ❌ خطأ: employee_data = '{'الرقم الوظيفي': '006', 'الاسم العربي': 'محمد صبحي', 'الاسم الإنجليزي': '', 'الرقم المالي': '15828', 'الرقم الوطني': '11946543215624105', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '', 'اسم المصرف': '', 'تاريخ أول مباشرة': '', 'الدرجة الحالية': '', 'العلاوة': '', 'تاريخ الدرجة الحالية': '', 'التخصص': '', 'المسمى الوظيفي': 'موظف', 'تاريخ التعيين': '', 'رقم الهاتف': '', 'الجنسية': '', 'تاريخ الميلاد': ''}'
  الرسالة: ❌ الرقم الوطني: يجب أن يكون 10 أرقام بالضبط
❌ الاسم الإنجليزي: مطلوب ولا يمكن أن يكون فارغاً
⚠️ رقم الهاتف: مستحسن إدخال رقم الهاتف
⚠️ رقم الحساب: مستحسن إدخال رقم الحساب
  المستخدم: يوسف
----------------------------------------
[2025-06-16 16:17:33] ❌ خطأ: employee_data = '{'الرقم الوظيفي': '002', 'الاسم العربي': 'فاطمة أحمد سالم', 'الاسم الإنجليزي': 'Fatima Ahmed Salem', 'الرقم المالي': 'F002', 'الرقم الوطني': '1234567890124', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '123456790', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2019-03-20', 'الدرجة الحالية': '', 'العلاوة': '1', 'تاريخ الدرجة الحالية': '2019-03-20', 'التخصص': 'تربية', 'المسمى الوظيفي': '', 'تاريخ التعيين': '2019-03-20', 'رقم الهاتف': '0912345679', 'الجنسية': '', 'تاريخ الميلاد': '1988-08-15'}'
  الرسالة: ❌ المسمى الوظيفي: حقل مطلوب
❌ الرقم الوطني: يجب أن يكون 10 أرقام بالضبط
❌ المسمى الوظيفي: مطلوب ولا يمكن أن يكون فارغاً
⚠️ رقم الهاتف: تنسيق رقم الهاتف قد يكون غير صحيح
⚠️ رقم الحساب: رقم الحساب قصير جداً
  المستخدم: يوسف
----------------------------------------
[2025-06-16 16:17:41] ❌ خطأ: employee_data = '{'الرقم الوظيفي': '002', 'الاسم العربي': 'فاطمة أحمد سالم', 'الاسم الإنجليزي': 'Fatima Ahmed Salem', 'الرقم المالي': 'F002', 'الرقم الوطني': '1234567890124', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '123456790', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2019-03-20', 'الدرجة الحالية': '', 'العلاوة': '1', 'تاريخ الدرجة الحالية': '2019-03-20', 'التخصص': 'تربية', 'المسمى الوظيفي': 'موظفة', 'تاريخ التعيين': '2019-03-20', 'رقم الهاتف': '0912345679', 'الجنسية': '', 'تاريخ الميلاد': '1988-08-15'}'
  الرسالة: ❌ الرقم الوطني: يجب أن يكون 10 أرقام بالضبط
⚠️ رقم الهاتف: تنسيق رقم الهاتف قد يكون غير صحيح
⚠️ رقم الحساب: رقم الحساب قصير جداً
  المستخدم: يوسف
----------------------------------------
[2025-06-16 16:18:01] ❌ خطأ: employee_data = '{'الرقم الوظيفي': '002', 'الاسم العربي': 'فاطمة أحمد سالم', 'الاسم الإنجليزي': 'Fatima Ahmed Salem', 'الرقم المالي': 'F002', 'الرقم الوطني': '123456789012', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '123456790', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2019-03-20', 'الدرجة الحالية': '', 'العلاوة': '1', 'تاريخ الدرجة الحالية': '2019-03-20', 'التخصص': 'تربية', 'المسمى الوظيفي': 'موظفة', 'تاريخ التعيين': '2019-03-20', 'رقم الهاتف': '0912345679', 'الجنسية': '', 'تاريخ الميلاد': '1988-08-15'}'
  الرسالة: ❌ الرقم الوطني: يجب أن يكون 10 أرقام بالضبط
⚠️ رقم الهاتف: تنسيق رقم الهاتف قد يكون غير صحيح
⚠️ رقم الحساب: رقم الحساب قصير جداً
  المستخدم: يوسف
----------------------------------------
[2025-06-16 17:47:35] ❌ خطأ: employee_data = '{'الرقم الوظيفي': '001', 'الاسم العربي': 'أحمد محمد علي', 'الاسم الإنجليزي': 'Ahmed Mohamed Ali', 'الرقم المالي': 'F001', 'الرقم الوطني': '1234567890123', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '123456789', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2020-01-15', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2020-01-15', 'التخصص': 'إدارة أعمال', 'المسمى الوظيفي': '', 'تاريخ التعيين': '2020-01-15', 'رقم الهاتف': '0912345678', 'الجنسية': '', 'تاريخ الميلاد': '1990-05-10'}'
  الرسالة: ❌ المسمى الوظيفي: حقل مطلوب
❌ الرقم الوطني: يجب أن يكون 10 أرقام بالضبط
❌ المسمى الوظيفي: مطلوب ولا يمكن أن يكون فارغاً
⚠️ رقم الهاتف: تنسيق رقم الهاتف قد يكون غير صحيح
⚠️ رقم الحساب: رقم الحساب قصير جداً
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:05:33] ❌ خطأ: employee_data = '{'الرقم الوظيفي': '002', 'الاسم العربي': 'فاطمة أحمد سالم', 'الاسم الإنجليزي': 'Fatima Ahmed Salem', 'الرقم المالي': 'F002', 'الرقم الوطني': '1234567890124', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '123456790', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2019-03-20', 'الدرجة الحالية': '', 'العلاوة': '1', 'تاريخ الدرجة الحالية': '2019-03-20', 'التخصص': 'تربية', 'المسمى الوظيفي': '', 'تاريخ التعيين': '2019-03-20', 'رقم الهاتف': '0912345679', 'الجنسية': '', 'تاريخ الميلاد': '1988-08-15'}'
  الرسالة: ❌ المسمى الوظيفي: حقل مطلوب
❌ الرقم الوطني: يجب أن يكون 10 أو 12 رقم
❌ المسمى الوظيفي: مطلوب ولا يمكن أن يكون فارغاً
⚠️ رقم الهاتف: تنسيق رقم الهاتف قد يكون غير صحيح
⚠️ رقم الحساب: رقم الحساب قصير جداً
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:05:52] ❌ خطأ: employee_data = '{'الرقم الوظيفي': '002', 'الاسم العربي': 'فاطمة أحمد سالم', 'الاسم الإنجليزي': 'Fatima Ahmed Salem', 'الرقم المالي': 'F002', 'الرقم الوطني': '1234567891224', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '123456790', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2019-03-20', 'الدرجة الحالية': '', 'العلاوة': '1', 'تاريخ الدرجة الحالية': '2019-03-20', 'التخصص': 'تربية', 'المسمى الوظيفي': 'موظفة', 'تاريخ التعيين': '2019-03-20', 'رقم الهاتف': '0912345679', 'الجنسية': '', 'تاريخ الميلاد': '1988-08-15'}'
  الرسالة: ❌ الرقم الوطني: يجب أن يكون 10 أو 12 رقم
⚠️ رقم الهاتف: تنسيق رقم الهاتف قد يكون غير صحيح
⚠️ رقم الحساب: رقم الحساب قصير جداً
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:05:53] ❌ خطأ: employee_data = '{'الرقم الوظيفي': '002', 'الاسم العربي': 'فاطمة أحمد سالم', 'الاسم الإنجليزي': 'Fatima Ahmed Salem', 'الرقم المالي': 'F002', 'الرقم الوطني': '123456789124', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '123456790', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2019-03-20', 'الدرجة الحالية': '', 'العلاوة': '1', 'تاريخ الدرجة الحالية': '2019-03-20', 'التخصص': 'تربية', 'المسمى الوظيفي': '', 'تاريخ التعيين': '2019-03-20', 'رقم الهاتف': '0912345679', 'الجنسية': '', 'تاريخ الميلاد': '1988-08-15'}'
  الرسالة: ❌ المسمى الوظيفي: حقل مطلوب
❌ المسمى الوظيفي: مطلوب ولا يمكن أن يكون فارغاً
⚠️ رقم الهاتف: تنسيق رقم الهاتف قد يكون غير صحيح
⚠️ رقم الحساب: رقم الحساب قصير جداً
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:06:05] ✅ صحيح: employee_data = '{'الرقم الوظيفي': '002', 'الاسم العربي': 'فاطمة أحمد سالم', 'الاسم الإنجليزي': 'Fatima Ahmed Salem', 'الرقم المالي': 'F002', 'الرقم الوطني': '123456789124', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '123456790', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2019-03-20', 'الدرجة الحالية': '', 'العلاوة': '1', 'تاريخ الدرجة الحالية': '2019-03-20', 'التخصص': 'تربية', 'المسمى الوظيفي': 'موظفة', 'تاريخ التعيين': '2019-03-20', 'رقم الهاتف': '0912345679', 'الجنسية': '', 'تاريخ الميلاد': '1988-08-15'}'
  الرسالة: تم التحقق من البيانات بنجاح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:06:55] ❌ خطأ: employee_data = '{'الرقم الوظيفي': '001', 'الاسم العربي': 'أحمد محمد علي', 'الاسم الإنجليزي': 'Ahmed Mohamed Ali', 'الرقم المالي': 'F001', 'الرقم الوطني': '1234567890123', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '1234567896596859', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2020-01-15', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2020-01-15', 'التخصص': 'إدارة أعمال', 'المسمى الوظيفي': 'موظف', 'تاريخ التعيين': '2020-01-15', 'رقم الهاتف': '0912345678', 'الجنسية': '', 'تاريخ الميلاد': '1990-05-10'}'
  الرسالة: ❌ الرقم الوطني: يجب أن يكون 10 أو 12 رقم
⚠️ رقم الهاتف: تنسيق رقم الهاتف قد يكون غير صحيح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:07:02] ✅ صحيح: employee_data = '{'الرقم الوظيفي': '001', 'الاسم العربي': 'أحمد محمد علي', 'الاسم الإنجليزي': 'Ahmed Mohamed Ali', 'الرقم المالي': 'F001', 'الرقم الوطني': '123456789013', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '1234567896596859', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2020-01-15', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2020-01-15', 'التخصص': 'إدارة أعمال', 'المسمى الوظيفي': 'موظف', 'تاريخ التعيين': '2020-01-15', 'رقم الهاتف': '0912345678', 'الجنسية': '', 'تاريخ الميلاد': '1990-05-10'}'
  الرسالة: تم التحقق من البيانات بنجاح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:07:06] ❌ خطأ: employee_data = '{'الرقم الوظيفي': '001', 'الاسم العربي': 'أحمد محمد علي', 'الاسم الإنجليزي': 'Ahmed Mohamed Ali', 'الرقم المالي': 'F001', 'الرقم الوطني': '1234567890123', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '123456789', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2020-01-15', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2020-01-15', 'التخصص': 'إدارة أعمال', 'المسمى الوظيفي': '', 'تاريخ التعيين': '2020-01-15', 'رقم الهاتف': '0912345678', 'الجنسية': '', 'تاريخ الميلاد': '1990-05-10'}'
  الرسالة: ❌ المسمى الوظيفي: حقل مطلوب
❌ الرقم الوطني: يجب أن يكون 10 أو 12 رقم
❌ المسمى الوظيفي: مطلوب ولا يمكن أن يكون فارغاً
⚠️ رقم الهاتف: تنسيق رقم الهاتف قد يكون غير صحيح
⚠️ رقم الحساب: رقم الحساب قصير جداً
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:07:31] ❌ خطأ: employee_data = '{'الرقم الوظيفي': '001', 'الاسم العربي': 'أحمد محمد علي', 'الاسم الإنجليزي': 'Ahmed Mohamed Ali', 'الرقم المالي': 'F001', 'الرقم الوطني': '123456789013', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '1234567896596859', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2020-01-15', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2020-01-15', 'التخصص': 'إدارة أعمال', 'المسمى الوظيفي': '', 'تاريخ التعيين': '2020-01-15', 'رقم الهاتف': '0912345678', 'الجنسية': '', 'تاريخ الميلاد': '1990-05-10'}'
  الرسالة: ❌ المسمى الوظيفي: حقل مطلوب
❌ المسمى الوظيفي: مطلوب ولا يمكن أن يكون فارغاً
⚠️ رقم الهاتف: تنسيق رقم الهاتف قد يكون غير صحيح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:07:34] ❌ خطأ: employee_data = '{'الرقم الوظيفي': '001', 'الاسم العربي': 'أحمد محمد علي', 'الاسم الإنجليزي': 'Ahmed Mohamed Ali', 'الرقم المالي': 'F001', 'الرقم الوطني': '123456789013', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '1234567896596859', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2020-01-15', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2020-01-15', 'التخصص': 'إدارة أعمال', 'المسمى الوظيفي': '', 'تاريخ التعيين': '2020-01-15', 'رقم الهاتف': '0912345678', 'الجنسية': '', 'تاريخ الميلاد': '1990-05-10'}'
  الرسالة: ❌ المسمى الوظيفي: حقل مطلوب
❌ المسمى الوظيفي: مطلوب ولا يمكن أن يكون فارغاً
⚠️ رقم الهاتف: تنسيق رقم الهاتف قد يكون غير صحيح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:10:39] ✅ صحيح: employee_data = '{'الرقم الوظيفي': '001', 'الاسم العربي': 'أحمد محمد علي', 'الاسم الإنجليزي': 'Ahmed Mohamed Ali', 'الرقم المالي': 'F001', 'الرقم الوطني': '123456789013', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '1234567896596859', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2020-01-15', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2020-01-15', 'التخصص': 'إدارة أعمال', 'المسمى الوظيفي': 'موظف', 'تاريخ التعيين': '2020-01-15', 'رقم الهاتف': '0912345678', 'الجنسية': '', 'تاريخ الميلاد': '1990-05-10'}'
  الرسالة: تم التحقق من البيانات بنجاح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:10:41] ✅ صحيح: employee_data = '{'الرقم الوظيفي': '001', 'الاسم العربي': 'أحمد محمد علي', 'الاسم الإنجليزي': 'Ahmed Mohamed Ali', 'الرقم المالي': 'F001', 'الرقم الوطني': '123456789013', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '1234567896596859', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2020-01-15', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2020-01-15', 'التخصص': 'إدارة أعمال', 'المسمى الوظيفي': 'موظف', 'تاريخ التعيين': '2020-01-15', 'رقم الهاتف': '0912345678', 'الجنسية': '', 'تاريخ الميلاد': '1990-05-10'}'
  الرسالة: تم التحقق من البيانات بنجاح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:10:42] ✅ صحيح: employee_data = '{'الرقم الوظيفي': '001', 'الاسم العربي': 'أحمد محمد علي', 'الاسم الإنجليزي': 'Ahmed Mohamed Ali', 'الرقم المالي': 'F001', 'الرقم الوطني': '123456789013', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '1234567896596859', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2020-01-15', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2020-01-15', 'التخصص': 'إدارة أعمال', 'المسمى الوظيفي': 'موظف', 'تاريخ التعيين': '2020-01-15', 'رقم الهاتف': '0912345678', 'الجنسية': '', 'تاريخ الميلاد': '1990-05-10'}'
  الرسالة: تم التحقق من البيانات بنجاح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:10:44] ✅ صحيح: employee_data = '{'الرقم الوظيفي': '001', 'الاسم العربي': 'أحمد محمد علي', 'الاسم الإنجليزي': 'Ahmed Mohamed Ali', 'الرقم المالي': 'F001', 'الرقم الوطني': '123456789013', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '1234567896596859', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2020-01-15', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2020-01-15', 'التخصص': 'إدارة أعمال', 'المسمى الوظيفي': 'موظف', 'تاريخ التعيين': '2020-01-15', 'رقم الهاتف': '0912345678', 'الجنسية': '', 'تاريخ الميلاد': '1990-05-10'}'
  الرسالة: تم التحقق من البيانات بنجاح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:10:48] ✅ صحيح: employee_data = '{'الرقم الوظيفي': '001', 'الاسم العربي': 'أحمد محمد علي', 'الاسم الإنجليزي': 'Ahmed Mohamed Ali', 'الرقم المالي': 'F001', 'الرقم الوطني': '123456789013', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '1234567896596859', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2020-01-15', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2020-01-15', 'التخصص': 'إدارة أعمال', 'المسمى الوظيفي': 'موظف', 'تاريخ التعيين': '2020-01-15', 'رقم الهاتف': '0912345678', 'الجنسية': '', 'تاريخ الميلاد': '1990-05-10'}'
  الرسالة: تم التحقق من البيانات بنجاح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:10:50] ✅ صحيح: employee_data = '{'الرقم الوظيفي': '001', 'الاسم العربي': 'أحمد محمد علي', 'الاسم الإنجليزي': 'Ahmed Mohamed Ali', 'الرقم المالي': 'F001', 'الرقم الوطني': '123456789013', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '1234567896596859', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2020-01-15', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2020-01-15', 'التخصص': 'إدارة أعمال', 'المسمى الوظيفي': 'موظف', 'تاريخ التعيين': '2020-01-15', 'رقم الهاتف': '0912345678', 'الجنسية': '', 'تاريخ الميلاد': '1990-05-10'}'
  الرسالة: تم التحقق من البيانات بنجاح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:10:51] ❌ خطأ: employee_data = '{'الرقم الوظيفي': '001', 'الاسم العربي': 'أحمد محمد علي', 'الاسم الإنجليزي': 'Ahmed Mohamed Ali', 'الرقم المالي': 'F001', 'الرقم الوطني': '123456789013', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '1234567896596859', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2020-01-15', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2020-01-15', 'التخصص': 'إدارة أعمال', 'المسمى الوظيفي': '', 'تاريخ التعيين': '2020-01-15', 'رقم الهاتف': '0912345678', 'الجنسية': '', 'تاريخ الميلاد': '1990-05-10'}'
  الرسالة: ❌ المسمى الوظيفي: حقل مطلوب
❌ المسمى الوظيفي: مطلوب ولا يمكن أن يكون فارغاً
⚠️ رقم الهاتف: تنسيق رقم الهاتف قد يكون غير صحيح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:11:25] ✅ صحيح: employee_data = '{'الرقم الوظيفي': '001', 'الاسم العربي': 'أحمد محمد علي', 'الاسم الإنجليزي': 'Ahmed Mohamed Ali', 'الرقم المالي': 'F001', 'الرقم الوطني': '123456789013', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '1234567896596859', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2020-01-15', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2020-01-15', 'التخصص': 'إدارة أعمال', 'المسمى الوظيفي': 'موظف', 'تاريخ التعيين': '2020-01-15', 'رقم الهاتف': '0915215906', 'الجنسية': '', 'تاريخ الميلاد': '1990-05-10'}'
  الرسالة: تم التحقق من البيانات بنجاح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:11:28] ✅ صحيح: employee_data = '{'الرقم الوظيفي': '001', 'الاسم العربي': 'أحمد محمد علي', 'الاسم الإنجليزي': 'Ahmed Mohamed Ali', 'الرقم المالي': 'F001', 'الرقم الوطني': '123456789013', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '1234567896596859', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2020-01-15', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2020-01-15', 'التخصص': 'إدارة أعمال', 'المسمى الوظيفي': 'موظف', 'تاريخ التعيين': '2020-01-15', 'رقم الهاتف': '0912345678', 'الجنسية': '', 'تاريخ الميلاد': '1990-05-10'}'
  الرسالة: تم التحقق من البيانات بنجاح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:11:33] ✅ صحيح: employee_data = '{'الرقم الوظيفي': '001', 'الاسم العربي': 'أحمد محمد علي', 'الاسم الإنجليزي': 'Ahmed Mohamed Ali', 'الرقم المالي': 'F001', 'الرقم الوطني': '123456789013', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '1234567896596859', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2020-01-15', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2020-01-15', 'التخصص': 'إدارة أعمال', 'المسمى الوظيفي': 'موظف', 'تاريخ التعيين': '2020-01-15', 'رقم الهاتف': '0912345678', 'الجنسية': '', 'تاريخ الميلاد': '1990-05-10'}'
  الرسالة: تم التحقق من البيانات بنجاح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:11:34] ❌ خطأ: employee_data = '{'الرقم الوظيفي': '001', 'الاسم العربي': 'أحمد محمد علي', 'الاسم الإنجليزي': 'Ahmed Mohamed Ali', 'الرقم المالي': 'F001', 'الرقم الوطني': '123456789013', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '1234567896596859', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2020-01-15', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2020-01-15', 'التخصص': 'إدارة أعمال', 'المسمى الوظيفي': '', 'تاريخ التعيين': '2020-01-15', 'رقم الهاتف': '0912345678', 'الجنسية': '', 'تاريخ الميلاد': '1990-05-10'}'
  الرسالة: ❌ المسمى الوظيفي: حقل مطلوب
❌ المسمى الوظيفي: مطلوب ولا يمكن أن يكون فارغاً
⚠️ رقم الهاتف: تنسيق رقم الهاتف قد يكون غير صحيح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:14:20] ✅ صحيح: employee_data = '{'الرقم الوظيفي': '001', 'الاسم العربي': 'أحمد محمد علي', 'الاسم الإنجليزي': 'Ahmed Mohamed Ali', 'الرقم المالي': 'F001', 'الرقم الوطني': '123456789013', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '1234567896596859', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2020-01-15', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2020-01-15', 'التخصص': 'إدارة أعمال', 'المسمى الوظيفي': 'موظفة', 'تاريخ التعيين': '2020-01-15', 'رقم الهاتف': '0912345678', 'الجنسية': '', 'تاريخ الميلاد': '1990-05-10'}'
  الرسالة: تم التحقق من البيانات بنجاح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:15:28] ❌ خطأ: employee_data = '{'الرقم الوظيفي': '003', 'الاسم العربي': 'محمد سالم عبدالله', 'الاسم الإنجليزي': 'Mohamed Salem Abdullah', 'الرقم المالي': 'F003', 'الرقم الوطني': '1234567890125', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '123456791', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2021-06-10', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2021-06-10', 'التخصص': 'محاسبة', 'المسمى الوظيفي': 'موظفة', 'تاريخ التعيين': '2021-06-10', 'رقم الهاتف': '0912345680', 'الجنسية': '', 'تاريخ الميلاد': '1992-12-20'}'
  الرسالة: ❌ الرقم الوطني: يجب أن يكون 10 أو 12 رقم
⚠️ رقم الهاتف: تنسيق رقم الهاتف قد يكون غير صحيح
⚠️ رقم الحساب: رقم الحساب قصير جداً
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:15:35] ✅ صحيح: employee_data = '{'الرقم الوظيفي': '003', 'الاسم العربي': 'محمد سالم عبدالله', 'الاسم الإنجليزي': 'Mohamed Salem Abdullah', 'الرقم المالي': 'F003', 'الرقم الوطني': '123456780125', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '123456791', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2021-06-10', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2021-06-10', 'التخصص': 'محاسبة', 'المسمى الوظيفي': 'موظفة', 'تاريخ التعيين': '2021-06-10', 'رقم الهاتف': '0912345680', 'الجنسية': '', 'تاريخ الميلاد': '1992-12-20'}'
  الرسالة: تم التحقق من البيانات بنجاح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:30:19] ✅ صحيح: employee_data = '{'الرقم الوظيفي': '002', 'الاسم العربي': 'فاطمة أحمد سالم', 'الاسم الإنجليزي': 'Fatima Ahmed Salem', 'الرقم المالي': 'F002', 'الرقم الوطني': '123456789124', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '123456790', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2019-03-20', 'الدرجة الحالية': '', 'العلاوة': '1', 'تاريخ الدرجة الحالية': '2019-03-20', 'التخصص': 'تربية', 'المسمى الوظيفي': 'موظفة', 'تاريخ التعيين': '2019-03-20', 'رقم الهاتف': '0912345679', 'الجنسية': '', 'تاريخ الميلاد': '1988-08-15'}'
  الرسالة: تم التحقق من البيانات بنجاح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:30:35] ✅ صحيح: employee_data = '{'الرقم الوظيفي': '002', 'الاسم العربي': 'فاطمة أحمد سالم', 'الاسم الإنجليزي': 'Fatima Ahmed Salem', 'الرقم المالي': 'F002', 'الرقم الوطني': '123456789124', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '123456790596', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2019-03-20', 'الدرجة الحالية': '', 'العلاوة': '1', 'تاريخ الدرجة الحالية': '2019-03-20', 'التخصص': 'تربية', 'المسمى الوظيفي': 'موظف', 'تاريخ التعيين': '2019-03-20', 'رقم الهاتف': '0912345679', 'الجنسية': '', 'تاريخ الميلاد': '1988-08-15'}'
  الرسالة: تم التحقق من البيانات بنجاح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:32:27] ✅ صحيح: employee_data = '{'الرقم الوظيفي': '003', 'الاسم العربي': 'محمد سالم عبدالله', 'الاسم الإنجليزي': 'Mohamed Salem Abdullah', 'الرقم المالي': 'F003', 'الرقم الوطني': '123456780125', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '123456791542', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2021-06-10', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2021-06-10', 'التخصص': 'محاسبة', 'المسمى الوظيفي': 'موظف', 'تاريخ التعيين': '2021-06-10', 'رقم الهاتف': '0912345680', 'الجنسية': '', 'تاريخ الميلاد': '1992-12-20'}'
  الرسالة: تم التحقق من البيانات بنجاح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:32:29] ✅ صحيح: employee_data = '{'الرقم الوظيفي': '003', 'الاسم العربي': 'محمد سالم عبدالله', 'الاسم الإنجليزي': 'Mohamed Salem Abdullah', 'الرقم المالي': 'F003', 'الرقم الوطني': '123456780125', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '123456791', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2021-06-10', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2021-06-10', 'التخصص': 'محاسبة', 'المسمى الوظيفي': 'موظف', 'تاريخ التعيين': '2021-06-10', 'رقم الهاتف': '0912345680', 'الجنسية': '', 'تاريخ الميلاد': '1992-12-20'}'
  الرسالة: تم التحقق من البيانات بنجاح
  المستخدم: يوسف
----------------------------------------
[2025-06-16 18:32:31] ❌ خطأ: employee_data = '{'الرقم الوظيفي': '003', 'الاسم العربي': 'محمد سالم عبدالله', 'الاسم الإنجليزي': 'Mohamed Salem Abdullah', 'الرقم المالي': 'F003', 'الرقم الوطني': '123456780125', 'المؤهل': '', 'مكان العمل الحالي': '', 'رقم الحساب': '123456791', 'اسم المصرف': '', 'تاريخ أول مباشرة': '2021-06-10', 'الدرجة الحالية': '', 'العلاوة': '0', 'تاريخ الدرجة الحالية': '2021-06-10', 'التخصص': 'محاسبة', 'المسمى الوظيفي': '', 'تاريخ التعيين': '2021-06-10', 'رقم الهاتف': '0912345680', 'الجنسية': '', 'تاريخ الميلاد': '1992-12-20'}'
  الرسالة: ❌ المسمى الوظيفي: حقل مطلوب
❌ المسمى الوظيفي: مطلوب ولا يمكن أن يكون فارغاً
⚠️ رقم الحساب: رقم الحساب قصير جداً
  المستخدم: يوسف
----------------------------------------
