#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسينات قسم الإجازات
Test Leave Department Improvements
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
from datetime import datetime

def test_leave_department_improvements():
    """اختبار تحسينات قسم الإجازات"""
    print("🔧 اختبار تحسينات قسم الإجازات")
    print("=" * 60)
    
    try:
        # استيراد قسم الإجازات المحسن
        print("📦 استيراد قسم الإجازات المحسن...")
        import leave_department_system
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة الاختبار...")
        root = tk.Tk()
        root.title("اختبار تحسينات قسم الإجازات")
        root.geometry("1400x900")
        root.configure(bg="#f0f8ff")
        
        # إنشاء قسم الإجازات
        print("🔧 إنشاء قسم الإجازات...")
        leave_dept = leave_department_system.LeaveDepartmentSystem(root)
        print("✅ تم إنشاء قسم الإجازات بنجاح")
        
        # فحص التحسينات
        print("\n🔍 فحص التحسينات المطبقة:")
        
        # فحص البيانات
        print(f"   👥 عدد الموظفين: {len(leave_dept.employees_data)}")
        print(f"   💰 عدد الأرصدة: {len(leave_dept.balance_data)}")
        
        # فحص الدوال المحسنة
        improved_functions = [
            ("on_employee_selected", "اختيار الموظف المحسن"),
            ("get_employee_type", "تحديد نوع الموظف المحسن"),
            ("get_employee_balance", "الحصول على الرصيد المحسن"),
            ("refresh_pending_leaves", "تحديث قائمة الإجازات المحسن"),
            ("select_date", "اختيار التاريخ الجديد")
        ]
        
        for func_name, description in improved_functions:
            if hasattr(leave_dept, func_name):
                print(f"   ✅ {description}: موجودة")
            else:
                print(f"   ❌ {description}: غير موجودة")
        
        # إنشاء واجهة تفاعلية للاختبار
        create_improvements_test_interface(root, leave_dept)
        
        print("\n🎉 انتهى الاختبار - الواجهة التفاعلية جاهزة")
        print("\n📋 تعليمات الاختبار:")
        print("   • اختبر اختيار الموظفين وعرض المعلومات")
        print("   • جرب أدوات اختيار التاريخ الجديدة")
        print("   • راجع تحسينات عرض الأرصدة")
        print("   • فحص تنسيق التواريخ")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def create_improvements_test_interface(root, leave_dept):
    """إنشاء واجهة تفاعلية لاختبار التحسينات"""
    
    # إطار الاختبار
    test_frame = tk.Frame(root, bg="#e8f5e8")
    test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
    
    # عنوان
    title_label = tk.Label(test_frame, text="🔧 اختبار تحسينات قسم الإجازات", 
                          bg="#e8f5e8", fg="#2e7d32", font=("Arial", 14, "bold"))
    title_label.pack(pady=5)
    
    # معلومات التحسينات
    info_frame = tk.LabelFrame(test_frame, text="التحسينات المطبقة", 
                              bg="#e8f5e8", fg="#2e7d32", font=("Arial", 12, "bold"))
    info_frame.pack(fill=tk.X, pady=5)
    
    # إحصائيات
    stats_text = f"""
🔧 التحسينات المطبقة:

👥 تحسينات اختيار الموظف:
• عرض اسم الموظف من حقول متعددة
• إظهار المسمى الوظيفي مع نوع الموظف
• عرض تفصيلي للرصيد المتبقي
• معالجة حالات العجز في الرصيد

📅 تحسينات التواريخ:
• أدوات اختيار التاريخ التفاعلية
• تنسيق موحد للتواريخ (YYYY-MM-DD)
• معاينة فورية للتاريخ المختار
• أزرار سريعة لاختيار اليوم الحالي

💰 تحسينات الأرصدة:
• دعم حقول متعددة للأرصدة
• معالجة أفضل للأخطاء
• عرض تفصيلي (تلقائي + يدوي)
• حساب دقيق للرصيد المتبقي

📊 تحسينات العرض:
• تنسيق أفضل لقوائم الإجازات
• عرض معلومات شاملة للموظفين
• تحسين تخطيط الواجهة
• رسائل خطأ واضحة

📊 البيانات المحملة:
• عدد الموظفين: {len(leave_dept.employees_data)}
• عدد الأرصدة: {len(leave_dept.balance_data)}
    """
    
    stats_label = tk.Label(info_frame, text=stats_text, bg="#e8f5e8", 
                          fg="#2e7d32", font=("Arial", 10), justify=tk.LEFT)
    stats_label.pack(padx=10, pady=5)
    
    # متغير لعرض النتائج
    result_var = tk.StringVar()
    result_label = tk.Label(test_frame, textvariable=result_var, bg="#e8f5e8", 
                           fg="#2e7d32", font=("Arial", 11, "bold"))
    result_label.pack(pady=5)
    
    # أزرار الاختبار
    buttons_frame = tk.Frame(test_frame, bg="#e8f5e8")
    buttons_frame.pack(pady=10)
    
    def test_employee_selection():
        """اختبار تحسينات اختيار الموظف"""
        try:
            print("\n👥 اختبار تحسينات اختيار الموظف...")
            
            if leave_dept.employees_data:
                # اختبار أول موظف
                emp = leave_dept.employees_data[0]
                emp_id = emp.get("الرقم الوظيفي")
                emp_name = emp.get("الاسم العربي", "") or emp.get("الاسم", "")
                
                print(f"   📝 اختبار الموظف: {emp_name} ({emp_id})")
                
                # اختبار تحديد نوع الموظف
                emp_type = leave_dept.get_employee_type(emp_id)
                print(f"   🏷️ نوع الموظف: {emp_type}")
                
                # اختبار الحصول على الرصيد
                balance = leave_dept.get_employee_balance(emp_id)
                print(f"   💰 الرصيد: إجمالي={balance['total']}, متبقي={balance['remaining']}")
                
                result_var.set(f"✅ تم اختبار الموظف {emp_name}: {emp_type}, رصيد {balance['remaining']}")
            else:
                result_var.set("⚠️ لا توجد بيانات موظفين للاختبار")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار اختيار الموظف: {e}")
            result_var.set(f"❌ خطأ اختيار الموظف: {e}")
    
    def test_employee_types():
        """اختبار تصنيف أنواع الموظفين"""
        try:
            print("\n🏷️ اختبار تصنيف أنواع الموظفين...")
            
            employees_count = 0
            teachers_count = 0
            
            for emp in leave_dept.employees_data:
                emp_id = emp.get("الرقم الوظيفي")
                emp_type = leave_dept.get_employee_type(emp_id)
                job_title = emp.get("المسمى الوظيفي", "")
                
                if emp_type == "teacher":
                    teachers_count += 1
                    print(f"     👨‍🏫 معلم: {emp.get('الاسم العربي', '')} - {job_title}")
                else:
                    employees_count += 1
                    if employees_count <= 3:  # عرض أول 3 موظفين فقط
                        print(f"     👤 موظف: {emp.get('الاسم العربي', '')} - {job_title}")
            
            result_var.set(f"🏷️ التصنيف: {employees_count} موظف، {teachers_count} معلم")
            print(f"   📊 النتيجة: {employees_count} موظف، {teachers_count} معلم")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار تصنيف الموظفين: {e}")
            result_var.set(f"❌ خطأ التصنيف: {e}")
    
    def test_balance_calculations():
        """اختبار حسابات الأرصدة المحسنة"""
        try:
            print("\n💰 اختبار حسابات الأرصدة المحسنة...")
            
            total_balance = 0
            employees_with_balance = 0
            negative_balances = 0
            
            for emp in leave_dept.employees_data:
                emp_id = emp.get("الرقم الوظيفي")
                emp_type = leave_dept.get_employee_type(emp_id)
                
                if emp_type == "employee":  # فقط للموظفين
                    balance = leave_dept.get_employee_balance(emp_id)
                    total_balance += balance['remaining']
                    employees_with_balance += 1
                    
                    if balance['remaining'] < 0:
                        negative_balances += 1
                        print(f"     ⚠️ عجز: {emp.get('الاسم العربي', '')} - {balance['remaining']} يوم")
            
            if employees_with_balance > 0:
                avg_balance = total_balance / employees_with_balance
                result_var.set(f"💰 متوسط الرصيد: {avg_balance:.1f} يوم، {negative_balances} حالة عجز")
                print(f"   📊 متوسط الرصيد: {avg_balance:.1f} يوم")
                print(f"   ⚠️ حالات العجز: {negative_balances}")
            else:
                result_var.set("⚠️ لا توجد أرصدة للموظفين")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار الأرصدة: {e}")
            result_var.set(f"❌ خطأ الأرصدة: {e}")
    
    def test_date_formatting():
        """اختبار تنسيق التواريخ"""
        try:
            print("\n📅 اختبار تنسيق التواريخ...")
            
            # اختبار تواريخ مختلفة
            test_dates = [
                "2024-12-15",
                "2024-01-01", 
                "2024-06-30"
            ]
            
            for test_date in test_dates:
                working_days = leave_dept.calculate_working_days(test_date, "2024-12-21")
                print(f"   📅 من {test_date} إلى 2024-12-21: {working_days} يوم عمل")
            
            result_var.set(f"📅 تم اختبار تنسيق التواريخ بنجاح")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار التواريخ: {e}")
            result_var.set(f"❌ خطأ التواريخ: {e}")
    
    def test_interface_improvements():
        """اختبار تحسينات الواجهة"""
        try:
            print("\n🖥️ اختبار تحسينات الواجهة...")
            
            # فتح نافذة قسم الإجازات
            leave_dept.show_leave_department()
            
            result_var.set("🖥️ تم فتح نافذة قسم الإجازات - راجع التحسينات")
            print("   ✅ تم فتح نافذة قسم الإجازات")
            print("   📋 راجع التحسينات في:")
            print("     • تبويب طلب الإجازة - أدوات اختيار التاريخ")
            print("     • تبويب الموافقة - عرض معلومات الموظفين")
            print("     • تحسينات عامة في التخطيط")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار الواجهة: {e}")
            result_var.set(f"❌ خطأ الواجهة: {e}")
    
    def show_improvements_summary():
        """عرض ملخص التحسينات"""
        summary = """
🔧 ملخص التحسينات المطبقة:

👥 تحسينات اختيار الموظف:
   ✅ عرض اسم الموظف من حقول متعددة
   ✅ إظهار المسمى الوظيفي مع نوع الموظف
   ✅ عرض تفصيلي للرصيد (متبقي/عجز)
   ✅ معالجة حالات البيانات المفقودة

📅 تحسينات التواريخ:
   ✅ أدوات اختيار التاريخ التفاعلية
   ✅ نافذة منبثقة لاختيار السنة/الشهر/اليوم
   ✅ معاينة فورية للتاريخ المختار
   ✅ أزرار سريعة (اليوم، تأكيد، إلغاء)
   ✅ تنسيق موحد (YYYY-MM-DD)

💰 تحسينات الأرصدة:
   ✅ دعم حقول متعددة للبيانات
   ✅ حساب دقيق (تلقائي + يدوي - مستخدم)
   ✅ معالجة أخطاء التحويل الرقمي
   ✅ عرض تفصيلي للرصيد

🏷️ تحسينات تصنيف الموظفين:
   ✅ فحص كلمات دالة متعددة
   ✅ دعم حقول وظيفية مختلفة
   ✅ فحص القسم/الإدارة كمرجع إضافي
   ✅ تصنيف أكثر دقة

📊 تحسينات العرض:
   ✅ تنسيق أفضل لقوائم الإجازات
   ✅ عرض معلومات شاملة
   ✅ رسائل خطأ واضحة
   ✅ تحسين تخطيط الواجهة

🎯 النتيجة: قسم إجازات محسن وأكثر دقة!
        """
        
        messagebox.showinfo("ملخص التحسينات", summary)
        result_var.set("📋 تم عرض ملخص التحسينات")
    
    def show_usage_tips():
        """عرض نصائح الاستخدام"""
        tips = """
💡 نصائح استخدام قسم الإجازات المحسن:

👥 اختيار الموظف:
   • ستظهر معلومات مفصلة عند اختيار الموظف
   • يتم عرض المسمى الوظيفي مع نوع الموظف
   • الرصيد يظهر بوضوح (متبقي أو عجز)

📅 اختيار التواريخ:
   • اضغط على زر 📅 لفتح أداة اختيار التاريخ
   • استخدم القوائم المنسدلة للسنة والشهر واليوم
   • راجع المعاينة قبل التأكيد
   • اضغط "اليوم" لاختيار التاريخ الحالي سريعاً

💰 مراجعة الأرصدة:
   • الموظفين: يظهر الرصيد المتبقي بالأيام
   • المعلمين: يظهر "غير محدود"
   • حالات العجز تظهر بوضوح

📊 مراجعة الإجازات:
   • قائمة الإجازات المعلقة تظهر معلومات شاملة
   • أسماء الموظفين واضحة ومفصلة
   • التواريخ منسقة بشكل موحد

⚠️ معالجة الأخطاء:
   • رسائل خطأ واضحة ومفيدة
   • معالجة البيانات المفقودة
   • تحقق من صحة التواريخ

🎯 للحصول على أفضل تجربة:
   • تأكد من اكتمال بيانات الموظفين
   • راجع الأرصدة دورياً
   • استخدم أدوات اختيار التاريخ للدقة
        """
        
        messagebox.showinfo("نصائح الاستخدام", tips)
        result_var.set("💡 تم عرض نصائح الاستخدام")
    
    # أزرار الاختبار
    tk.Label(buttons_frame, text="اختبارات التحسينات:", bg="#e8f5e8", 
            font=("Arial", 12, "bold")).pack()
    
    buttons_row1 = tk.Frame(buttons_frame, bg="#e8f5e8")
    buttons_row1.pack(pady=5)
    
    tk.Button(buttons_row1, text="👥 اختيار الموظف",
             command=test_employee_selection,
             bg="#4caf50", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row1, text="🏷️ تصنيف الموظفين",
             command=test_employee_types,
             bg="#2196f3", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row1, text="💰 حسابات الأرصدة",
             command=test_balance_calculations,
             bg="#ff9800", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    buttons_row2 = tk.Frame(buttons_frame, bg="#e8f5e8")
    buttons_row2.pack(pady=5)
    
    tk.Button(buttons_row2, text="📅 تنسيق التواريخ",
             command=test_date_formatting,
             bg="#9c27b0", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row2, text="🖥️ تحسينات الواجهة",
             command=test_interface_improvements,
             bg="#e74c3c", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    buttons_row3 = tk.Frame(buttons_frame, bg="#e8f5e8")
    buttons_row3.pack(pady=5)
    
    tk.Button(buttons_row3, text="📋 ملخص التحسينات",
             command=show_improvements_summary,
             bg="#795548", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row3, text="💡 نصائح الاستخدام",
             command=show_usage_tips,
             bg="#607d8b", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    # معلومات إضافية
    info_frame = tk.Frame(test_frame, bg="#e8f5e8")
    info_frame.pack(pady=10)
    
    info_text = """
🎯 التحسينات الرئيسية:
• عرض أسماء الموظفين من حقول متعددة
• تحسين دقة تصنيف الموظفين (موظف/معلم)
• أدوات اختيار التاريخ التفاعلية
• عرض تفصيلي للأرصدة مع معالجة العجز
• تحسين تخطيط وتنسيق الواجهة
    """
    info_label = tk.Label(info_frame, text=info_text, bg="#e8f5e8", 
                         fg="#2e7d32", font=("Arial", 10), justify=tk.LEFT)
    info_label.pack()
    
    # زر إغلاق
    tk.Button(test_frame, text="❌ إغلاق الاختبار", 
             command=root.destroy,
             bg="#616161", fg="white", 
             font=("Arial", 12, "bold")).pack(pady=15)

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار تحسينات قسم الإجازات")
    print("=" * 60)
    
    test_leave_department_improvements()

if __name__ == "__main__":
    main()
