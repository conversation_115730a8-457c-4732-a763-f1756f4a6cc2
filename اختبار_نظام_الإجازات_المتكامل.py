#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إدارة الإجازات المتكامل
Test Integrated Leave Management System
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
from datetime import datetime, timedelta

def test_integrated_leave_system():
    """اختبار النظام المتكامل"""
    print("🏢 اختبار نظام إدارة الإجازات المتكامل")
    print("=" * 60)
    
    try:
        # استيراد النظام
        print("📦 استيراد النظام المتكامل...")
        import integrated_leave_management_system
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة الاختبار...")
        root = tk.Tk()
        root.title("اختبار نظام الإجازات المتكامل")
        root.geometry("1400x900")
        
        # إنشاء النظام
        print("🔧 إنشاء النظام المتكامل...")
        leave_system = integrated_leave_management_system.IntegratedLeaveManagementSystem(root)
        print("✅ تم إنشاء النظام بنجاح")
        
        # فحص المكونات
        print("\n🔍 فحص مكونات النظام:")
        
        # فحص البيانات
        print(f"   👥 عدد الموظفين: {len(leave_system.employees_data)}")
        print(f"   🏖️ عدد الإجازات: {len(leave_system.leaves_data)}")
        print(f"   💰 عدد الأرصدة: {len(leave_system.balance_data)}")
        
        # فحص الدوال الرئيسية
        main_functions = [
            ("get_employee_type", "تحديد نوع الموظف"),
            ("calculate_working_days", "حساب أيام العمل"),
            ("get_employee_balance", "الحصول على الرصيد"),
            ("submit_leave_request", "تقديم طلب إجازة"),
            ("approve_selected_leave", "الموافقة على الإجازة"),
            ("reject_selected_leave", "رفض الإجازة"),
            ("check_leave_alerts", "فحص التنبيهات"),
            ("generate_comprehensive_report", "التقرير الشامل")
        ]
        
        for func_name, description in main_functions:
            if hasattr(leave_system, func_name):
                print(f"   ✅ {description}: موجودة")
            else:
                print(f"   ❌ {description}: غير موجودة")
        
        # إنشاء واجهة تفاعلية للاختبار
        create_integrated_test_interface(root, leave_system)
        
        print("\n🎉 انتهى الاختبار - الواجهة التفاعلية جاهزة")
        print("\n📋 تعليمات الاختبار:")
        print("   • جرب تقديم طلب إجازة جديد")
        print("   • اختبر الموافقة والرفض")
        print("   • راجع التقارير والإحصائيات")
        print("   • فحص التنبيهات")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def create_integrated_test_interface(root, leave_system):
    """إنشاء واجهة تفاعلية لاختبار النظام المتكامل"""
    
    # إطار الاختبار
    test_frame = tk.Frame(root, bg="#e8f4fd")
    test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
    
    # عنوان
    title_label = tk.Label(test_frame, text="🔧 اختبار نظام إدارة الإجازات المتكامل", 
                          bg="#e8f4fd", fg="#1565c0", font=("Arial", 14, "bold"))
    title_label.pack(pady=5)
    
    # معلومات النظام
    info_frame = tk.LabelFrame(test_frame, text="معلومات النظام", 
                              bg="#e8f4fd", fg="#1565c0", font=("Arial", 12, "bold"))
    info_frame.pack(fill=tk.X, pady=5)
    
    # إحصائيات
    stats_text = f"""
🏢 النظام المتكامل لإدارة الإجازات:

📊 البيانات المحملة:
• عدد الموظفين: {len(leave_system.employees_data)}
• عدد الإجازات المسجلة: {len(leave_system.leaves_data)}
• عدد أرصدة الإجازات: {len(leave_system.balance_data)}

🔧 المميزات الرئيسية:
• تصنيف المستخدمين (موظف/معلم)
• حساب أيام العمل الفعلية
• خصم تلقائي للموظفين
• منح يدوي للمعلمين
• تنبيهات قبل انتهاء الإجازة
• تقارير شاملة ومفصلة

📋 حالات الإجازة:
• في الانتظار → موافق عليها/مرفوضة
• تتبع تلقائي للرصيد
• ترحيل الرصيد المتبقي
    """
    
    stats_label = tk.Label(info_frame, text=stats_text, bg="#e8f4fd", 
                          fg="#1565c0", font=("Arial", 10), justify=tk.LEFT)
    stats_label.pack(padx=10, pady=5)
    
    # متغير لعرض النتائج
    result_var = tk.StringVar()
    result_label = tk.Label(test_frame, textvariable=result_var, bg="#e8f4fd", 
                           fg="#1565c0", font=("Arial", 11, "bold"))
    result_label.pack(pady=5)
    
    # أزرار الاختبار
    buttons_frame = tk.Frame(test_frame, bg="#e8f4fd")
    buttons_frame.pack(pady=10)
    
    def test_employee_classification():
        """اختبار تصنيف الموظفين"""
        try:
            print("\n👥 اختبار تصنيف الموظفين...")
            
            employees_count = 0
            teachers_count = 0
            
            for emp in leave_system.employees_data:
                emp_id = emp.get("الرقم الوظيفي")
                emp_type = leave_system.get_employee_type(emp_id)
                
                if emp_type == "teacher":
                    teachers_count += 1
                else:
                    employees_count += 1
            
            result_var.set(f"✅ التصنيف: {employees_count} موظف، {teachers_count} معلم")
            print(f"   📊 الموظفين: {employees_count}")
            print(f"   📊 المعلمين: {teachers_count}")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار التصنيف: {e}")
            result_var.set(f"❌ خطأ التصنيف: {e}")
    
    def test_working_days_calculation():
        """اختبار حساب أيام العمل"""
        try:
            print("\n📅 اختبار حساب أيام العمل...")
            
            # اختبار فترة أسبوع واحد
            start_date = "2024-12-15"  # أحد
            end_date = "2024-12-21"    # سبت
            
            working_days = leave_system.calculate_working_days(start_date, end_date)
            
            result_var.set(f"✅ أيام العمل من {start_date} إلى {end_date}: {working_days} يوم")
            print(f"   📊 أيام العمل المحسوبة: {working_days} يوم")
            print(f"   📝 (استثناء الجمعة والسبت)")
            
        except Exception as e:
            print(f"   ❌ خطأ في حساب أيام العمل: {e}")
            result_var.set(f"❌ خطأ حساب الأيام: {e}")
    
    def test_balance_system():
        """اختبار نظام الأرصدة"""
        try:
            print("\n💰 اختبار نظام الأرصدة...")
            
            if leave_system.employees_data:
                # اختبار أول موظف
                emp = leave_system.employees_data[0]
                emp_id = emp.get("الرقم الوظيفي")
                emp_type = leave_system.get_employee_type(emp_id)
                
                if emp_type == "employee":
                    balance = leave_system.get_employee_balance(emp_id)
                    result_var.set(f"✅ رصيد الموظف {emp_id}: {balance['remaining']} يوم متبقي")
                    print(f"   📊 الرصيد الإجمالي: {balance['total']}")
                    print(f"   📊 المستخدم: {balance['used']}")
                    print(f"   📊 المتبقي: {balance['remaining']}")
                else:
                    result_var.set(f"✅ المعلم {emp_id}: رصيد غير محدود")
                    print(f"   📊 المعلم: رصيد غير محدود")
            else:
                result_var.set("⚠️ لا توجد بيانات موظفين")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار الأرصدة: {e}")
            result_var.set(f"❌ خطأ الأرصدة: {e}")
    
    def test_alerts_system():
        """اختبار نظام التنبيهات"""
        try:
            print("\n⚠️ اختبار نظام التنبيهات...")
            
            alerts = leave_system.check_leave_alerts()
            
            if alerts:
                result_var.set(f"⚠️ يوجد {len(alerts)} تنبيه")
                print(f"   📊 عدد التنبيهات: {len(alerts)}")
                for alert in alerts[:3]:  # عرض أول 3 تنبيهات
                    print(f"     • {alert}")
            else:
                result_var.set("✅ لا توجد تنبيهات")
                print("   ✅ لا توجد تنبيهات حالياً")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار التنبيهات: {e}")
            result_var.set(f"❌ خطأ التنبيهات: {e}")
    
    def test_reports_system():
        """اختبار نظام التقارير"""
        try:
            print("\n📊 اختبار نظام التقارير...")
            
            # اختبار حساب الإحصائيات
            stats = leave_system.calculate_leave_statistics()
            
            result_var.set(f"📊 إحصائيات: {stats['total_leaves']} إجازة، {stats['approved_leaves']} موافق عليها")
            
            print(f"   📊 إجمالي الإجازات: {stats['total_leaves']}")
            print(f"   ✅ الموافق عليها: {stats['approved_leaves']}")
            print(f"   ❌ المرفوضة: {stats['rejected_leaves']}")
            print(f"   ⏳ في الانتظار: {stats['pending_leaves']}")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار التقارير: {e}")
            result_var.set(f"❌ خطأ التقارير: {e}")
    
    def test_leave_workflow():
        """اختبار سير عمل الإجازات"""
        try:
            print("\n🔄 اختبار سير عمل الإجازات...")
            
            # فحص الحالات المختلفة
            states = {}
            for leave in leave_system.leaves_data:
                state = leave.get("الحالة", "غير محدد")
                states[state] = states.get(state, 0) + 1
            
            states_text = ", ".join([f"{state}: {count}" for state, count in states.items()])
            result_var.set(f"🔄 حالات الإجازات: {states_text}")
            
            print("   📊 حالات الإجازات:")
            for state, count in states.items():
                print(f"     • {state}: {count}")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار سير العمل: {e}")
            result_var.set(f"❌ خطأ سير العمل: {e}")
    
    def show_system_features():
        """عرض مميزات النظام"""
        features = """
🏢 مميزات نظام إدارة الإجازات المتكامل:

🔧 تصنيف المستخدمين:
   • الموظف/الموظفة: خصم تلقائي من الرصيد
   • المعلم/المعلمة: منح يدوي بدون خصم

📅 حساب أيام العمل:
   • استثناء الجمعة والسبت تلقائياً
   • حساب دقيق للفترات

🔄 آلية العمل:
   • حالات: في الانتظار → موافق/مرفوض
   • تتبع تلقائي للرصيد
   • ترحيل الرصيد المتبقي

⚠️ التنبيهات:
   • تنبيه قبل 3 أيام من انتهاء إجازة المعلمين
   • مراقبة الأرصدة المنخفضة

📊 التقارير:
   • تقرير شامل لجميع الإجازات
   • تقرير خاص بكل موظف
   • إحصائيات مفصلة
   • تقرير التنبيهات

💾 التصدير:
   • ملفات Excel منسقة
   • حفظ خارج النظام
   • تقارير متعددة الأوراق

🎯 النتيجة: نظام متكامل وشامل!
        """
        
        messagebox.showinfo("مميزات النظام المتكامل", features)
        result_var.set("📋 تم عرض مميزات النظام")
    
    def show_usage_guide():
        """عرض دليل الاستخدام"""
        guide = """
📖 دليل استخدام النظام المتكامل:

📝 تقديم طلب إجازة:
   1. اختر تبويب "طلب إجازة"
   2. حدد الموظف من القائمة
   3. اختر نوع الإجازة والتواريخ
   4. اضغط "حساب الأيام" للتحقق
   5. أدخل سبب الإجازة
   6. اضغط "تقديم الطلب"

✅ الموافقة على الإجازات:
   1. اختر تبويب "الموافقة على الإجازات"
   2. راجع الطلبات المعلقة
   3. حدد الطلب المطلوب
   4. اضغط "موافقة" أو "رفض"
   5. أضف ملاحظات إذا لزم الأمر

📊 التقارير:
   1. اختر تبويب "التقارير"
   2. اختر نوع التقرير المطلوب
   3. راجع المعاينة
   4. اضغط على التقرير لتصديره

⚙️ الإعدادات:
   1. اختر تبويب "الإعدادات"
   2. عدل أيام العطل الأسبوعية
   3. اضبط فترة التنبيهات
   4. احفظ التغييرات

🎯 نصائح:
   • راجع التنبيهات دورياً
   • استخدم التقارير لمتابعة الأرصدة
   • احفظ التقارير خارج النظام
        """
        
        messagebox.showinfo("دليل الاستخدام", guide)
        result_var.set("📖 تم عرض دليل الاستخدام")
    
    # أزرار الاختبار
    tk.Label(buttons_frame, text="اختبارات النظام:", bg="#e8f4fd", 
            font=("Arial", 12, "bold")).pack()
    
    buttons_row1 = tk.Frame(buttons_frame, bg="#e8f4fd")
    buttons_row1.pack(pady=5)
    
    tk.Button(buttons_row1, text="👥 تصنيف الموظفين",
             command=test_employee_classification,
             bg="#2196f3", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row1, text="📅 حساب أيام العمل",
             command=test_working_days_calculation,
             bg="#4caf50", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row1, text="💰 نظام الأرصدة",
             command=test_balance_system,
             bg="#ff9800", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    buttons_row2 = tk.Frame(buttons_frame, bg="#e8f4fd")
    buttons_row2.pack(pady=5)
    
    tk.Button(buttons_row2, text="⚠️ التنبيهات",
             command=test_alerts_system,
             bg="#e74c3c", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row2, text="📊 التقارير",
             command=test_reports_system,
             bg="#9c27b0", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row2, text="🔄 سير العمل",
             command=test_leave_workflow,
             bg="#607d8b", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    buttons_row3 = tk.Frame(buttons_frame, bg="#e8f4fd")
    buttons_row3.pack(pady=5)
    
    tk.Button(buttons_row3, text="🏢 مميزات النظام",
             command=show_system_features,
             bg="#795548", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(buttons_row3, text="📖 دليل الاستخدام",
             command=show_usage_guide,
             bg="#455a64", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    # معلومات إضافية
    info_frame = tk.Frame(test_frame, bg="#e8f4fd")
    info_frame.pack(pady=10)
    
    info_text = """
💡 ملاحظات مهمة:
• النظام يدعم تصنيف الموظفين تلقائياً
• حساب أيام العمل يستثني الجمعة والسبت
• الموظفين: خصم تلقائي من الرصيد
• المعلمين: منح يدوي بدون خصم
• تنبيهات تلقائية قبل انتهاء الإجازة
• تقارير شاملة ومفصلة
    """
    info_label = tk.Label(info_frame, text=info_text, bg="#e8f4fd", 
                         fg="#1565c0", font=("Arial", 10), justify=tk.LEFT)
    info_label.pack()
    
    # زر إغلاق
    tk.Button(test_frame, text="❌ إغلاق الاختبار", 
             command=root.destroy,
             bg="#616161", fg="white", 
             font=("Arial", 12, "bold")).pack(pady=15)

def main():
    """الدالة الرئيسية"""
    print("🏢 اختبار نظام إدارة الإجازات المتكامل")
    print("=" * 60)
    
    test_integrated_leave_system()

if __name__ == "__main__":
    main()
