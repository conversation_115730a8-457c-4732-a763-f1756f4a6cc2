#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي لنظام الإجازات بعد الإصلاحات
Final Test for Leave System After Fixes
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
from datetime import datetime, timed<PERSON><PERSON>

def run_final_test():
    """تشغيل الاختبار النهائي"""
    
    print("🔍 اختبار نهائي لنظام الإجازات بعد الإصلاحات")
    print("=" * 60)
    
    test_results = []
    
    try:
        # استيراد النظام
        print("📦 استيراد نظام الإجازات...")
        import leave_department_system
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.title("اختبار نهائي - نظام الإجازات")
        root.geometry("1000x700")
        root.configure(bg="#f0f8ff")
        
        # إنشاء النظام
        print("🔧 إنشاء نظام الإجازات...")
        leave_dept = leave_department_system.LeaveDepartmentSystem(root)
        print("✅ تم إنشاء النظام بنجاح")
        
        # إنشاء واجهة الاختبار
        create_final_test_interface(root, leave_dept, test_results)
        
        print("\n🎉 الاختبار النهائي جاهز!")
        print("📋 اضغط على الأزرار لاختبار الوظائف المحسنة")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار النهائي: {e}")
        import traceback
        traceback.print_exc()

def create_final_test_interface(root, leave_dept, test_results):
    """إنشاء واجهة الاختبار النهائي"""
    
    # عنوان
    title_frame = tk.Frame(root, bg="#2c3e50", height=80)
    title_frame.pack(fill=tk.X)
    title_frame.pack_propagate(False)
    
    tk.Label(title_frame, text="🔍 اختبار نهائي - نظام الإجازات المحسن", 
            font=("Arial", 18, "bold"), bg="#2c3e50", fg="white").pack(expand=True)
    
    # إطار المحتوى
    main_frame = tk.Frame(root, bg="#f0f8ff")
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # إطار الاختبارات
    tests_frame = tk.LabelFrame(main_frame, text="🧪 اختبارات الوظائف المحسنة", 
                               font=("Arial", 14, "bold"), fg="#2c3e50", bg="#f0f8ff")
    tests_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
    
    # إطار النتائج
    results_frame = tk.LabelFrame(main_frame, text="📊 نتائج الاختبارات", 
                                 font=("Arial", 14, "bold"), fg="#2c3e50", bg="#f0f8ff")
    results_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
    
    # منطقة النتائج
    results_text = tk.Text(results_frame, font=("Arial", 11), bg="white", 
                          fg="#2c3e50", wrap=tk.WORD, height=25)
    results_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    def log_test_result(test_name, result, details=""):
        """تسجيل نتيجة الاختبار"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        result_line = f"[{timestamp}] {result} {test_name}"
        if details:
            result_line += f" - {details}"
        result_line += "\n"
        
        results_text.insert(tk.END, result_line)
        results_text.see(tk.END)
        root.update()
        
        test_results.append({
            "test": test_name,
            "result": result,
            "details": details,
            "timestamp": timestamp
        })
    
    # اختبارات الوظائف الجديدة
    def test_date_validation():
        """اختبار التحقق من صحة التواريخ"""
        try:
            log_test_result("التحقق من التواريخ", "🔄 جاري الاختبار...")
            
            # اختبار تواريخ صحيحة
            is_valid, msg = leave_dept.validate_dates("2025-06-20", "2025-06-25")
            if is_valid:
                log_test_result("التواريخ الصحيحة", "✅ نجح", "تم قبول التواريخ الصحيحة")
            else:
                log_test_result("التواريخ الصحيحة", "❌ فشل", msg)
            
            # اختبار تواريخ خاطئة
            is_valid, msg = leave_dept.validate_dates("2025-06-30", "2025-06-25")
            if not is_valid:
                log_test_result("التواريخ الخاطئة", "✅ نجح", "تم رفض التواريخ الخاطئة")
            else:
                log_test_result("التواريخ الخاطئة", "❌ فشل", "لم يتم رفض التواريخ الخاطئة")
                
        except Exception as e:
            log_test_result("التحقق من التواريخ", "❌ فشل", f"خطأ: {str(e)}")
    
    def test_balance_validation():
        """اختبار التحقق من الرصيد"""
        try:
            log_test_result("التحقق من الرصيد", "🔄 جاري الاختبار...")
            
            if leave_dept.employees_data:
                emp_id = str(leave_dept.employees_data[0].get("الرقم الوظيفي", ""))
                
                # اختبار رصيد كافي
                is_sufficient, msg = leave_dept.validate_leave_balance(emp_id, 5)
                log_test_result("فحص الرصيد", "✅ نجح", msg)
                
                # اختبار رصيد غير كافي
                is_sufficient, msg = leave_dept.validate_leave_balance(emp_id, 1000)
                if not is_sufficient:
                    log_test_result("رصيد غير كافي", "✅ نجح", "تم اكتشاف الرصيد غير الكافي")
                else:
                    log_test_result("رصيد غير كافي", "⚠️ تحذير", "لم يتم اكتشاف الرصيد غير الكافي")
            else:
                log_test_result("التحقق من الرصيد", "❌ فشل", "لا توجد بيانات موظفين")
                
        except Exception as e:
            log_test_result("التحقق من الرصيد", "❌ فشل", f"خطأ: {str(e)}")
    
    def test_backup_system():
        """اختبار نظام النسخ الاحتياطي"""
        try:
            log_test_result("النسخ الاحتياطي", "🔄 جاري الاختبار...")
            
            success, msg = leave_dept.create_backup()
            if success:
                log_test_result("النسخ الاحتياطي", "✅ نجح", msg)
            else:
                log_test_result("النسخ الاحتياطي", "❌ فشل", msg)
                
        except Exception as e:
            log_test_result("النسخ الاحتياطي", "❌ فشل", f"خطأ: {str(e)}")
    
    def test_enhanced_statistics():
        """اختبار الإحصائيات المحسنة"""
        try:
            log_test_result("الإحصائيات المحسنة", "🔄 جاري الاختبار...")
            
            stats = leave_dept.get_enhanced_statistics()
            if stats:
                log_test_result("الإحصائيات المحسنة", "✅ نجح", 
                               f"تم حساب {len(stats)} إحصائية")
                
                # عرض بعض الإحصائيات
                log_test_result("إجمالي الموظفين", "ℹ️ معلومات", 
                               f"{stats.get('total_employees', 0)} موظف")
                log_test_result("إجمالي الإجازات", "ℹ️ معلومات", 
                               f"{stats.get('total_leaves', 0)} إجازة")
                log_test_result("معدل الموافقة", "ℹ️ معلومات", 
                               f"{stats.get('approval_rate', 0):.1f}%")
            else:
                log_test_result("الإحصائيات المحسنة", "❌ فشل", "لم يتم حساب الإحصائيات")
                
        except Exception as e:
            log_test_result("الإحصائيات المحسنة", "❌ فشل", f"خطأ: {str(e)}")
    
    def test_ui_responsiveness():
        """اختبار استجابة الواجهة"""
        try:
            log_test_result("استجابة الواجهة", "🔄 جاري الاختبار...")
            
            success = leave_dept.enhance_ui_responsiveness()
            if success:
                log_test_result("استجابة الواجهة", "✅ نجح", "تم تحسين استجابة الواجهة")
            else:
                log_test_result("استجابة الواجهة", "❌ فشل", "فشل في تحسين الواجهة")
                
        except Exception as e:
            log_test_result("استجابة الواجهة", "❌ فشل", f"خطأ: {str(e)}")
    
    def test_complete_workflow():
        """اختبار سير العمل الكامل"""
        try:
            log_test_result("سير العمل الكامل", "🔄 جاري الاختبار...")
            
            # 1. اختيار موظف
            if leave_dept.employees_data:
                emp_id = str(leave_dept.employees_data[0].get("الرقم الوظيفي", ""))
                leave_dept.emp_id_var.set(emp_id)
                leave_dept.on_employee_selected()
                log_test_result("اختيار الموظف", "✅ نجح", f"تم اختيار الموظف: {emp_id}")
            
            # 2. تعيين بيانات الإجازة
            leave_dept.leave_type_var.set("إجازة سنوية")
            leave_dept.start_date_var.set("2025-06-22")
            leave_dept.end_date_var.set("2025-06-26")
            log_test_result("تعيين البيانات", "✅ نجح", "تم تعيين بيانات الإجازة")
            
            # 3. حساب الأيام
            leave_dept.calculate_leave_days()
            days_result = leave_dept.leave_days_var.get()
            if days_result:
                log_test_result("حساب الأيام", "✅ نجح", f"النتيجة: {days_result}")
            else:
                log_test_result("حساب الأيام", "❌ فشل", "لم يتم حساب الأيام")
            
            # 4. إدخال السبب
            if hasattr(leave_dept, 'leave_reason_text'):
                leave_dept.leave_reason_text.delete("1.0", tk.END)
                leave_dept.leave_reason_text.insert("1.0", "إجازة اختبار نهائي")
                log_test_result("إدخال السبب", "✅ نجح", "تم إدخال سبب الإجازة")
            
            log_test_result("سير العمل الكامل", "✅ نجح", "تم اختبار جميع الخطوات بنجاح")
                
        except Exception as e:
            log_test_result("سير العمل الكامل", "❌ فشل", f"خطأ: {str(e)}")
    
    def run_all_final_tests():
        """تشغيل جميع الاختبارات النهائية"""
        log_test_result("بدء الاختبارات", "🚀 بدء", "تشغيل جميع الاختبارات النهائية")
        
        tests = [
            ("التحقق من التواريخ", test_date_validation),
            ("التحقق من الرصيد", test_balance_validation),
            ("النسخ الاحتياطي", test_backup_system),
            ("الإحصائيات المحسنة", test_enhanced_statistics),
            ("استجابة الواجهة", test_ui_responsiveness),
            ("سير العمل الكامل", test_complete_workflow)
        ]
        
        for test_name, test_func in tests:
            try:
                test_func()
                root.after(1000)  # توقف قصير بين الاختبارات
            except Exception as e:
                log_test_result(test_name, "❌ فشل", f"خطأ في التشغيل: {str(e)}")
        
        # ملخص النتائج
        total = len(test_results)
        passed = len([r for r in test_results if r["result"] == "✅ نجح"])
        failed = len([r for r in test_results if r["result"] == "❌ فشل"])
        
        log_test_result("ملخص النتائج", "📊 انتهى", 
                       f"المجموع: {total}, نجح: {passed}, فشل: {failed}")
        
        # عرض رسالة النتيجة النهائية
        success_rate = (passed / total * 100) if total > 0 else 0
        if success_rate >= 80:
            result_msg = f"🎉 ممتاز! معدل النجاح: {success_rate:.1f}%"
            messagebox.showinfo("نتيجة الاختبار", result_msg)
        elif success_rate >= 60:
            result_msg = f"👍 جيد! معدل النجاح: {success_rate:.1f}%"
            messagebox.showinfo("نتيجة الاختبار", result_msg)
        else:
            result_msg = f"⚠️ يحتاج تحسين! معدل النجاح: {success_rate:.1f}%"
            messagebox.showwarning("نتيجة الاختبار", result_msg)
    
    def open_leave_system():
        """فتح نظام الإجازات"""
        try:
            leave_dept.show_leave_department()
            log_test_result("فتح النظام", "✅ نجح", "تم فتح نظام الإجازات")
        except Exception as e:
            log_test_result("فتح النظام", "❌ فشل", f"خطأ: {str(e)}")
    
    # أزرار الاختبارات
    buttons_frame = tk.Frame(tests_frame, bg="#f0f8ff")
    buttons_frame.pack(fill=tk.X, padx=10, pady=10)
    
    # معلومات النظام
    info_text = f"""
🔧 نظام الإجازات المحسن - الاختبار النهائي

📊 معلومات النظام:
• عدد الموظفين: {len(leave_dept.employees_data)}
• عدد الأرصدة: {len(leave_dept.balance_data)}
• عدد الإجازات: {len(leave_dept.leaves_data)}

🆕 الوظائف المضافة:
• التحقق من صحة التواريخ
• التحقق من كفاية الرصيد
• النسخ الاحتياطي التلقائي
• الإحصائيات المحسنة
• تحسين استجابة الواجهة
• رسائل خطأ محسنة

📋 اختبر الوظائف باستخدام الأزرار أدناه:
    """
    
    info_label = tk.Label(tests_frame, text=info_text, bg="#f0f8ff", 
                         fg="#2c3e50", font=("Arial", 10), justify=tk.LEFT)
    info_label.pack(padx=10, pady=10)
    
    # أزرار الاختبارات الفردية
    individual_tests = [
        ("📅 اختبار التواريخ", test_date_validation, "#3498db"),
        ("💰 اختبار الرصيد", test_balance_validation, "#2ecc71"),
        ("💾 اختبار النسخ الاحتياطي", test_backup_system, "#9b59b6"),
        ("📊 اختبار الإحصائيات", test_enhanced_statistics, "#e67e22"),
        ("🖥️ اختبار الواجهة", test_ui_responsiveness, "#34495e"),
        ("🔄 اختبار سير العمل", test_complete_workflow, "#16a085")
    ]
    
    for i, (text, command, color) in enumerate(individual_tests):
        row = i // 2
        col = i % 2
        btn = tk.Button(buttons_frame, text=text, command=command,
                       bg=color, fg="white", font=("Arial", 11, "bold"),
                       padx=15, pady=8, cursor="hand2")
        btn.grid(row=row, column=col, padx=5, pady=5, sticky="ew")
    
    buttons_frame.grid_columnconfigure(0, weight=1)
    buttons_frame.grid_columnconfigure(1, weight=1)
    
    # أزرار التحكم الرئيسية
    control_frame = tk.Frame(tests_frame, bg="#f0f8ff")
    control_frame.pack(fill=tk.X, padx=10, pady=20)
    
    tk.Button(control_frame, text="🚀 تشغيل جميع الاختبارات", 
             command=run_all_final_tests,
             bg="#2c3e50", fg="white", font=("Arial", 14, "bold"),
             padx=20, pady=12, cursor="hand2").pack(side=tk.LEFT, padx=5)
    
    tk.Button(control_frame, text="🏖️ فتح نظام الإجازات", 
             command=open_leave_system,
             bg="#27ae60", fg="white", font=("Arial", 14, "bold"),
             padx=20, pady=12, cursor="hand2").pack(side=tk.LEFT, padx=5)
    
    tk.Button(control_frame, text="❌ إغلاق", 
             command=root.destroy,
             bg="#e74c3c", fg="white", font=("Arial", 14, "bold"),
             padx=20, pady=12, cursor="hand2").pack(side=tk.RIGHT, padx=5)
    
    # رسالة ترحيب
    log_test_result("مرحباً", "🎯 جاهز", "النظام جاهز للاختبار النهائي")

def main():
    """الدالة الرئيسية"""
    run_final_test()

if __name__ == "__main__":
    main()
