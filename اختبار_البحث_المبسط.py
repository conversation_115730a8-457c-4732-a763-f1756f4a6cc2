#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار البحث المبسط - بدون فحص أو تنظيف
Simplified Search Test - No Validation or Cleaning
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_simplified_search():
    """اختبار البحث المبسط"""
    print("🔍 اختبار البحث المبسط - بدون فحص أو تنظيف")
    print("=" * 60)
    
    try:
        # استيراد النظام
        print("📦 استيراد نظام إدارة الموظفين...")
        import employee_management
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة الاختبار...")
        root = tk.Tk()
        root.title("اختبار البحث المبسط")
        root.geometry("1200x800")
        
        # إنشاء النظام
        print("🔧 إنشاء نظام إدارة الموظفين...")
        current_user = {"username": "simple_tester", "name": "مختبر البحث المبسط"}
        emp_system = employee_management.EmployeeManagementSystem(root, current_user)
        print("✅ تم إنشاء النظام بنجاح")
        
        # اختبار البحث المبسط
        print("\n🧪 اختبار البحث المبسط:")
        
        test_cases = [
            ("", "نص فارغ"),
            ("   ", "مسافات فقط"),
            ("أحمد", "نص عادي"),
            ("  أحمد  ", "نص مع مسافات"),
            ("أحمد   محمد", "نص مع مسافات متعددة"),
            ("Ahmed", "نص إنجليزي"),
            ("123", "أرقام"),
            ("موظف", "مسمى وظيفي")
        ]
        
        for i, (test_text, description) in enumerate(test_cases, 1):
            print(f"\n📝 اختبار {i}: {description}")
            print(f"   النص: '{test_text}'")
            
            try:
                # تعيين النص
                emp_system.search_var.set(test_text)
                
                # حفظ الدوال الأصلية
                original_showwarning = messagebox.showwarning
                original_showerror = messagebox.showerror
                
                messages = []
                
                def mock_showwarning(title, message):
                    messages.append(f"تحذير: {message}")
                    print(f"     📢 رسالة تحذير: {message}")
                
                def mock_showerror(title, message):
                    messages.append(f"خطأ: {message}")
                    print(f"     📢 رسالة خطأ: {message}")
                
                # استبدال الدوال مؤقتاً
                messagebox.showwarning = mock_showwarning
                messagebox.showerror = mock_showerror
                
                # تشغيل البحث المبسط
                print("     🔍 تشغيل البحث المبسط...")
                emp_system.force_search()
                
                # استعادة الدوال الأصلية
                messagebox.showwarning = original_showwarning
                messagebox.showerror = original_showerror
                
                # تحليل النتيجة
                if messages:
                    result = f"❌ ظهرت رسالة: {messages[0]}"
                else:
                    result = "✅ تم البحث بدون رسائل"
                
                print(f"     النتيجة: {result}")
                
            except Exception as e:
                print(f"     ❌ خطأ في الاختبار: {e}")
        
        # إنشاء واجهة تفاعلية للاختبار
        create_simple_test_interface(root, emp_system)
        
        print("\n🎉 انتهى الاختبار - الواجهة التفاعلية جاهزة")
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def create_simple_test_interface(root, emp_system):
    """إنشاء واجهة تفاعلية للاختبار المبسط"""
    
    # إطار الاختبار
    test_frame = tk.Frame(root, bg="#e8f5e8")
    test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
    
    # عنوان
    title_label = tk.Label(test_frame, text="🔍 اختبار البحث المبسط - بدون فحص أو تنظيف", 
                          bg="#e8f5e8", fg="#2d5a2d", font=("Arial", 14, "bold"))
    title_label.pack(pady=5)
    
    # تعليمات
    instructions_text = """
✅ البحث المبسط:
• لا يوجد فحص للنص
• لا يوجد تنظيف للنص
• البحث مباشر كما هو مكتوب
• رسالة تحذير فقط إذا لم يوجد نص نهائياً

الآن زر "🔍 بحث" يبحث مباشرة بدون أي معالجة للنص
    """
    instructions_label = tk.Label(test_frame, text=instructions_text, bg="#e8f5e8", 
                                 fg="#2d5a2d", font=("Arial", 10), justify=tk.LEFT)
    instructions_label.pack(pady=5)
    
    # متغير لعرض النتائج
    result_var = tk.StringVar()
    result_label = tk.Label(test_frame, textvariable=result_var, bg="#e8f5e8", 
                           fg="#2d5a2d", font=("Arial", 10, "bold"))
    result_label.pack(pady=5)
    
    # أزرار الاختبار
    buttons_frame = tk.Frame(test_frame, bg="#e8f5e8")
    buttons_frame.pack(pady=10)
    
    def test_with_text(text, desc):
        emp_system.search_var.set(text)
        result_var.set(f"تم تعيين النص: '{text}' - {desc}")
        print(f"🧪 تم تعيين نص الاختبار: {desc} - '{text}'")
    
    def test_simple_search():
        current_text = emp_system.search_var.get()
        print(f"\n🔍 اختبار البحث المبسط للنص: '{current_text}'")
        
        # حفظ الدوال الأصلية
        original_showwarning = messagebox.showwarning
        original_showerror = messagebox.showerror
        
        messages = []
        
        def mock_showwarning(title, message):
            messages.append(f"تحذير: {message}")
            print(f"📢 رسالة تحذير: {message}")
        
        def mock_showerror(title, message):
            messages.append(f"خطأ: {message}")
            print(f"📢 رسالة خطأ: {message}")
        
        messagebox.showwarning = mock_showwarning
        messagebox.showerror = mock_showerror
        
        try:
            emp_system.force_search()
            
            if messages:
                result_var.set(f"❌ ظهرت رسالة: {messages[0]}")
            else:
                result_var.set("✅ تم البحث بنجاح بدون رسائل")
                
        except Exception as e:
            result_var.set(f"❌ خطأ: {e}")
        finally:
            messagebox.showwarning = original_showwarning
            messagebox.showerror = original_showerror
    
    def show_current_text():
        current_text = emp_system.search_var.get()
        print(f"\n📝 النص الحالي في حقل البحث:")
        print(f"   النص: '{current_text}'")
        print(f"   الطول: {len(current_text)}")
        print(f"   رموز ASCII: {[ord(c) for c in current_text]}")
        result_var.set(f"النص الحالي: '{current_text}' (طول: {len(current_text)})")
    
    # أزرار اختبار النصوص
    tk.Label(buttons_frame, text="اختبار نصوص:", bg="#e8f5e8", 
            font=("Arial", 10, "bold")).pack(side=tk.LEFT)
    
    test_texts = [
        ("", "فارغ"),
        ("   ", "مسافات"),
        ("أحمد", "عادي"),
        ("  أحمد  ", "مسافات زائدة"),
        ("أحمد   محمد", "مسافات متعددة")
    ]
    
    for text, desc in test_texts:
        btn = tk.Button(buttons_frame, text=desc,
                       command=lambda t=text, d=desc: test_with_text(t, d),
                       bg="#28a745", fg="white", font=("Arial", 8))
        btn.pack(side=tk.LEFT, padx=2)
    
    # أزرار الوظائف
    functions_frame = tk.Frame(test_frame, bg="#e8f5e8")
    functions_frame.pack(pady=5)
    
    tk.Label(functions_frame, text="وظائف الاختبار:", bg="#e8f5e8", 
            font=("Arial", 10, "bold")).pack(side=tk.LEFT)
    
    tk.Button(functions_frame, text="🔍 البحث المبسط",
             command=test_simple_search,
             bg="#007bff", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(functions_frame, text="📝 عرض النص الحالي",
             command=show_current_text,
             bg="#6c757d", fg="white", font=("Arial", 9)).pack(side=tk.LEFT, padx=2)
    
    # معلومات إضافية
    info_frame = tk.Frame(test_frame, bg="#e8f5e8")
    info_frame.pack(pady=5)
    
    info_text = """
💡 ملاحظة: البحث الآن مبسط تماماً - يبحث مباشرة بدون أي فحص أو تنظيف للنص
🎯 النتيجة: البحث سيعمل مع أي نص تكتبه، حتى لو كان يحتوي على مسافات أو أحرف خاصة
    """
    info_label = tk.Label(info_frame, text=info_text, bg="#e8f5e8", 
                         fg="#2d5a2d", font=("Arial", 9), justify=tk.LEFT)
    info_label.pack()
    
    # زر إغلاق
    tk.Button(test_frame, text="❌ إغلاق الاختبار", 
             command=root.destroy,
             bg="#dc3545", fg="white", 
             font=("Arial", 12, "bold")).pack(pady=10)

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار البحث المبسط")
    print("=" * 60)
    
    test_simplified_search()

if __name__ == "__main__":
    main()
