@echo off
chcp 65001 > nul
title تثبيت المكتبات المطلوبة للنظام

echo.
echo ===============================================
echo    📦 تثبيت المكتبات المطلوبة
echo         نظام إدارة الموارد البشرية
echo ===============================================
echo.

echo 🔧 بدء تثبيت المكتبات الأساسية...
echo.

echo 📊 تثبيت openpyxl (للتعامل مع ملفات Excel)...
pip install openpyxl
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت openpyxl
) else (
    echo ✅ تم تثبيت openpyxl بنجاح
)
echo.

echo 📄 تثبيت python-docx (للتعامل مع ملفات Word)...
pip install python-docx
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت python-docx
) else (
    echo ✅ تم تثبيت python-docx بنجاح
)
echo.

echo 📅 تثبيت tkcalendar (لتحسين اختيار التواريخ)...
pip install tkcalendar
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت tkcalendar
) else (
    echo ✅ تم تثبيت tkcalendar بنجاح
)
echo.

echo 🖥️ تثبيت psutil (لمراقبة الأداء)...
pip install psutil
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت psutil
    echo ⚠️ سيعمل النظام بدون ميزات مراقبة الأداء المتقدمة
) else (
    echo ✅ تم تثبيت psutil بنجاح
)
echo.

echo 🔧 تحديث pip...
python -m pip install --upgrade pip
echo.

echo ===============================================
echo 📋 فحص المكتبات المثبتة:
echo ===============================================

python -c "
import sys
print('🐍 إصدار Python:', sys.version)
print()

libraries = [
    ('openpyxl', 'للتعامل مع ملفات Excel'),
    ('docx', 'للتعامل مع ملفات Word'),
    ('tkcalendar', 'لتحسين اختيار التواريخ'),
    ('psutil', 'لمراقبة الأداء'),
    ('tkinter', 'للواجهة الرسومية'),
    ('datetime', 'للتعامل مع التواريخ'),
    ('json', 'للتعامل مع ملفات JSON'),
    ('os', 'لعمليات النظام'),
    ('threading', 'للمعالجة المتوازية')
]

print('📦 حالة المكتبات:')
for lib, desc in libraries:
    try:
        __import__(lib)
        print(f'✅ {lib} - {desc}')
    except ImportError:
        print(f'❌ {lib} - {desc} (غير مثبت)')

print()
print('🎯 النتيجة:')
try:
    import openpyxl, docx, tkinter
    print('✅ المكتبات الأساسية مثبتة - النظام جاهز للعمل')
except ImportError as e:
    print(f'❌ مكتبات أساسية مفقودة: {e}')

try:
    import tkcalendar, psutil
    print('✅ المكتبات المحسنة مثبتة - جميع الميزات متاحة')
except ImportError:
    print('⚠️ بعض المكتبات المحسنة مفقودة - النظام سيعمل بميزات محدودة')
"

echo.
echo ===============================================
echo 🚀 تم الانتهاء من تثبيت المكتبات
echo ===============================================
echo.
echo 💡 يمكنك الآن تشغيل النظام باستخدام:
echo    تشغيل_النظام_المحسن.bat
echo.
echo اضغط أي مفتاح للمتابعة...
pause > nul
