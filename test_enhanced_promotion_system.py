#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام الترقيات المحسن
Test Enhanced Promotion System
"""

import sys
import datetime
import os

def test_promotion_system_integration():
    """اختبار تكامل نظام الترقيات مع الأنظمة المحسنة"""
    print("⬆️ اختبار نظام الترقيات المحسن")
    print("=" * 60)
    
    test_results = {
        'total_tests': 0,
        'passed_tests': 0,
        'failed_tests': 0
    }
    
    # اختبار 1: استيراد نظام الترقيات
    print("\n🧪 اختبار 1: استيراد نظام الترقيات")
    print("-" * 50)
    
    try:
        import promotion_system
        print("✅ تم استيراد نظام الترقيات بنجاح")
        test_results['passed_tests'] += 1
    except ImportError as e:
        print(f"❌ فشل في استيراد نظام الترقيات: {e}")
        test_results['failed_tests'] += 1
    test_results['total_tests'] += 1
    
    # اختبار 2: استيراد الأنظمة المحسنة
    print("\n🧪 اختبار 2: استيراد الأنظمة المحسنة")
    print("-" * 50)
    
    enhanced_systems = [
        ("data_validation", "نظام التحقق من البيانات"),
        ("error_handler", "نظام إدارة الأخطاء"),
        ("performance_optimizer", "نظام تحسين الأداء")
    ]
    
    for module_name, description in enhanced_systems:
        try:
            __import__(module_name)
            print(f"✅ {description}: متاح")
            test_results['passed_tests'] += 1
        except ImportError:
            print(f"⚠️ {description}: غير متاح")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 3: فئة نظام الترقيات المحسن
    print("\n🧪 اختبار 3: فئة نظام الترقيات المحسن")
    print("-" * 50)
    
    try:
        from promotion_system import PromotionSystem
        print("✅ تم استيراد فئة PromotionSystem")
        test_results['passed_tests'] += 1
        
        # اختبار الدوال الجديدة
        methods_to_test = [
            'create_enhanced_ui',
            'calculate_enhanced_promotions',
            'determine_promotion_status',
            'update_promotion_stats',
            'update_stats_display',
            'apply_filter',
            'refresh_data',
            'apply_promotion'
        ]
        
        for method_name in methods_to_test:
            if hasattr(PromotionSystem, method_name):
                print(f"✅ دالة {method_name}: موجودة")
                test_results['passed_tests'] += 1
            else:
                print(f"❌ دالة {method_name}: مفقودة")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
    
    except ImportError as e:
        print(f"❌ فشل في استيراد فئة PromotionSystem: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 4: حساب حالة الترقية
    print("\n🧪 اختبار 4: حساب حالة الترقية")
    print("-" * 50)
    
    try:
        from promotion_system import PromotionSystem
        import tkinter as tk
        
        # إنشاء نافذة وهمية للاختبار
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # إنشاء نظام الترقيات (بدون تشغيل الواجهة)
        promotion_sys = PromotionSystem.__new__(PromotionSystem)
        promotion_sys.validator = None
        promotion_sys.calculator = None
        
        # اختبار حالات مختلفة
        test_cases = [
            (5, 5.0, "موظف", "مستحق للترقية", "6"),
            (10, 4.5, "موظفة", "مستحق للترقية", "11"),
            (15, 4.0, "معلم", "وصل للحد الأقصى", "15"),
            (8, 3.5, "معلمة", "قريب من الاستحقاق", ""),
            (3, 2.0, "موظف", "غير مستحق", ""),
        ]
        
        for current_grade, years, job_title, expected_status, expected_new_grade in test_cases:
            status, new_grade = promotion_sys.determine_promotion_status(str(current_grade), years, job_title)
            
            if status == expected_status and new_grade == expected_new_grade:
                print(f"✅ الدرجة {current_grade} ({years} سنة): {status}")
                test_results['passed_tests'] += 1
            else:
                print(f"❌ الدرجة {current_grade}: متوقع {expected_status}, فعلي {status}")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
        
        root.destroy()
    
    except Exception as e:
        print(f"❌ خطأ في اختبار حساب الترقية: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 5: ملف بيانات الموظفين
    print("\n🧪 اختبار 5: ملف بيانات الموظفين")
    print("-" * 50)
    
    employees_file = "employees_data.xlsx"
    if os.path.exists(employees_file):
        print(f"✅ ملف البيانات موجود: {employees_file}")
        test_results['passed_tests'] += 1
        
        try:
            from openpyxl import load_workbook
            wb = load_workbook(employees_file)
            if "الموظفين" in wb.sheetnames:
                print("✅ ورقة الموظفين موجودة")
                test_results['passed_tests'] += 1
                
                ws = wb["الموظفين"]
                if ws.max_row > 1:
                    print(f"✅ يحتوي على {ws.max_row - 1} موظف")
                    test_results['passed_tests'] += 1
                else:
                    print("⚠️ لا يحتوي على بيانات موظفين")
                    test_results['failed_tests'] += 1
            else:
                print("❌ ورقة الموظفين غير موجودة")
                test_results['failed_tests'] += 1
            
            test_results['total_tests'] += 2
            
        except Exception as e:
            print(f"❌ خطأ في قراءة ملف البيانات: {e}")
            test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
    else:
        print(f"⚠️ ملف البيانات غير موجود: {employees_file}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 6: التكامل مع أنظمة التحسين
    print("\n🧪 اختبار 6: التكامل مع أنظمة التحسين")
    print("-" * 50)
    
    try:
        from data_validation import DataValidator, CalculationEngine
        from error_handler import error_handler
        
        # اختبار DataValidator
        validator = DataValidator()
        print("✅ DataValidator: يعمل")
        test_results['passed_tests'] += 1
        
        # اختبار CalculationEngine
        calculator = CalculationEngine()
        years = calculator.calculate_service_years("2020-01-01")
        if years > 0:
            print(f"✅ CalculationEngine: يعمل ({years:.1f} سنة)")
            test_results['passed_tests'] += 1
        else:
            print("❌ CalculationEngine: لا يعمل بشكل صحيح")
            test_results['failed_tests'] += 1
        
        # اختبار error_handler
        error_handler.log_activity("TEST_PROMOTION", "اختبار نظام الترقيات", success=True)
        print("✅ error_handler: يعمل")
        test_results['passed_tests'] += 1
        
        test_results['total_tests'] += 3
    
    except ImportError:
        print("⚠️ أنظمة التحسين غير متاحة")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    except Exception as e:
        print(f"❌ خطأ في اختبار أنظمة التحسين: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    return test_results

def main():
    """تشغيل جميع الاختبارات"""
    print("⬆️ اختبار نظام الترقيات المحسن")
    print(f"📅 التاريخ: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 إصدار Python: {sys.version}")
    print()
    
    # تشغيل الاختبارات
    results = test_promotion_system_integration()
    
    # عرض النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print(f"   📈 إجمالي الاختبارات: {results['total_tests']}")
    print(f"   ✅ نجح: {results['passed_tests']}")
    print(f"   ❌ فشل: {results['failed_tests']}")
    
    success_rate = (results['passed_tests'] / results['total_tests']) * 100 if results['total_tests'] > 0 else 0
    print(f"   📊 معدل النجاح: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("\n🎉 ممتاز! نظام الترقيات المحسن يعمل بشكل مثالي")
    elif success_rate >= 75:
        print("\n✅ جيد! نظام الترقيات يعمل بشكل جيد مع بعض التحسينات المطلوبة")
    elif success_rate >= 50:
        print("\n⚠️ متوسط! النظام يحتاج إلى تحسينات إضافية")
    else:
        print("\n❌ ضعيف! النظام يحتاج إلى مراجعة شاملة")
    
    print("=" * 60)
    print("\n🏁 انتهى اختبار نظام الترقيات المحسن")

if __name__ == "__main__":
    main()
