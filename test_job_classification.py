#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تصنيف الوظائف في نظام الرصيد
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_job_classification():
    """اختبار تصنيف الوظائف (موظف/معلم)"""
    
    print("🧪 اختبار تصنيف الوظائف في نظام الرصيد")
    print("=" * 70)
    
    try:
        # استيراد نظام الرصيد
        import leave_balance_system
        
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة للاختبار
        
        # إنشاء نظام الرصيد
        balance_system = leave_balance_system.LeaveBalanceSystem(root)
        
        print("✅ تم إنشاء نظام الرصيد بنجاح")
        
        # اختبار تصنيف المسميات الوظيفية
        test_cases = [
            # حالات الموظفين (يستحقون رصيد)
            ("موظف", 5, 150),
            ("موظفة", 3, 90),
            ("مدير", 4, 120),
            ("مديرة", 2, 60),
            ("محاسب", 6, 180),
            ("محاسبة", 1, 30),
            ("سكرتير", 3, 90),
            ("مشرف", 4, 120),
            ("أخصائي", 2, 60),
            
            # حالات المعلمين (لا يستحقون رصيد)
            ("معلم", 5, 0),
            ("معلمة", 3, 0),
            ("مدرس", 4, 0),
            ("مدرسة", 2, 0),
            ("أستاذ", 6, 0),
            ("أستاذة", 1, 0),
            ("محاضر", 3, 0),
            ("محاضرة", 4, 0),
            
            # حالات مختلطة
            ("مدير مدرسة", 5, 0),    # مدير (موظف) + مدرسة (معلم) = معلم (أولوية للمعلم)
            ("معلم مساعد", 3, 0),     # معلم (معلم) + مساعد (موظف) = معلم (أولوية للمعلم)
            ("", 4, 120),             # فارغ = موظف (افتراضي)
            ("غير محدد", 2, 60),      # غير واضح = موظف (افتراضي)
        ]
        
        print(f"\n🔍 اختبار {len(test_cases)} حالة تصنيف:")
        print("-" * 70)
        
        passed_tests = 0
        failed_tests = 0
        
        for job_title, service_years, expected_balance in test_cases:
            # حساب الرصيد التلقائي
            actual_balance = balance_system.calculate_automatic_balance(service_years, job_title)
            
            # فحص النتيجة
            if actual_balance == expected_balance:
                status = "✅ نجح"
                passed_tests += 1
            else:
                status = "❌ فشل"
                failed_tests += 1
            
            print(f"   {status} | '{job_title}' ({service_years} سنة) → متوقع: {expected_balance}, فعلي: {actual_balance}")
        
        print("-" * 70)
        print(f"📊 النتائج: {passed_tests} نجح، {failed_tests} فشل")
        
        # إغلاق النافذة
        root.destroy()
        
        return failed_tests == 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تصنيف الوظائف: {e}")
        return False

def test_balance_calculation_with_jobs():
    """اختبار حساب الرصيد مع بيانات حقيقية"""
    
    print("\n🧪 اختبار حساب الرصيد مع البيانات الحقيقية")
    print("=" * 70)
    
    try:
        import leave_balance_system
        
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.withdraw()
        
        # إنشاء نظام الرصيد
        balance_system = leave_balance_system.LeaveBalanceSystem(root)
        
        if balance_system.employees_data:
            print(f"📊 عدد الموظفين في النظام: {len(balance_system.employees_data)}")
            print("\n👥 تحليل الموظفين:")
            print("-" * 70)
            
            employees_count = 0
            teachers_count = 0
            total_balance = 0
            
            for emp in balance_system.employees_data:
                emp_id = emp.get("الرقم الوظيفي", "")
                emp_name = emp.get("الاسم العربي", "")
                job_title = emp.get("المسمى الوظيفي", "") or emp.get("الوظيفة", "") or emp.get("المنصب", "")
                start_date = emp.get("تاريخ أول مباشرة", "")
                
                # حساب البيانات
                service_years = balance_system.calculate_service_years(start_date)
                auto_balance = balance_system.calculate_automatic_balance(service_years, job_title)
                manual_balance = balance_system.get_manual_balance(emp_id)
                used_leaves = balance_system.calculate_used_leaves(emp_id)
                total_emp_balance = auto_balance + manual_balance
                remaining_balance = total_emp_balance - used_leaves
                
                # تصنيف الموظف
                if auto_balance > 0:
                    classification = "موظف"
                    employees_count += 1
                    total_balance += total_emp_balance
                else:
                    classification = "معلم"
                    teachers_count += 1
                
                print(f"   {emp_id} | {emp_name[:20]:20} | {job_title[:15]:15} | {classification:6} | {service_years:2}س | {auto_balance:3}ي | {total_emp_balance:3}ي")
            
            print("-" * 70)
            print(f"📈 الإحصائيات:")
            print(f"   👥 الموظفون: {employees_count} (يستحقون رصيد)")
            print(f"   👨‍🏫 المعلمون: {teachers_count} (لا يستحقون رصيد)")
            print(f"   💰 إجمالي الرصيد: {total_balance} يوم")
            print(f"   📊 متوسط الرصيد للموظف: {total_balance/employees_count if employees_count > 0 else 0:.1f} يوم")
            
        else:
            print("❌ لا توجد بيانات موظفين للاختبار")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حساب الرصيد: {e}")
        return False

def test_manual_balance_addition():
    """اختبار إضافة رصيد يدوي للموظفين فقط"""
    
    print("\n🧪 اختبار إضافة رصيد يدوي للموظفين فقط")
    print("=" * 70)
    
    try:
        import leave_balance_system
        
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.withdraw()
        
        # إنشاء نظام الرصيد
        balance_system = leave_balance_system.LeaveBalanceSystem(root)
        
        if balance_system.employees_data:
            # البحث عن موظف وعن معلم للاختبار
            test_employee = None
            test_teacher = None
            
            for emp in balance_system.employees_data:
                job_title = emp.get("المسمى الوظيفي", "") or emp.get("الوظيفة", "") or emp.get("المنصب", "")
                service_years = balance_system.calculate_service_years(emp.get("تاريخ أول مباشرة", ""))
                auto_balance = balance_system.calculate_automatic_balance(service_years, job_title)
                
                if auto_balance > 0 and not test_employee:
                    test_employee = emp
                elif auto_balance == 0 and not test_teacher:
                    test_teacher = emp
                
                if test_employee and test_teacher:
                    break
            
            # اختبار إضافة رصيد للموظف
            if test_employee:
                emp_id = test_employee.get("الرقم الوظيفي", "")
                emp_name = test_employee.get("الاسم العربي", "")
                job_title = test_employee.get("المسمى الوظيفي", "") or test_employee.get("الوظيفة", "")
                
                print(f"✅ اختبار إضافة رصيد للموظف:")
                print(f"   الرقم: {emp_id}, الاسم: {emp_name}, المسمى: {job_title}")
                
                # إضافة رصيد يدوي
                balance_system.update_manual_balance(emp_id, 20, "اختبار إضافة رصيد للموظف")
                
                # التحقق من الإضافة
                manual_balance = balance_system.get_manual_balance(emp_id)
                print(f"   الرصيد اليدوي بعد الإضافة: {manual_balance} يوم")
                
                if manual_balance == 20:
                    print("   ✅ تم إضافة الرصيد بنجاح للموظف")
                else:
                    print("   ❌ فشل في إضافة الرصيد للموظف")
            
            # اختبار إضافة رصيد للمعلم (يجب أن يعمل ولكن الرصيد التلقائي يبقى 0)
            if test_teacher:
                emp_id = test_teacher.get("الرقم الوظيفي", "")
                emp_name = test_teacher.get("الاسم العربي", "")
                job_title = test_teacher.get("المسمى الوظيفي", "") or test_teacher.get("الوظيفة", "")
                
                print(f"\n⚠️ اختبار إضافة رصيد للمعلم:")
                print(f"   الرقم: {emp_id}, الاسم: {emp_name}, المسمى: {job_title}")
                
                # إضافة رصيد يدوي
                balance_system.update_manual_balance(emp_id, 15, "اختبار إضافة رصيد للمعلم")
                
                # التحقق من الإضافة
                manual_balance = balance_system.get_manual_balance(emp_id)
                service_years = balance_system.calculate_service_years(test_teacher.get("تاريخ أول مباشرة", ""))
                auto_balance = balance_system.calculate_automatic_balance(service_years, job_title)
                total_balance = auto_balance + manual_balance
                
                print(f"   الرصيد التلقائي: {auto_balance} يوم (يجب أن يكون 0)")
                print(f"   الرصيد اليدوي: {manual_balance} يوم")
                print(f"   إجمالي الرصيد: {total_balance} يوم")
                
                if auto_balance == 0 and manual_balance == 15:
                    print("   ✅ النظام يعمل بشكل صحيح: المعلم لا يحصل على رصيد تلقائي ولكن يمكن إضافة رصيد يدوي")
                else:
                    print("   ❌ خطأ في النظام")
            
        else:
            print("❌ لا توجد بيانات موظفين للاختبار")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إضافة الرصيد اليدوي: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار تصنيف الوظائف في نظام الرصيد")
    print("=" * 80)
    
    # تشغيل الاختبارات
    test1 = test_job_classification()
    test2 = test_balance_calculation_with_jobs()
    test3 = test_manual_balance_addition()
    
    print("\n" + "=" * 80)
    print("📊 ملخص نتائج الاختبار:")
    print(f"   اختبار تصنيف الوظائف: {'✅ نجح' if test1 else '❌ فشل'}")
    print(f"   اختبار حساب الرصيد: {'✅ نجح' if test2 else '❌ فشل'}")
    print(f"   اختبار الرصيد اليدوي: {'✅ نجح' if test3 else '❌ فشل'}")
    
    if all([test1, test2, test3]):
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ النظام يميز بين الموظفين والمعلمين بشكل صحيح")
        print("✅ الموظفون يحصلون على رصيد تلقائي")
        print("✅ المعلمون لا يحصلون على رصيد تلقائي")
        print("✅ يمكن إضافة رصيد يدوي للجميع")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    print("=" * 80)
