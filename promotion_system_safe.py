import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import os

try:
    from openpyxl import load_workbook, Workbook
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    print("⚠️ مكتبة openpyxl غير مثبتة")

class PromotionSystem:
    def __init__(self, root, employees_data_file="employees_data.xlsx"):
        self.root = root
        self.root.title("⬆️ نظام الترقيات")
        self.root.geometry("1200x700")
        self.root.configure(bg="#f0f0f0")
        
        # ملف بيانات الموظفين
        self.employees_data_file = employees_data_file
        self.sheet_name = "الموظفين"
        
        # قائمة الترقيات
        self.promotion_list = []
        self.promotion_stats = {
            'total_employees': 0,
            'eligible_for_promotion': 0,
            'not_eligible': 0,
            'near_eligibility': 0,
            'average_years_in_grade': 0
        }
        
        # تحميل بيانات الموظفين
        self.employees_data = self.load_employees_data()
        
        # إنشاء واجهة المستخدم
        self.create_ui()
        
        # حساب الترقيات المستحقة
        self.calculate_promotions()
    
    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg="#f0f0f0")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # عنوان النظام
        title_frame = tk.Frame(main_frame, bg="#2c3e50", relief=tk.RAISED, bd=2)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_label = tk.Label(title_frame, text="⬆️ نظام الترقيات", 
                              font=("Arial", 16, "bold"), fg="white", bg="#2c3e50")
        title_label.pack(pady=10)
        
        # إطار الإحصائيات
        stats_frame = tk.LabelFrame(main_frame, text="📊 إحصائيات سريعة", 
                                   font=("Arial", 12, "bold"), bg="#f0f0f0")
        stats_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.stats_inner_frame = tk.Frame(stats_frame, bg="#f0f0f0")
        self.stats_inner_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # إطار التحكم
        control_frame = tk.LabelFrame(main_frame, text="🔧 أدوات التحكم", 
                                     font=("Arial", 12, "bold"), bg="#f0f0f0")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # أزرار التحكم
        buttons_frame = tk.Frame(control_frame, bg="#f0f0f0")
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.create_control_buttons(buttons_frame)
        
        # إطار الجدول
        table_frame = tk.LabelFrame(main_frame, text="📋 قائمة الترقيات", 
                                   font=("Arial", 12, "bold"), bg="#f0f0f0")
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        self.create_table(table_frame)
    
    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons = [
            ("🔄 تحديث البيانات", self.refresh_data, "#3498db"),
            ("📊 عرض التفاصيل", self.show_details, "#e67e22"),
            ("💾 تصدير Excel", self.export_to_excel, "#16a085"),
            ("❌ إغلاق", self.close_window, "#e74c3c")
        ]
        
        for i, (text, command, color) in enumerate(buttons):
            btn = tk.Button(parent, text=text, command=command,
                           font=("Arial", 10, "bold"), bg=color, fg="white",
                           relief=tk.RAISED, bd=2, padx=15, pady=5)
            btn.grid(row=0, column=i, padx=5, pady=5, sticky="ew")
        
        # تكوين الأعمدة للتوزيع المتساوي
        for i in range(len(buttons)):
            parent.grid_columnconfigure(i, weight=1)
    
    def create_table(self, parent):
        """إنشاء جدول الترقيات"""
        # إطار الجدول مع شريط التمرير
        table_container = tk.Frame(parent, bg="#f0f0f0")
        table_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # إنشاء Treeview
        columns = ("الرقم الوظيفي", "الاسم العربي", "الدرجة الحالية", 
                  "تاريخ الدرجة", "سنوات في الدرجة", "الحالة")
        
        self.tree = ttk.Treeview(table_container, columns=columns, show="headings", height=15)
        
        # تكوين العناوين
        headers = {
            "الرقم الوظيفي": "الرقم الوظيفي",
            "الاسم العربي": "الاسم العربي", 
            "الدرجة الحالية": "الدرجة الحالية",
            "تاريخ الدرجة": "تاريخ الدرجة الحالية",
            "سنوات في الدرجة": "سنوات في الدرجة",
            "الحالة": "حالة الترقية"
        }
        
        # تكوين عرض الأعمدة
        column_widths = {
            "الرقم الوظيفي": 120,
            "الاسم العربي": 200,
            "الدرجة الحالية": 100,
            "تاريخ الدرجة": 120,
            "سنوات في الدرجة": 150,
            "الحالة": 150
        }
        
        for col in columns:
            self.tree.heading(col, text=headers[col], anchor="center")
            self.tree.column(col, width=column_widths[col], anchor="center")
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_container, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=v_scrollbar.set)
        
        # تخطيط الجدول وشريط التمرير
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        
        # تكوين التمدد
        table_container.grid_rowconfigure(0, weight=1)
        table_container.grid_columnconfigure(0, weight=1)
        
        # ربط الأحداث
        self.tree.bind("<Double-1>", self.on_item_double_click)
    
    def load_employees_data(self):
        """تحميل بيانات الموظفين من ملف Excel"""
        try:
            if not OPENPYXL_AVAILABLE or not os.path.exists(self.employees_data_file):
                return []
            
            wb = load_workbook(self.employees_data_file)
            if self.sheet_name not in wb.sheetnames:
                return []
            
            ws = wb[self.sheet_name]
            employees_data = []
            
            # قراءة العناوين من الصف الأول
            headers = []
            for cell in ws[1]:
                if cell.value:
                    headers.append(cell.value)
                else:
                    break
            
            # قراءة البيانات
            for row in ws.iter_rows(min_row=2, values_only=True):
                if row[0]:  # إذا كان هناك رقم وظيفي
                    employee = {}
                    for i, value in enumerate(row):
                        if i < len(headers):
                            employee[headers[i]] = value
                    employees_data.append(employee)
            
            return employees_data
        
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            return []
    
    def calculate_promotions(self):
        """حساب الترقيات المستحقة"""
        self.promotion_list = []
        self.promotion_stats = {
            'total_employees': 0,
            'eligible_for_promotion': 0,
            'not_eligible': 0,
            'near_eligibility': 0,
            'average_years_in_grade': 0
        }
        
        if not self.employees_data:
            self.update_stats_display()
            return
        
        total_years = 0
        current_date = datetime.now()
        
        for emp in self.employees_data:
            try:
                # التحقق من البيانات الأساسية
                emp_id = emp.get("الرقم الوظيفي", "")
                name = emp.get("الاسم العربي", "")
                current_grade = emp.get("الدرجة الحالية", "")
                grade_date_str = emp.get("تاريخ الدرجة الحالية", "")
                
                if not all([emp_id, name, current_grade, grade_date_str]):
                    continue
                
                # تحويل تاريخ الدرجة الحالية
                grade_date = None
                for date_format in ["%Y-%m-%d", "%d/%m/%Y", "%d-%m-%Y"]:
                    try:
                        grade_date = datetime.strptime(str(grade_date_str), date_format)
                        break
                    except ValueError:
                        continue
                
                if not grade_date:
                    continue
                
                # حساب سنوات الخدمة في الدرجة الحالية
                duration = current_date - grade_date
                years_in_grade = duration.days / 365.25
                total_years += years_in_grade
                
                # تحديد حالة الترقية
                status = self.determine_promotion_status(current_grade, years_in_grade)
                
                # إضافة إلى الإحصائيات
                self.update_promotion_stats(status)
                
                # إضافة إلى قائمة الترقيات
                promotion_data = {
                    "الرقم الوظيفي": emp_id,
                    "الاسم العربي": name,
                    "الدرجة الحالية": current_grade,
                    "تاريخ الدرجة الحالية": grade_date_str,
                    "سنوات في الدرجة": round(years_in_grade, 1),
                    "حالة الترقية": status
                }
                
                self.promotion_list.append(promotion_data)
                
            except Exception as e:
                print(f"خطأ في حساب ترقية الموظف {emp.get('الاسم العربي', 'غير محدد')}: {e}")
                continue
        
        # حساب المتوسطات
        if self.promotion_list:
            self.promotion_stats['average_years_in_grade'] = round(total_years / len(self.promotion_list), 1)
        
        # تحديث الواجهة
        self.update_table()
        self.update_stats_display()
    
    def determine_promotion_status(self, current_grade, years_in_grade):
        """تحديد حالة الترقية"""
        try:
            current_grade_num = int(current_grade)
        except (ValueError, TypeError):
            return "بيانات غير صحيحة"
        
        # قواعد الترقية
        if years_in_grade >= 4:
            if current_grade_num >= 15:
                return "وصل للحد الأقصى"
            else:
                return "مستحق للترقية"
        elif years_in_grade >= 3:
            return "قريب من الاستحقاق"
        else:
            return "غير مستحق"
    
    def update_promotion_stats(self, status):
        """تحديث إحصائيات الترقيات"""
        self.promotion_stats['total_employees'] += 1
        
        if status == "مستحق للترقية":
            self.promotion_stats['eligible_for_promotion'] += 1
        elif status == "قريب من الاستحقاق":
            self.promotion_stats['near_eligibility'] += 1
        else:
            self.promotion_stats['not_eligible'] += 1
    
    def update_stats_display(self):
        """تحديث عرض الإحصائيات"""
        # مسح الإحصائيات السابقة
        for widget in self.stats_inner_frame.winfo_children():
            widget.destroy()
        
        stats = self.promotion_stats
        
        # إنشاء بطاقات الإحصائيات
        stat_cards = [
            ("👥 إجمالي الموظفين", stats['total_employees'], "#3498db"),
            ("⬆️ مستحق للترقية", stats['eligible_for_promotion'], "#27ae60"),
            ("⏳ قريب من الاستحقاق", stats['near_eligibility'], "#f39c12"),
            ("❌ غير مستحق", stats['not_eligible'], "#e74c3c")
        ]
        
        for i, (title, value, color) in enumerate(stat_cards):
            card_frame = tk.Frame(self.stats_inner_frame, bg=color, relief=tk.RAISED, bd=2)
            card_frame.grid(row=0, column=i, padx=5, pady=5, sticky="ew")
            
            title_label = tk.Label(card_frame, text=title, font=("Arial", 9, "bold"), 
                                  fg="white", bg=color)
            title_label.pack(pady=(5, 0))
            
            value_label = tk.Label(card_frame, text=str(value), font=("Arial", 12, "bold"), 
                                  fg="white", bg=color)
            value_label.pack(pady=(0, 5))
        
        # تكوين التوزيع المتساوي
        for i in range(len(stat_cards)):
            self.stats_inner_frame.grid_columnconfigure(i, weight=1)
    
    def update_table(self):
        """تحديث جدول الترقيات"""
        # مسح البيانات السابقة
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # إضافة البيانات الجديدة
        for promotion in self.promotion_list:
            # تحديد لون الصف حسب الحالة
            tags = []
            if promotion["حالة الترقية"] == "مستحق للترقية":
                tags = ["eligible"]
            elif promotion["حالة الترقية"] == "قريب من الاستحقاق":
                tags = ["near"]
            elif promotion["حالة الترقية"] == "وصل للحد الأقصى":
                tags = ["max"]
            
            values = (
                promotion["الرقم الوظيفي"],
                promotion["الاسم العربي"],
                promotion["الدرجة الحالية"],
                promotion["تاريخ الدرجة الحالية"],
                f"{promotion['سنوات في الدرجة']} سنة",
                promotion["حالة الترقية"]
            )
            
            self.tree.insert("", "end", values=values, tags=tags)
        
        # تكوين ألوان الصفوف
        self.tree.tag_configure("eligible", background="#d5f4e6")
        self.tree.tag_configure("near", background="#fff3cd")
        self.tree.tag_configure("max", background="#f8d7da")
    
    def refresh_data(self):
        """تحديث البيانات"""
        try:
            # إعادة تحميل البيانات
            self.employees_data = self.load_employees_data()
            
            # إعادة حساب الترقيات
            self.calculate_promotions()
            
            messagebox.showinfo("تحديث البيانات", "تم تحديث البيانات بنجاح")
        
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث البيانات: {e}")
    
    def show_details(self):
        """عرض تفاصيل الترقيات"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار موظف من القائمة")
            return
        
        # الحصول على بيانات الموظف المحدد
        item_values = self.tree.item(selected_item[0])['values']
        
        details_window = tk.Toplevel(self.root)
        details_window.title("👁️ تفاصيل الموظف")
        details_window.geometry("400x300")
        details_window.configure(bg="#f0f0f0")
        
        # إطار التفاصيل
        details_frame = tk.Frame(details_window, bg="#f0f0f0")
        details_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = tk.Label(details_frame, text=f"👁️ تفاصيل الموظف: {item_values[1]}", 
                              font=("Arial", 14, "bold"), bg="#f0f0f0")
        title_label.pack(pady=(0, 20))
        
        # التفاصيل
        details = [
            ("الرقم الوظيفي", item_values[0]),
            ("الاسم العربي", item_values[1]),
            ("الدرجة الحالية", item_values[2]),
            ("تاريخ الدرجة الحالية", item_values[3]),
            ("سنوات في الدرجة", item_values[4]),
            ("حالة الترقية", item_values[5])
        ]
        
        for label, value in details:
            row_frame = tk.Frame(details_frame, bg="#f0f0f0")
            row_frame.pack(fill=tk.X, pady=5)
            
            tk.Label(row_frame, text=f"{label}:", font=("Arial", 10, "bold"), 
                    bg="#f0f0f0", width=20, anchor="w").pack(side=tk.LEFT)
            tk.Label(row_frame, text=str(value), font=("Arial", 10), 
                    bg="#f0f0f0", anchor="w").pack(side=tk.LEFT, padx=(10, 0))
        
        # زر الإغلاق
        close_btn = tk.Button(details_frame, text="❌ إغلاق", 
                            command=details_window.destroy,
                            font=("Arial", 10, "bold"), bg="#e74c3c", fg="white")
        close_btn.pack(pady=(20, 0))
    
    def export_to_excel(self):
        """تصدير البيانات إلى Excel"""
        try:
            if not OPENPYXL_AVAILABLE:
                messagebox.showerror("خطأ", "مكتبة openpyxl غير متاحة")
                return
            
            from tkinter import filedialog
            
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="تصدير بيانات الترقيات"
            )
            
            if not filename:
                return
            
            # إنشاء ملف Excel
            wb = Workbook()
            ws = wb.active
            ws.title = "الترقيات"
            
            # العناوين
            headers = ["الرقم الوظيفي", "الاسم العربي", "الدرجة الحالية", 
                      "تاريخ الدرجة الحالية", "سنوات في الدرجة", "حالة الترقية"]
            
            # كتابة العناوين
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)
            
            # كتابة البيانات
            for row, promotion in enumerate(self.promotion_list, 2):
                ws.cell(row=row, column=1, value=promotion["الرقم الوظيفي"])
                ws.cell(row=row, column=2, value=promotion["الاسم العربي"])
                ws.cell(row=row, column=3, value=promotion["الدرجة الحالية"])
                ws.cell(row=row, column=4, value=promotion["تاريخ الدرجة الحالية"])
                ws.cell(row=row, column=5, value=f"{promotion['سنوات في الدرجة']} سنة")
                ws.cell(row=row, column=6, value=promotion["حالة الترقية"])
            
            # حفظ الملف
            wb.save(filename)
            messagebox.showinfo("نجح", f"تم تصدير البيانات إلى:\n{filename}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير البيانات: {e}")
    
    def on_item_double_click(self, event):
        """معالجة النقر المزدوج على عنصر"""
        self.show_details()
    
    def close_window(self):
        """إغلاق النافذة"""
        self.root.destroy()

# للاختبار المباشر
if __name__ == "__main__":
    root = tk.Tk()
    app = PromotionSystem(root)
    root.mainloop()
