#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تشخيص نص البحث
Test Search Text Debug
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_search_text_debug():
    """اختبار تشخيص نص البحث"""
    print("🔍 اختبار تشخيص نص البحث")
    print("=" * 60)
    
    try:
        # استيراد النظام
        print("📦 استيراد نظام إدارة الموظفين...")
        import employee_management
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة اختبار...")
        root = tk.Tk()
        root.title("اختبار تشخيص نص البحث")
        root.geometry("1200x800")
        
        # إنشاء النظام
        print("🔧 إنشاء نظام إدارة الموظفين...")
        current_user = {"username": "test", "name": "مستخدم اختبار"}
        emp_system = employee_management.EmployeeManagementSystem(root, current_user)
        print("✅ تم إنشاء النظام بنجاح")
        
        # اختبارات مختلفة لنص البحث
        test_cases = [
            ("", "نص فارغ"),
            ("   ", "مسافات فقط"),
            ("أحمد", "نص عادي"),
            ("  أحمد  ", "نص مع مسافات"),
            ("أحمد   محمد", "نص مع مسافات متعددة"),
            ("\tأحمد\t", "نص مع تابات"),
            ("أحمد\nمحمد", "نص مع سطر جديد")
        ]
        
        print("\n🧪 اختبار حالات مختلفة:")
        for i, (test_text, description) in enumerate(test_cases, 1):
            print(f"\n📝 اختبار {i}: {description}")
            print(f"   النص: '{test_text}' (طول: {len(test_text)})")
            
            # تعيين النص
            emp_system.search_var.set(test_text)
            
            # اختبار دالة فحص النص
            try:
                print("   🔍 فحص النص...")
                # محاكاة فحص النص بدون عرض الرسالة
                raw_text = emp_system.search_var.get()
                cleaned_text = raw_text.strip()
                
                if len(raw_text) == 0:
                    status = "فارغ"
                elif len(cleaned_text) == 0:
                    status = "مسافات فقط"
                else:
                    status = "صالح"
                
                print(f"   ✅ النتيجة: {status}")
                
            except Exception as e:
                print(f"   ❌ خطأ في فحص النص: {e}")
            
            # اختبار دالة تنظيف النص
            try:
                print("   🧹 تنظيف النص...")
                original_text = emp_system.search_var.get()
                cleaned_text = original_text.strip()
                cleaned_text = ' '.join(cleaned_text.split())
                
                if original_text != cleaned_text:
                    print(f"   ✅ تم التنظيف: '{original_text}' → '{cleaned_text}'")
                else:
                    print(f"   ℹ️ النص نظيف بالفعل: '{original_text}'")
                    
            except Exception as e:
                print(f"   ❌ خطأ في تنظيف النص: {e}")
        
        print("\n" + "=" * 60)
        print("🎉 انتهى الاختبار - النظام جاهز للاستخدام!")
        
        # إضافة تعليمات للمستخدم
        instructions_frame = tk.Frame(root, bg="#f0f0f0")
        instructions_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
        
        instructions_text = """
📝 تعليمات الاختبار:
1. اكتب نص في حقل البحث (جرب مسافات زائدة أو أحرف خفية)
2. اضغط زر "🔍 فحص النص" لرؤية تحليل مفصل للنص
3. اضغط زر "🧹 تنظيف النص" لإزالة المسافات الزائدة والأحرف الخفية
4. اضغط زر "🔍 بحث" للبحث الفعلي
5. اضغط زر "🔧 تشخيص" لفحص النظام بالكامل
        """
        
        instructions_label = tk.Label(instructions_frame, text=instructions_text,
                                    bg="#f0f0f0", fg="#2c3e50",
                                    font=("Arial", 10), justify=tk.LEFT)
        instructions_label.pack(anchor=tk.W)
        
        # إضافة أزرار اختبار سريع
        test_frame = tk.Frame(root, bg="#ecf0f1")
        test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=5)
        
        tk.Label(test_frame, text="اختبارات سريعة:", 
                bg="#ecf0f1", fg="#2c3e50", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        
        def set_test_text(text, desc):
            emp_system.search_var.set(text)
            print(f"🧪 تم تعيين نص الاختبار: {desc} - '{text}'")
        
        test_buttons = [
            ("", "نص فارغ"),
            ("   ", "مسافات فقط"),
            ("  أحمد  ", "نص مع مسافات"),
            ("أحمد   محمد", "مسافات متعددة")
        ]
        
        for text, desc in test_buttons:
            btn = tk.Button(test_frame, text=desc,
                           command=lambda t=text, d=desc: set_test_text(t, d),
                           bg="#3498db", fg="white", font=("Arial", 8))
            btn.pack(side=tk.LEFT, padx=2)
        
        # إضافة زر إغلاق سريع
        close_btn = tk.Button(root, text="❌ إغلاق الاختبار", 
                             command=root.destroy,
                             bg="#e74c3c", fg="white", 
                             font=("Arial", 12, "bold"))
        close_btn.pack(side=tk.BOTTOM, pady=10)
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def test_text_cleaning_logic():
    """اختبار منطق تنظيف النص"""
    print("\n🧹 اختبار منطق تنظيف النص")
    print("-" * 40)
    
    test_cases = [
        ("", "نص فارغ"),
        ("   ", "مسافات فقط"),
        ("أحمد", "نص عادي"),
        ("  أحمد  ", "مسافات في البداية والنهاية"),
        ("أحمد   محمد", "مسافات متعددة في الوسط"),
        ("\tأحمد\t", "تابات"),
        ("أحمد\nمحمد", "سطر جديد"),
        ("  أحمد   محمد   علي  ", "مسافات متعددة ومختلطة")
    ]
    
    for text, description in test_cases:
        print(f"\n📝 {description}:")
        print(f"   الأصلي: '{text}' (طول: {len(text)})")
        
        # تنظيف النص
        cleaned = text.strip()
        cleaned = ' '.join(cleaned.split())
        
        print(f"   المنظف: '{cleaned}' (طول: {len(cleaned)})")
        
        if text != cleaned:
            print(f"   ✅ تم التنظيف")
        else:
            print(f"   ℹ️ لا يحتاج تنظيف")

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار تشخيص وتنظيف نص البحث")
    print("=" * 60)
    
    # اختبار منطق التنظيف أولاً
    test_text_cleaning_logic()
    
    print("\n" + "=" * 60)
    
    # اختبار النظام الكامل
    response = input("هل تريد اختبار النظام الكامل؟ (y/n): ")
    if response.lower() in ['y', 'yes', 'نعم', 'ن']:
        test_search_text_debug()
    else:
        print("🏁 انتهى الاختبار")

if __name__ == "__main__":
    main()
