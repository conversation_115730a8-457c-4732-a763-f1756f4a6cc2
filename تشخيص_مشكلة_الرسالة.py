#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة رسالة "الرجاء إدخال النص أولاً"
Diagnose "Please Enter Text First" Message Issue
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def diagnose_search_message_issue():
    """تشخيص مشكلة رسالة البحث"""
    print("🔍 تشخيص مشكلة رسالة 'الرجاء إدخال النص أولاً'")
    print("=" * 70)
    
    try:
        # استيراد النظام
        print("📦 استيراد نظام إدارة الموظفين...")
        import employee_management
        print("✅ تم الاستيراد بنجاح")
        
        # إنشاء نافذة اختبار
        print("🪟 إنشاء نافذة التشخيص...")
        root = tk.Tk()
        root.title("تشخيص مشكلة رسالة البحث")
        root.geometry("1400x900")
        
        # إنشاء النظام
        print("🔧 إنشاء نظام إدارة الموظفين...")
        current_user = {"username": "diagnostician", "name": "مشخص النظام"}
        emp_system = employee_management.EmployeeManagementSystem(root, current_user)
        print("✅ تم إنشاء النظام بنجاح")
        
        # إنشاء واجهة التشخيص
        create_diagnostic_interface(root, emp_system)
        
        print("\n🎉 واجهة التشخيص جاهزة للاستخدام")
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {e}")
        import traceback
        traceback.print_exc()

def create_diagnostic_interface(root, emp_system):
    """إنشاء واجهة التشخيص"""
    
    # إطار التشخيص
    diag_frame = tk.Frame(root, bg="#fff3cd")
    diag_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
    
    # عنوان التشخيص
    title_label = tk.Label(diag_frame, text="🔍 تشخيص مشكلة رسالة البحث", 
                          bg="#fff3cd", fg="#856404", font=("Arial", 14, "bold"))
    title_label.pack(pady=5)
    
    # معلومات المشكلة
    info_text = """
المشكلة: رسالة "الرجاء إدخال النص أولاً" تظهر حتى عندما يكون هناك نص في حقل البحث
الهدف: تشخيص السبب الجذري وإصلاحه
    """
    info_label = tk.Label(diag_frame, text=info_text, bg="#fff3cd", fg="#856404", 
                         font=("Arial", 10), justify=tk.LEFT)
    info_label.pack(pady=5)
    
    # إطار الاختبارات
    test_frame = tk.Frame(diag_frame, bg="#fff3cd")
    test_frame.pack(pady=10)
    
    # متغير لعرض النتائج
    result_var = tk.StringVar()
    result_label = tk.Label(diag_frame, textvariable=result_var, bg="#fff3cd", 
                           fg="#856404", font=("Arial", 10, "bold"))
    result_label.pack(pady=5)
    
    def test_search_with_text(test_text, description):
        """اختبار البحث مع نص محدد"""
        print(f"\n🧪 اختبار: {description}")
        print(f"   النص المدخل: '{test_text}'")
        
        try:
            # تعيين النص في حقل البحث
            emp_system.search_var.set(test_text)
            
            # محاكاة منطق دالة البحث
            raw_text = emp_system.search_var.get()
            print(f"   النص الخام: '{raw_text}' (طول: {len(raw_text)})")
            
            # تنظيف النص (نفس المنطق في الكود)
            search_term = raw_text.strip()
            search_term = ' '.join(search_term.split())
            print(f"   النص بعد التنظيف: '{search_term}' (طول: {len(search_term)})")
            
            # فحص الشروط
            if len(search_term) == 0:
                result = "❌ سيظهر تنبيه: النص فارغ"
                print(f"   النتيجة: {result}")
            elif len(search_term.replace(" ", "")) == 0:
                result = "❌ سيظهر تنبيه: النص يحتوي على مسافات فقط"
                print(f"   النتيجة: {result}")
            else:
                result = f"✅ سيبحث عن: '{search_term}'"
                print(f"   النتيجة: {result}")
            
            # اختبار فعلي لزر البحث
            print("   🔍 اختبار زر البحث الفعلي:")
            try:
                # حفظ الدالة الأصلية لـ messagebox
                original_showwarning = messagebox.showwarning
                original_showerror = messagebox.showerror
                
                # متغير لتتبع الرسائل
                message_shown = []
                
                def mock_showwarning(title, message):
                    message_shown.append(f"تحذير: {message}")
                    print(f"     📢 رسالة تحذير: {message}")
                
                def mock_showerror(title, message):
                    message_shown.append(f"خطأ: {message}")
                    print(f"     📢 رسالة خطأ: {message}")
                
                # استبدال الدوال مؤقتاً
                messagebox.showwarning = mock_showwarning
                messagebox.showerror = mock_showerror
                
                # تشغيل البحث الفعلي
                emp_system.perform_search()
                
                # استعادة الدوال الأصلية
                messagebox.showwarning = original_showwarning
                messagebox.showerror = original_showerror
                
                if message_shown:
                    actual_result = f"❌ ظهرت رسالة: {message_shown[0]}"
                else:
                    actual_result = "✅ تم البحث بدون رسائل خطأ"
                
                print(f"     النتيجة الفعلية: {actual_result}")
                
                # مقارنة النتيجة المتوقعة مع الفعلية
                if "سيظهر تنبيه" in result and "ظهرت رسالة" in actual_result:
                    final_result = f"✅ متطابق: {description} - {actual_result}"
                elif "سيبحث" in result and "تم البحث" in actual_result:
                    final_result = f"✅ متطابق: {description} - {actual_result}"
                else:
                    final_result = f"❌ غير متطابق: {description}\n   متوقع: {result}\n   فعلي: {actual_result}"
                
                result_var.set(final_result)
                
            except Exception as e:
                error_result = f"❌ خطأ في الاختبار: {e}"
                print(f"     {error_result}")
                result_var.set(error_result)
                
        except Exception as e:
            error_msg = f"❌ خطأ في اختبار '{description}': {e}"
            print(f"   {error_msg}")
            result_var.set(error_msg)
    
    def test_current_field():
        """اختبار النص الحالي في حقل البحث"""
        current_text = emp_system.search_var.get()
        test_search_with_text(current_text, f"النص الحالي في الحقل")
    
    def clear_field():
        """مسح حقل البحث"""
        emp_system.search_var.set("")
        result_var.set("تم مسح حقل البحث")
    
    # أزرار الاختبار
    tk.Label(test_frame, text="اختبارات سريعة:", bg="#fff3cd", 
            font=("Arial", 10, "bold")).pack(side=tk.LEFT)
    
    # اختبارات مختلفة
    test_cases = [
        ("", "نص فارغ"),
        ("   ", "مسافات فقط"),
        ("أحمد", "نص عادي"),
        ("  أحمد  ", "نص مع مسافات"),
        ("أحمد   محمد", "مسافات متعددة")
    ]
    
    for text, desc in test_cases:
        btn = tk.Button(test_frame, text=desc,
                       command=lambda t=text, d=desc: test_search_with_text(t, d),
                       bg="#ffc107", fg="#212529", font=("Arial", 8))
        btn.pack(side=tk.LEFT, padx=2)
    
    # أزرار إضافية
    tk.Button(test_frame, text="اختبار النص الحالي",
             command=test_current_field,
             bg="#17a2b8", fg="white", font=("Arial", 8)).pack(side=tk.LEFT, padx=2)
    
    tk.Button(test_frame, text="مسح الحقل",
             command=clear_field,
             bg="#6c757d", fg="white", font=("Arial", 8)).pack(side=tk.LEFT, padx=2)
    
    # إطار التحليل المتقدم
    analysis_frame = tk.Frame(diag_frame, bg="#fff3cd")
    analysis_frame.pack(pady=10)
    
    def deep_analysis():
        """تحليل عميق للمشكلة"""
        print("\n🔬 تحليل عميق للمشكلة:")
        
        # فحص حالة النظام
        print("1️⃣ فحص حالة النظام:")
        print(f"   متغير البحث موجود: {hasattr(emp_system, 'search_var')}")
        if hasattr(emp_system, 'search_var'):
            print(f"   قيمة متغير البحث: '{emp_system.search_var.get()}'")
        
        print(f"   دالة البحث موجودة: {hasattr(emp_system, 'perform_search')}")
        print(f"   بيانات الموظفين: {len(emp_system.employees_data) if hasattr(emp_system, 'employees_data') else 'غير موجودة'}")
        
        # فحص منطق التنظيف
        print("\n2️⃣ فحص منطق التنظيف:")
        test_texts = ["أحمد", "  أحمد  ", "أحمد   محمد", "Ahmed", "123"]
        
        for text in test_texts:
            print(f"   النص: '{text}'")
            cleaned = text.strip()
            cleaned = ' '.join(cleaned.split())
            print(f"   بعد التنظيف: '{cleaned}' (طول: {len(cleaned)})")
            print(f"   سيمر الفحص: {len(cleaned) > 0}")
        
        # فحص الترميز
        print("\n3️⃣ فحص الترميز:")
        current_text = emp_system.search_var.get()
        print(f"   النص الحالي: '{current_text}'")
        print(f"   طول النص: {len(current_text)}")
        print(f"   رموز ASCII: {[ord(c) for c in current_text]}")
        
        result_var.set("تم إجراء التحليل العميق - راجع وحدة التحكم")
    
    def test_manual_input():
        """اختبار إدخال يدوي"""
        # إنشاء نافذة إدخال
        input_window = tk.Toplevel(root)
        input_window.title("اختبار إدخال يدوي")
        input_window.geometry("400x200")
        input_window.grab_set()
        
        tk.Label(input_window, text="أدخل النص للاختبار:", 
                font=("Arial", 12)).pack(pady=10)
        
        entry = tk.Entry(input_window, font=("Arial", 12), width=30)
        entry.pack(pady=10)
        entry.focus_set()
        
        def test_input():
            text = entry.get()
            input_window.destroy()
            test_search_with_text(text, f"إدخال يدوي: '{text}'")
        
        tk.Button(input_window, text="اختبار", command=test_input,
                 bg="#28a745", fg="white", font=("Arial", 10)).pack(pady=10)
    
    tk.Button(analysis_frame, text="🔬 تحليل عميق",
             command=deep_analysis,
             bg="#6f42c1", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    tk.Button(analysis_frame, text="✏️ اختبار إدخال يدوي",
             command=test_manual_input,
             bg="#fd7e14", fg="white", font=("Arial", 10)).pack(side=tk.LEFT, padx=5)
    
    # زر إغلاق
    tk.Button(diag_frame, text="❌ إغلاق التشخيص", 
             command=root.destroy,
             bg="#dc3545", fg="white", 
             font=("Arial", 12, "bold")).pack(pady=10)

def main():
    """الدالة الرئيسية"""
    print("🔍 تشخيص مشكلة رسالة البحث")
    print("=" * 70)
    
    diagnose_search_message_issue()

if __name__ == "__main__":
    main()
