#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار توحيد معايير الترقيات بين الواجهة الرئيسية ونظام الترقيات
Test Unified Promotion Criteria Between Main Interface and Promotion System
"""

import sys
import datetime
import os

def test_unified_promotion_criteria():
    """اختبار توحيد معايير الترقيات"""
    print("🔄 اختبار توحيد معايير الترقيات")
    print("=" * 70)
    
    test_results = {
        'total_tests': 0,
        'passed_tests': 0,
        'failed_tests': 0
    }
    
    # اختبار 1: حساب الترقيات في الواجهة الرئيسية
    print("\n🧪 اختبار 1: حساب الترقيات في الواجهة الرئيسية")
    print("-" * 50)
    
    try:
        import tkinter as tk
        from hr_system import HRSystem
        
        # إنشاء نافذة وهمية للاختبار
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # إنشاء النظام الرئيسي
        hr_sys = HRSystem(root)
        
        # حساب الترقيات المستحقة
        main_interface_count = hr_sys.count_pending_promotions()
        
        print(f"📊 الواجهة الرئيسية: {main_interface_count} موظف مستحق")
        test_results['passed_tests'] += 1
        
        root.destroy()
    
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة الرئيسية: {e}")
        test_results['failed_tests'] += 1
        main_interface_count = 0
    test_results['total_tests'] += 1
    
    # اختبار 2: حساب الترقيات في نظام الترقيات
    print("\n🧪 اختبار 2: حساب الترقيات في نظام الترقيات")
    print("-" * 50)
    
    try:
        import tkinter as tk
        from promotion_system_safe import PromotionSystem
        
        # إنشاء نافذة وهمية للاختبار
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # إنشاء نظام الترقيات
        promotion_sys = PromotionSystem(root)
        
        # الحصول على عدد المستحقين
        promotion_system_count = promotion_sys.promotion_stats.get('eligible_for_promotion', 0)
        
        print(f"📊 نظام الترقيات: {promotion_system_count} موظف مستحق")
        test_results['passed_tests'] += 1
        
        root.destroy()
    
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام الترقيات: {e}")
        test_results['failed_tests'] += 1
        promotion_system_count = 0
    test_results['total_tests'] += 1
    
    # اختبار 3: مقارنة النتائج
    print("\n🧪 اختبار 3: مقارنة النتائج")
    print("-" * 50)
    
    print(f"📊 الواجهة الرئيسية: {main_interface_count}")
    print(f"📊 نظام الترقيات: {promotion_system_count}")
    
    if main_interface_count == promotion_system_count:
        print("✅ النتائج متطابقة - المعايير موحدة")
        test_results['passed_tests'] += 1
    else:
        print(f"❌ النتائج مختلفة - فرق: {abs(main_interface_count - promotion_system_count)}")
        test_results['failed_tests'] += 1
    test_results['total_tests'] += 1
    
    # اختبار 4: فحص المعايير المستخدمة
    print("\n🧪 اختبار 4: فحص المعايير المستخدمة")
    print("-" * 50)
    
    try:
        # فحص الكود للتأكد من استخدام نفس المعايير
        with open("hr_system.py", "r", encoding="utf-8") as f:
            hr_code = f.read()
        
        with open("promotion_system_safe.py", "r", encoding="utf-8") as f:
            promotion_code = f.read()
        
        # فحص المعايير المهمة
        criteria_checks = [
            ('years_in_grade >= 4', 'معيار 4 سنوات'),
            ('"معلم" in job_title', 'استثناء المعلمين'),
            ('current_grade_num >= 15', 'الحد الأقصى للدرجة'),
            ('"تاريخ الدرجة الحالية"', 'استخدام تاريخ الدرجة الحالية')
        ]
        
        for criterion, description in criteria_checks:
            hr_has = criterion in hr_code
            promotion_has = criterion in promotion_code
            
            if hr_has and promotion_has:
                print(f"✅ {description}: موجود في كلا النظامين")
                test_results['passed_tests'] += 1
            elif hr_has or promotion_has:
                print(f"⚠️ {description}: موجود في نظام واحد فقط")
                test_results['failed_tests'] += 1
            else:
                print(f"❌ {description}: غير موجود في أي نظام")
                test_results['failed_tests'] += 1
            test_results['total_tests'] += 1
    
    except Exception as e:
        print(f"❌ خطأ في فحص المعايير: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 5: فحص تفصيلي للموظفين المستحقين
    print("\n🧪 اختبار 5: فحص تفصيلي للموظفين المستحقين")
    print("-" * 50)
    
    try:
        import tkinter as tk
        from promotion_system_safe import PromotionSystem
        
        # إنشاء نافذة وهمية للاختبار
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # إنشاء نظام الترقيات
        promotion_sys = PromotionSystem(root)
        
        # عرض تفاصيل الموظفين المستحقين
        eligible_employees = []
        for promotion in promotion_sys.promotion_list:
            if promotion["حالة الترقية"] == "مستحق للترقية":
                eligible_employees.append({
                    "name": promotion["الاسم"],
                    "emp_id": promotion["الرقم الوظيفي"],
                    "grade": promotion["الدرجة الحالية"],
                    "years": promotion["سنوات في الدرجة"]
                })
        
        print(f"📋 الموظفين المستحقين للترقية ({len(eligible_employees)}):")
        for i, emp in enumerate(eligible_employees, 1):
            print(f"   {i}. {emp['name']} ({emp['emp_id']}) - الدرجة {emp['grade']} - {emp['years']} سنة")
        
        if len(eligible_employees) > 0:
            print("✅ يوجد موظفين مستحقين للترقية")
            test_results['passed_tests'] += 1
        else:
            print("⚠️ لا يوجد موظفين مستحقين للترقية")
            test_results['passed_tests'] += 1  # هذا مقبول
        test_results['total_tests'] += 1
        
        root.destroy()
    
    except Exception as e:
        print(f"❌ خطأ في الفحص التفصيلي: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    # اختبار 6: فحص الإحصائيات الشاملة
    print("\n🧪 اختبار 6: فحص الإحصائيات الشاملة")
    print("-" * 50)
    
    try:
        import tkinter as tk
        from hr_system import HRSystem
        
        # إنشاء نافذة وهمية للاختبار
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # إنشاء النظام الرئيسي
        hr_sys = HRSystem(root)
        
        # حساب الإحصائيات الشاملة
        stats = hr_sys.calculate_dashboard_stats()
        
        print(f"📊 إحصائيات الواجهة الرئيسية:")
        print(f"   👥 إجمالي الموظفين: {stats['employees_count']}")
        print(f"   ⬆️ ترقيات مستحقة: {stats['pending_promotions']}")
        print(f"   🏖️ إجازات الشهر: {stats['leaves_this_month']}")
        print(f"   💰 متوسط الرصيد: {stats['avg_remaining_balance']}")
        
        if stats['pending_promotions'] >= 0:
            print("✅ الإحصائيات تعمل بنجاح")
            test_results['passed_tests'] += 1
        else:
            print("❌ خطأ في الإحصائيات")
            test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
        
        root.destroy()
    
    except Exception as e:
        print(f"❌ خطأ في فحص الإحصائيات: {e}")
        test_results['failed_tests'] += 1
        test_results['total_tests'] += 1
    
    return test_results, main_interface_count, promotion_system_count

def main():
    """تشغيل جميع الاختبارات"""
    print("🔄 اختبار توحيد معايير الترقيات")
    print(f"📅 التاريخ: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 إصدار Python: {sys.version}")
    print()
    
    # تشغيل الاختبارات
    results, main_count, promotion_count = test_unified_promotion_criteria()
    
    # عرض النتائج النهائية
    print("\n" + "=" * 70)
    print("📊 ملخص نتائج اختبار توحيد المعايير:")
    print(f"   📈 إجمالي الاختبارات: {results['total_tests']}")
    print(f"   ✅ نجح: {results['passed_tests']}")
    print(f"   ❌ فشل: {results['failed_tests']}")
    
    success_rate = (results['passed_tests'] / results['total_tests']) * 100 if results['total_tests'] > 0 else 0
    print(f"   📊 معدل النجاح: {success_rate:.1f}%")
    
    # عرض المقارنة النهائية
    print("\n🔍 المقارنة النهائية:")
    print(f"   📊 الواجهة الرئيسية: {main_count} موظف مستحق")
    print(f"   📊 نظام الترقيات: {promotion_count} موظف مستحق")
    
    if main_count == promotion_count:
        print("   ✅ النتائج متطابقة تماماً!")
        print("   🎉 تم توحيد المعايير بنجاح!")
    else:
        print(f"   ❌ النتائج مختلفة - فرق: {abs(main_count - promotion_count)}")
        print("   ⚠️ يحتاج مراجعة إضافية")
    
    if success_rate >= 90:
        print("\n🎉 ممتاز! معايير الترقيات موحدة بنجاح")
    elif success_rate >= 75:
        print("\n✅ جيد! المعايير موحدة مع بعض التحسينات المطلوبة")
    elif success_rate >= 50:
        print("\n⚠️ متوسط! التوحيد يحتاج إلى تحسينات إضافية")
    else:
        print("\n❌ ضعيف! المعايير تحتاج إلى مراجعة شاملة")
    
    print("=" * 70)
    
    # عرض التوصيات
    print("\n📋 التوصيات:")
    if main_count == promotion_count:
        print("✅ المعايير موحدة - لا حاجة لتغييرات")
        print("✅ النظامان يعطيان نفس النتائج")
        print("✅ يمكن الاعتماد على كلا النظامين")
    else:
        print("⚠️ يُنصح بمراجعة المعايير المستخدمة")
        print("⚠️ التأكد من استخدام نفس الحقول والشروط")
        print("⚠️ فحص معالجة الحالات الخاصة")
    
    print("\n🏁 انتهى اختبار توحيد معايير الترقيات")

if __name__ == "__main__":
    main()
